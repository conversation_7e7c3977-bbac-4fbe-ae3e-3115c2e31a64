"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    // État pour gérer la liste des expéditions\n    const [shipments, setShipments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockData.recentShipments);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Mettre à jour la liste quand les données mockées changent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShipments(mockData.recentShipments);\n    }, []);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const handleViewShipment = (shipment)=>{\n        // Créer une popup ou modal avec les détails de l'expédition\n        const details = \"\\nD\\xc9TAILS DE L'EXP\\xc9DITION\\n\\nN\\xb0 Commande: \".concat(shipment.orderNumber, \"\\nFournisseur: \").concat(shipment.supplier, \"\\nDate: \").concat(shipment.date, \"\\nStatut: \").concat(shipment.status, \"\\n\\nINFORMATIONS FINANCI\\xc8RES:\\nFOB Amount: \").concat(formatUSD(shipment.fobAmount), \"\\nLanded Cost: \").concat(formatCurrency(shipment.landedCost), \"\\nCoefficient: \").concat(shipment.coefficient.toFixed(3), \"x\\nMarge: \").concat(((shipment.coefficient - 1) * 100).toFixed(1), \"%\\n\\nR\\xc9PARTITION ESTIM\\xc9E DES CO\\xdbTS:\\n• FOB Amount: \").concat(formatCurrency(shipment.landedCost * 0.65), \"\\n• Droits de douane: \").concat(formatCurrency(shipment.landedCost * 0.15), \"\\n• Frais portuaires: \").concat(formatCurrency(shipment.landedCost * 0.08), \"\\n• Frais de transport: \").concat(formatCurrency(shipment.landedCost * 0.07), \"\\n• Autres co\\xfbts: \").concat(formatCurrency(shipment.landedCost * 0.05), \"\\n    \");\n        alert(details);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"D\\xe9tails de l'exp\\xe9dition affich\\xe9s\");\n    };\n    const handleEditShipment = (shipment)=>{\n        // Rediriger vers la page du calculateur avec les données pré-remplies\n        const editUrl = \"/calculator?edit=\".concat(shipment.orderNumber, \"&supplier=\").concat(encodeURIComponent(shipment.supplier), \"&fob=\").concat(shipment.fobAmount, \"&date=\").concat(shipment.date);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Redirection vers l'\\xe9dition de \".concat(shipment.orderNumber));\n        // Dans une vraie application, on utiliserait router.push()\n        // router.push(editUrl)\n        // Pour la démo, on simule l'ouverture\n        console.log(\"Edit URL:\", editUrl);\n        alert(\"Redirection vers l'\\xe9dition de l'exp\\xe9dition \".concat(shipment.orderNumber, \"\\n\\nURL: \").concat(editUrl));\n    };\n    const handleDeleteShipment = (shipment)=>{\n        const confirmDelete = confirm(\"\\xcates-vous s\\xfbr de vouloir supprimer l'exp\\xe9dition \".concat(shipment.orderNumber, \" ?\\n\\n\") + \"Fournisseur: \".concat(shipment.supplier, \"\\n\") + \"FOB Amount: \".concat(formatUSD(shipment.fobAmount), \"\\n\") + \"Date: \".concat(shipment.date, \"\\n\\n\") + \"Cette action est irr\\xe9versible.\");\n        if (confirmDelete) {\n            // Supprimer réellement l'expédition de la liste\n            setShipments((prevShipments)=>prevShipments.filter((s)=>s.id !== shipment.id));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Exp\\xe9dition \".concat(shipment.orderNumber, \" supprim\\xe9e avec succ\\xe8s\"));\n            // Dans une vraie application, on ferait aussi un appel API pour supprimer\n            // await deleteShipment(shipment.id)\n            console.log(\"Shipment deleted:\", shipment.orderNumber);\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"Suppression annul\\xe9e\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Configuration de base\n        const currency = \"USD\" // Dans une vraie app, ceci viendrait des données\n        ;\n        const exchangeRate = 134.50000 // Taux de change vers DZD\n        ;\n        const invoiceNumber = \"INV-2024-001\" // Simulé\n        ;\n        // Calculs de base\n        const fobAmount = shipment.fobAmount;\n        const freight = fobAmount * 0.15 // 15% du FOB\n        ;\n        const totalCif = fobAmount + freight;\n        // Conversions automatiques en DZD\n        const fobAmountDzd = fobAmount * exchangeRate;\n        const freightDzd = freight * exchangeRate;\n        const totalCifDzd = totalCif * exchangeRate;\n        // Valeurs simulées pour les frais (dans une vraie app, ces valeurs viendraient des champs de saisie)\n        // CUSTOMS DUTIES\n        const customsDuties1Ttc = 850000;\n        const customsDuties1Tva = 136000;\n        const customsDuties1Ht = customsDuties1Ttc - customsDuties1Tva;\n        const customsDuties2Ttc = 650000;\n        const customsDuties2Tva = 104000;\n        const customsDuties2Ht = customsDuties2Ttc - customsDuties2Tva;\n        const customsDutiesTotalTtc = customsDuties1Ttc + customsDuties2Ttc;\n        const customsDutiesTotalTva = customsDuties1Tva + customsDuties2Tva;\n        const customsDutiesTotalHt = customsDuties1Ht + customsDuties2Ht;\n        // PORT FEES\n        const importDeliveryTtc = 482344;\n        const importDeliveryTva = 77175;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406;\n        const customsInspectionTva = 46305;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalTva = importDeliveryTva + customsInspectionTva;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES\n        const shippingAgencyTtc = 675281;\n        const shippingAgencyTva = 108045;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875;\n        const emptyContainersTva = 61740;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // HT seulement\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalTva = shippingAgencyTva + emptyContainersTva;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES\n        const miscExpensesTtc = 482344;\n        const miscExpensesTva = 77175;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES\n        const transitExpensesTtc = 385875;\n        const transitExpensesTva = 61740;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs finaux\n        const landedCostTtc = totalCifDzd + customsDutiesTotalTtc + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + customsDutiesTotalHt + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const landedCostCoefficient = landedCostHt / fobAmountDzd;\n        const shipmentData = [\n            // En-tête\n            [\n                \"\",\n                \"\",\n                \"Valeur DZD\",\n                \"\\xc9quivalent \".concat(currency)\n            ],\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"---------------------------------------------\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INVOICE #:\",\n                invoiceNumber\n            ],\n            [\n                \"Exchange Rate Used : DZD/\" + currency + \":\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"\"\n            ],\n            // Goods Price in Currency\n            [\n                \"Goods Price in Currency (\" + currency + \"):\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT:\",\n                fobAmount.toLocaleString(\"fr-FR\"),\n                fobAmount.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT:\",\n                freight.toLocaleString(\"fr-FR\"),\n                freight.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF (FOB + Freight):\",\n                totalCif.toLocaleString(\"fr-FR\"),\n                totalCif.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            // Conversion to DZD\n            [\n                \"Conversion to DZD:\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT DZD (Automatic conversion):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\"),\n                (fobAmountDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT DZD (Automatic conversion):\",\n                freightDzd.toLocaleString(\"fr-FR\"),\n                (freightDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):\",\n                totalCifDzd.toLocaleString(\"fr-FR\"),\n                (totalCifDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"_____________________________________________________________________________________\"\n            ],\n            // Customs Duties\n            [\n                \"Customs Duties:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (ALGERIA CUSTOMS):\"\n            ],\n            [\n                \"\",\n                \"D3 N#:\",\n                \"D3-2024-001\"\n            ],\n            [\n                \"\",\n                \"D3 Date:\",\n                \"15/01/2024\"\n            ],\n            [\n                \"\",\n                \"QUITTANCE 1:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD Total TTC:\",\n                customsDuties1Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD TVA:\",\n                customsDuties1Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties1Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD HT (TTC - TVA):\",\n                customsDuties1Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"QUITTANCE 2:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD Total TTC:\",\n                customsDuties2Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD TVA:\",\n                customsDuties2Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties2Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD HT (TTC - TVA):\",\n                customsDuties2Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD Total TTC:',\n                customsDutiesTotalTtc.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD TVA:',\n                customsDutiesTotalTva.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD HT (TTC - TVA):',\n                customsDutiesTotalHt.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------------\"\n            ],\n            // Port Fees\n            [\n                \"PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (IMPORT DELIVERY):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE N#:\",\n                \"IMP-DEL-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY Total TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\"),\n                (importDeliveryTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\"),\n                (importDeliveryTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY HT (TTC - TVA):\",\n                importDeliveryHt.toLocaleString(\"fr-FR\"),\n                (importDeliveryHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (CUSTOMS INSPECTION):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE N#:\",\n                \"CUST-INS-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION Total TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\"),\n                (customsInspectionTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\"),\n                (customsInspectionTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION HT (TTC - TVA):\",\n                customsInspectionHt.toLocaleString(\"fr-FR\"),\n                (customsInspectionHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD Total TTC:',\n                portFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD TVA:',\n                portFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                portFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (portFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"---------------------------------------------------------------------------------------------------------------\"\n            ],\n            // Shipping Company Fees\n            [\n                \"SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE N#:\",\n                \"SHIP-AGE-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE DATE:\",\n                \"17/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES Total TTC (DZD):\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES TVA (DZD):\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\"),\n                (shippingAgencyHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (EMPTY CONTAINERS):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE N#:\",\n                \"EMPTY-CONT-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE DATE:\",\n                \"18/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN Total TTC (DZD):\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\"),\n                (emptyContainersTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN TVA (DZD):\",\n                emptyContainersTva.toLocaleString(\"fr-FR\"),\n                (emptyContainersTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA):\",\n                emptyContainersHt.toLocaleString(\"fr-FR\"),\n                (emptyContainersHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE N#:\",\n                \"DEMUR-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE DATE:\",\n                \"19/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE HT (DZD) (This currency field must be entered manually.):\",\n                demurrageHt.toLocaleString(\"fr-FR\"),\n                (demurrageHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC:',\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA:',\n                shippingFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"-------------------------------------------------------\"\n            ],\n            // Other Miscellaneous Expenses\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE N#:\",\n                \"MISC-EXP-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE:\",\n                \"20/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TTC (DZD):\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\"),\n                (miscExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TVA (DZD):\",\n                miscExpensesTva.toLocaleString(\"fr-FR\"),\n                (miscExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):\",\n                miscExpensesHt.toLocaleString(\"fr-FR\"),\n                (miscExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------\"\n            ],\n            // Transit Services Expenses\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE N#:\",\n                \"TRANS-SERV-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE DATE:\",\n                \"21/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TTC (DZD):\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\"),\n                (transitExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TVA (DZD):\",\n                transitExpensesTva.toLocaleString(\"fr-FR\"),\n                (transitExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):\",\n                transitExpensesHt.toLocaleString(\"fr-FR\"),\n                (transitExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\"\n            ],\n            // Calculs finaux\n            [\n                \"CALCULS FINAUX:\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostHt / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES HT + OTHER MISCELLANEOUS EXPENSES HT + SHIPPING COMPANY FEES Overall Totals HT + PORT FEES Overall Totals HT + CUSTOMS DUTIES Overall Totals HT + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostTtc / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES TTC + OTHER MISCELLANEOUS EXPENSES TTC + SHIPPING COMPANY FEES Overall Totals TTC + PORT FEES Overall Totals TTC + CUSTOMS DUTIES Overall Totals TTC + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed cost coefficient:\",\n                landedCostCoefficient.toFixed(5),\n                landedCostCoefficient.toFixed(5)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 553,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 552,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 723,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleViewShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Voir les d\\xe9tails\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Eye, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleEditShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Edit, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleDeleteShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:border-red-300\",\n                                                                        title: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Trash2, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                        className: \"h-8 px-2\",\n                                                                        title: \"Exporter en Excel\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Export\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 842,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 566,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 565,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"vi0zUBlEbKpvy4OAJiMYSrdG9SE=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});