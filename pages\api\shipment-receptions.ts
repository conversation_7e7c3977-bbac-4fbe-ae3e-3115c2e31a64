import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth/next'
import { authOptions } from './auth/[...nextauth]'
import fs from 'fs'
import path from 'path'

// Fallback vers un système de fichiers JSON si SQLite ne fonctionne pas
const dataDir = path.join(process.cwd(), 'data')
const receptionsFile = path.join(dataDir, 'receptions.json')

// Créer le dossier data s'il n'existe pas
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}

// Fonctions pour gérer les données JSON
const loadReceptions = () => {
  try {
    if (fs.existsSync(receptionsFile)) {
      const data = fs.readFileSync(receptionsFile, 'utf8')
      return JSON.parse(data)
    }
    return []
  } catch (error) {
    console.error('Error loading receptions:', error)
    return []
  }
}

const saveReceptions = (receptions: any[]) => {
  try {
    fs.writeFileSync(receptionsFile, JSON.stringify(receptions, null, 2))
    return true
  } catch (error) {
    console.error('Error saving receptions:', error)
    return false
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions)

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  try {
    switch (req.method) {
      case 'GET':
        const receptions = loadReceptions()

        // Récupérer toutes les réceptions ou une réception spécifique
        if (req.query.id) {
          const reception = receptions.find((r: any) => r.id === req.query.id)
          if (!reception) {
            return res.status(404).json({ error: 'Reception not found' })
          }
          return res.status(200).json(reception)
        } else if (req.query.invoiceNumber) {
          const reception = receptions.find((r: any) => r.invoice_number === req.query.invoiceNumber)
          if (!reception) {
            return res.status(404).json({ error: 'Reception not found' })
          }
          return res.status(200).json(reception)
        } else if (req.query.search) {
          // Recherche avancée
          const searchTerm = (req.query.search as string).toLowerCase()
          const filteredReceptions = receptions.filter((r: any) =>
            r.supplier_name?.toLowerCase().includes(searchTerm) ||
            r.invoice_number?.toLowerCase().includes(searchTerm) ||
            r.order_number?.toLowerCase().includes(searchTerm)
          )
          return res.status(200).json(filteredReceptions)
        } else if (req.query.supplier) {
          // Filtrer par fournisseur
          const filteredReceptions = receptions.filter((r: any) => r.supplier_name === req.query.supplier)
          return res.status(200).json(filteredReceptions)
        } else if (req.query.startDate && req.query.endDate) {
          // Filtrer par période
          const filteredReceptions = receptions.filter((r: any) =>
            r.created_at >= req.query.startDate && r.created_at <= req.query.endDate
          )
          return res.status(200).json(filteredReceptions)
        } else if (req.query.stats === 'true') {
          // Récupérer les statistiques
          const totalReceptions = receptions.length
          const uniqueSuppliers = [...new Set(receptions.map((r: any) => r.supplier_name))].filter(Boolean)
          const uniqueCurrencies = [...new Set(receptions.map((r: any) => r.currency))].filter(Boolean)
          const totalFobAmount = receptions.reduce((sum: number, r: any) => sum + (r.fob_amount || 0), 0)
          const avgFobAmount = totalReceptions > 0 ? totalFobAmount / totalReceptions : 0
          const firstReception = receptions.length > 0 ? receptions[receptions.length - 1]?.created_at : null
          const lastReception = receptions.length > 0 ? receptions[0]?.created_at : null

          return res.status(200).json({
            total_receptions: totalReceptions,
            unique_suppliers: uniqueSuppliers.length,
            currencies_used: uniqueCurrencies.length,
            avg_fob_amount: avgFobAmount,
            total_fob_amount: totalFobAmount,
            first_reception: firstReception,
            last_reception: lastReception,
            suppliers: uniqueSuppliers,
            currencies: uniqueCurrencies
          })
        } else {
          return res.status(200).json(receptions)
        }

      case 'POST':
        // Créer une nouvelle réception
        const newReception = req.body
        const receptions = loadReceptions()

        // Générer un ID unique
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)

        // Créer l'objet réception avec tous les champs
        const receptionData = {
          id,
          // Arrival Notice
          voyage_number: newReception.voyageNumber || null,
          bill_of_lading: newReception.billOfLading || null,
          vessel_name: newReception.vesselName || null,
          shipowner: newReception.shipowner || null,
          actual_time_of_arrival: newReception.actualTimeOfArrival || null,

          // General Information
          exchange_rate: newReception.exchangeRate || 0,
          currency: newReception.currency || 'USD',
          supplier_name: newReception.supplierName || '',
          shipment_type: newReception.shipmentType || null,
          order_number: newReception.orderNumber || '',
          date_of_order_number: newReception.dateOfOrderNumber || null,
          invoice_number: newReception.invoiceNumber || '',
          invoice_date: newReception.invoiceDate || null,
          from_location: newReception.fromLocation || null,
          operation_type: newReception.operationType || null,
          description_of_goods: newReception.descriptionOfGoods || null,

          // Banking Information
          bank_name: newReception.bankName || null,
          bank_address: newReception.bankAddress || null,
          swift_code: newReception.swiftCode || null,
          account_number: newReception.accountNumber || null,
          beneficiary_name: newReception.beneficiaryName || null,
          beneficiary_address: newReception.beneficiaryAddress || null,

          // Quantities and Container Information
          total_quantity: newReception.totalQuantity || null,
          unit_of_measure: newReception.unitOfMeasure || null,
          total_weight_kg: newReception.totalWeightKg || null,
          total_volume_m3: newReception.totalVolumeM3 || null,
          number_of_containers: newReception.numberOfContainers || null,
          container_type: newReception.containerType || null,
          container_numbers: newReception.containerNumbers || null,

          // Goods Price
          fob_amount: newReception.fobAmount || 0,
          freight: newReception.freight || 0,
          cif_amount_currency: newReception.cifAmountCurrency || 0,
          fob_amount_dzd: newReception.fobAmountDzd || 0,
          freight_dzd: newReception.freightDzd || 0,
          cif_amount_dzd: newReception.cifAmountDzd || 0,

          // Customs Duties
          d3_number: newReception.d3Number || null,
          d3_date: newReception.d3Date || null,
          customs_duties_1_ttc: newReception.customsDuties1Ttc || 0,
          customs_duties_1_tva: newReception.customsDuties1Tva || 0,
          customs_duties_2_ht: newReception.customsDuties2Ht || 0,

          // Port Fees
          import_delivery_invoice_number: newReception.importDeliveryInvoiceNumber || null,
          import_delivery_invoice_date: newReception.importDeliveryInvoiceDate || null,
          import_delivery_ttc: newReception.importDeliveryTtc || 0,
          import_delivery_tva: newReception.importDeliveryTva || 0,
          customs_inspection_invoice_number: newReception.customsInspectionInvoiceNumber || null,
          customs_inspection_invoice_date: newReception.customsInspectionInvoiceDate || null,
          customs_inspection_ttc: newReception.customsInspectionTtc || 0,
          customs_inspection_tva: newReception.customsInspectionTva || 0,

          // Shipping Company Fees
          shipping_agency_invoice_number: newReception.shippingAgencyInvoiceNumber || null,
          shipping_agency_invoice_date: newReception.shippingAgencyInvoiceDate || null,
          shipping_agency_ttc: newReception.shippingAgencyTtc || 0,
          shipping_agency_tva: newReception.shippingAgencyTva || 0,
          empty_containers_invoice_number: newReception.emptyContainersInvoiceNumber || null,
          empty_containers_invoice_date: newReception.emptyContainersInvoiceDate || null,
          empty_containers_ttc: newReception.emptyContainersTtc || 0,
          empty_containers_tva: newReception.emptyContainersTva || 0,
          demurrage_invoice_number: newReception.demurrageInvoiceNumber || null,
          demurrage_invoice_date: newReception.demurrageInvoiceDate || null,
          demurrage_ht: newReception.demurrageHt || 0,

          // Miscellaneous Expenses
          misc_expenses_invoice_number: newReception.miscExpensesInvoiceNumber || null,
          misc_expenses_invoice_date: newReception.miscExpensesInvoiceDate || null,
          misc_expenses_ttc: newReception.miscExpensesTtc || 0,
          misc_expenses_tva: newReception.miscExpensesTva || 0,

          // Transit Services
          transit_services_invoice_number: newReception.transitServicesInvoiceNumber || null,
          transit_services_invoice_date: newReception.transitServicesInvoiceDate || null,
          transit_services_ttc: newReception.transitServicesTtc || 0,
          transit_services_tva: newReception.transitServicesTva || 0,

          // Metadata
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: null,
          created_by: session.user?.email || 'unknown'
        }

        // Ajouter la nouvelle réception au début du tableau (plus récent en premier)
        receptions.unshift(receptionData)

        // Sauvegarder dans le fichier JSON
        const saved = saveReceptions(receptions)

        if (!saved) {
          return res.status(500).json({ error: 'Failed to save reception' })
        }

        return res.status(201).json({
          id,
          message: 'Reception created successfully',
          data: receptionData
        })

      case 'PUT':
        // Mettre à jour une réception existante
        if (!req.query.id) {
          return res.status(400).json({ error: 'Reception ID is required' })
        }

        const updateData = req.body
        const receptionsToUpdate = loadReceptions()
        const receptionIndex = receptionsToUpdate.findIndex((r: any) => r.id === req.query.id)

        if (receptionIndex === -1) {
          return res.status(404).json({ error: 'Reception not found' })
        }

        // Mettre à jour la réception
        receptionsToUpdate[receptionIndex] = {
          ...receptionsToUpdate[receptionIndex],
          ...updateData,
          updated_at: new Date().toISOString()
        }

        const saved = saveReceptions(receptionsToUpdate)

        if (!saved) {
          return res.status(500).json({ error: 'Failed to update reception' })
        }

        return res.status(200).json({ message: 'Reception updated successfully' })

      case 'DELETE':
        // Supprimer une réception
        if (!req.query.id) {
          return res.status(400).json({ error: 'Reception ID is required' })
        }

        const receptions = loadReceptions()
        const receptionIndex = receptions.findIndex((r: any) => r.id === req.query.id)

        if (receptionIndex === -1) {
          return res.status(404).json({ error: 'Reception not found' })
        }

        // Supprimer la réception
        receptions.splice(receptionIndex, 1)

        const saved = saveReceptions(receptions)

        if (!saved) {
          return res.status(500).json({ error: 'Failed to delete reception' })
        }

        return res.status(200).json({ message: 'Reception deleted successfully' })

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
        return res.status(405).json({ error: `Method ${req.method} not allowed` })
    }
  } catch (error) {
    console.error('Database error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
