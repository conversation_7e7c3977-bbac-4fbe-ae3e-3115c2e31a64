import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth/next'
import { authOptions } from './auth/[...nextauth]'
import { shipmentReceptions } from '@/lib/database'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions)

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  try {
    switch (req.method) {
      case 'GET':
        // Récupérer toutes les réceptions ou une réception spécifique
        if (req.query.id) {
          const reception = shipmentReceptions.getById.get(req.query.id as string)
          if (!reception) {
            return res.status(404).json({ error: 'Reception not found' })
          }
          return res.status(200).json(reception)
        } else if (req.query.invoiceNumber) {
          const reception = shipmentReceptions.getByInvoiceNumber.get(req.query.invoiceNumber as string)
          if (!reception) {
            return res.status(404).json({ error: 'Reception not found' })
          }
          return res.status(200).json(reception)
        } else {
          const receptions = shipmentReceptions.getAll.all()
          return res.status(200).json(receptions)
        }

      case 'POST':
        // Créer une nouvelle réception
        const newReception = req.body
        
        // Générer un ID unique
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
        
        // Préparer les données pour l'insertion
        const receptionData = [
          id,
          newReception.voyageNumber || null,
          newReception.billOfLading || null,
          newReception.vesselName || null,
          newReception.shipowner || null,
          newReception.actualTimeOfArrival || null,
          newReception.exchangeRate || 0,
          newReception.currency || 'USD',
          newReception.supplierName || '',
          newReception.shipmentType || null,
          newReception.orderNumber || '',
          newReception.dateOfOrderNumber || null,
          newReception.invoiceNumber || '',
          newReception.invoiceDate || null,
          newReception.fromLocation || null,
          newReception.operationType || null,
          newReception.descriptionOfGoods || null,
          newReception.bankName || null,
          newReception.bankAddress || null,
          newReception.swiftCode || null,
          newReception.accountNumber || null,
          newReception.beneficiaryName || null,
          newReception.beneficiaryAddress || null,
          newReception.totalQuantity || null,
          newReception.unitOfMeasure || null,
          newReception.totalWeightKg || null,
          newReception.totalVolumeM3 || null,
          newReception.numberOfContainers || null,
          newReception.containerType || null,
          newReception.containerNumbers || null,
          newReception.fobAmount || 0,
          newReception.freight || 0,
          newReception.cifAmountCurrency || 0,
          newReception.fobAmountDzd || 0,
          newReception.freightDzd || 0,
          newReception.cifAmountDzd || 0,
          newReception.d3Number || null,
          newReception.d3Date || null,
          newReception.customsDuties1Ttc || 0,
          newReception.customsDuties1Tva || 0,
          newReception.customsDuties2Ht || 0,
          newReception.importDeliveryInvoiceNumber || null,
          newReception.importDeliveryInvoiceDate || null,
          newReception.importDeliveryTtc || 0,
          newReception.importDeliveryTva || 0,
          newReception.customsInspectionInvoiceNumber || null,
          newReception.customsInspectionInvoiceDate || null,
          newReception.customsInspectionTtc || 0,
          newReception.customsInspectionTva || 0,
          newReception.shippingAgencyInvoiceNumber || null,
          newReception.shippingAgencyInvoiceDate || null,
          newReception.shippingAgencyTtc || 0,
          newReception.shippingAgencyTva || 0,
          newReception.emptyContainersInvoiceNumber || null,
          newReception.emptyContainersInvoiceDate || null,
          newReception.emptyContainersTtc || 0,
          newReception.emptyContainersTva || 0,
          newReception.demurrageInvoiceNumber || null,
          newReception.demurrageInvoiceDate || null,
          newReception.demurrageHt || 0,
          newReception.miscExpensesInvoiceNumber || null,
          newReception.miscExpensesInvoiceDate || null,
          newReception.miscExpensesTtc || 0,
          newReception.miscExpensesTva || 0,
          newReception.transitServicesInvoiceNumber || null,
          newReception.transitServicesInvoiceDate || null,
          newReception.transitServicesTtc || 0,
          newReception.transitServicesTva || 0,
          'completed',
          new Date().toISOString(),
          session.user?.email || 'unknown'
        ]

        const result = shipmentReceptions.create.run(...receptionData)
        
        return res.status(201).json({ 
          id, 
          message: 'Reception created successfully',
          insertId: result.lastInsertRowid 
        })

      case 'PUT':
        // Mettre à jour une réception existante
        if (!req.query.id) {
          return res.status(400).json({ error: 'Reception ID is required' })
        }

        const updateData = req.body
        const updateParams = [
          updateData.voyageNumber || null,
          updateData.billOfLading || null,
          updateData.vesselName || null,
          updateData.shipowner || null,
          updateData.actualTimeOfArrival || null,
          updateData.exchangeRate || 0,
          updateData.currency || 'USD',
          updateData.supplierName || '',
          updateData.shipmentType || null,
          updateData.orderNumber || '',
          updateData.dateOfOrderNumber || null,
          updateData.invoiceNumber || '',
          updateData.invoiceDate || null,
          updateData.fromLocation || null,
          updateData.operationType || null,
          updateData.descriptionOfGoods || null,
          updateData.bankName || null,
          updateData.bankAddress || null,
          updateData.swiftCode || null,
          updateData.accountNumber || null,
          updateData.beneficiaryName || null,
          updateData.beneficiaryAddress || null,
          updateData.totalQuantity || null,
          updateData.unitOfMeasure || null,
          updateData.totalWeightKg || null,
          updateData.totalVolumeM3 || null,
          updateData.numberOfContainers || null,
          updateData.containerType || null,
          updateData.containerNumbers || null,
          updateData.fobAmount || 0,
          updateData.freight || 0,
          updateData.cifAmountCurrency || 0,
          updateData.fobAmountDzd || 0,
          updateData.freightDzd || 0,
          updateData.cifAmountDzd || 0,
          updateData.d3Number || null,
          updateData.d3Date || null,
          updateData.customsDuties1Ttc || 0,
          updateData.customsDuties1Tva || 0,
          updateData.customsDuties2Ht || 0,
          updateData.importDeliveryInvoiceNumber || null,
          updateData.importDeliveryInvoiceDate || null,
          updateData.importDeliveryTtc || 0,
          updateData.importDeliveryTva || 0,
          updateData.customsInspectionInvoiceNumber || null,
          updateData.customsInspectionInvoiceDate || null,
          updateData.customsInspectionTtc || 0,
          updateData.customsInspectionTva || 0,
          updateData.shippingAgencyInvoiceNumber || null,
          updateData.shippingAgencyInvoiceDate || null,
          updateData.shippingAgencyTtc || 0,
          updateData.shippingAgencyTva || 0,
          updateData.emptyContainersInvoiceNumber || null,
          updateData.emptyContainersInvoiceDate || null,
          updateData.emptyContainersTtc || 0,
          updateData.emptyContainersTva || 0,
          updateData.demurrageInvoiceNumber || null,
          updateData.demurrageInvoiceDate || null,
          updateData.demurrageHt || 0,
          updateData.miscExpensesInvoiceNumber || null,
          updateData.miscExpensesInvoiceDate || null,
          updateData.miscExpensesTtc || 0,
          updateData.miscExpensesTva || 0,
          updateData.transitServicesInvoiceNumber || null,
          updateData.transitServicesInvoiceDate || null,
          updateData.transitServicesTtc || 0,
          updateData.transitServicesTva || 0,
          new Date().toISOString(),
          req.query.id as string
        ]

        const updateResult = shipmentReceptions.update.run(...updateParams)
        
        if (updateResult.changes === 0) {
          return res.status(404).json({ error: 'Reception not found' })
        }

        return res.status(200).json({ message: 'Reception updated successfully' })

      case 'DELETE':
        // Supprimer une réception
        if (!req.query.id) {
          return res.status(400).json({ error: 'Reception ID is required' })
        }

        const deleteResult = shipmentReceptions.delete.run(req.query.id as string)
        
        if (deleteResult.changes === 0) {
          return res.status(404).json({ error: 'Reception not found' })
        }

        return res.status(200).json({ message: 'Reception deleted successfully' })

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
        return res.status(405).json({ error: `Method ${req.method} not allowed` })
    }
  } catch (error) {
    console.error('Database error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
