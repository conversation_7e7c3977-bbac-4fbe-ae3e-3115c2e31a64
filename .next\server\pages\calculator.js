/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/calculator";
exports.ids = ["pages/calculator"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: () => (/* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Edit: () => (/* reexport safe */ _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   RefreshCw: () => (/* reexport safe */ _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Save: () => (/* reexport safe */ _icons_save_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/download.js */ \"./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/pen-square.js */ \"./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/refresh-cw.js */ \"./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _icons_save_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/save.js */ \"./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/search.js */ \"./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxDYWxjdWxhdG9yLENoZWNrQ2lyY2xlLERvd25sb2FkLEVkaXQsRXllLEZpbGVUZXh0LFJlZnJlc2hDdyxTYXZlLFNlYXJjaCxUcmFzaDIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0g7QUFDRztBQUNQO0FBQ0Y7QUFDUjtBQUNXO0FBQ0U7QUFDWDtBQUNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZjFiMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxlcnRDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9hbGVydC1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxjdWxhdG9yIH0gZnJvbSBcIi4vaWNvbnMvY2FsY3VsYXRvci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2stY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG93bmxvYWQgfSBmcm9tIFwiLi9pY29ucy9kb3dubG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEVkaXQgfSBmcm9tIFwiLi9pY29ucy9wZW4tc3F1YXJlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllIH0gZnJvbSBcIi4vaWNvbnMvZXllLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVRleHQgfSBmcm9tIFwiLi9pY29ucy9maWxlLXRleHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBSZWZyZXNoQ3cgfSBmcm9tIFwiLi9pY29ucy9yZWZyZXNoLWN3LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2F2ZSB9IGZyb20gXCIuL2ljb25zL3NhdmUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   FileSpreadsheet: () => (/* reexport safe */ _icons_file_spreadsheet_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Ship: () => (/* reexport safe */ _icons_ship_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_file_spreadsheet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/file-spreadsheet.js */ \"./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_ship_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/ship.js */ \"./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsY3VsYXRvcixGaWxlU3ByZWFkc2hlZXQsRmlsZVRleHQsTG9nT3V0LE1lbnUsUGFja2FnZSxTZXR0aW5ncyxTaGlwLFVzZXIsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDQTtBQUNXO0FBQ2Q7QUFDSjtBQUNMO0FBQ007QUFDRTtBQUNSO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbXBvcnQtbG9naXN0aWNzLWNhbGN1bGF0b3IvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9hMjFlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJDaGFydDMgfSBmcm9tIFwiLi9pY29ucy9iYXItY2hhcnQtMy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGN1bGF0b3IgfSBmcm9tIFwiLi9pY29ucy9jYWxjdWxhdG9yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVNwcmVhZHNoZWV0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS1zcHJlYWRzaGVldC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpcCB9IGZyb20gXCIuL2ljb25zL3NoaXAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\calculator.tsx */ \"./pages/calculator.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/calculator\",\n        pathname: \"/calculator\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst getNavigation = (userRole)=>{\n    const baseNavigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.BarChart3\n        },\n        {\n            name: \"Shipment Reception\",\n            href: \"/shipment-reception\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Ship\n        },\n        {\n            name: \"Cost Calculator\",\n            href: \"/calculator\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator\n        },\n        {\n            name: \"Cost Distribution\",\n            href: \"/cost-distribution\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileSpreadsheet\n        },\n        {\n            name: \"Order Management\",\n            href: \"/orders\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Package\n        },\n        {\n            name: \"Reports\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileText\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings\n        }\n    ];\n    // Add admin-only navigation for super admin\n    if (userRole === \"super_admin\") {\n        baseNavigation.splice(-1, 0, {\n            name: \"User Management\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User\n        });\n    }\n    return baseNavigation;\n};\nfunction Layout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [sidebarOpen, setSidebarOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const navigation = getNavigation(session?.user?.role);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-0 z-50 lg:hidden\", sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-16 items-center justify-between px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Import Calculator\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 space-y-1 px-2 py-4\",\n                                children: navigation.map((item)=>{\n                                    const isActive = router.pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center flex-shrink-0 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator, {\n                                    className: \"h-8 w-8 text-primary mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Import Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-8 flex-1 space-y-1 px-2\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 flex-shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Menu, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: session.user?.name || session.user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        session.user?.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800\",\n                                                            children: session.user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                    className: \"text-gray-500 hover:text-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Layout.tsx\n");

/***/ }),

/***/ "./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/button.tsx\n");

/***/ }),

/***/ "./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/card.tsx\n");

/***/ }),

/***/ "./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/input.tsx\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4ade80\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFFb0I7QUFDUjtBQTZCMUIsU0FBU0UsSUFBSSxFQUMxQkMsU0FBUyxFQUNUQyxXQUFXLEVBQUVDLE9BQU8sRUFBRSxHQUFHRCxXQUFXLEVBQzNCO0lBQ1QscUJBQ0UsOERBQUNKLDREQUFlQTtRQUFDSyxTQUFTQTs7MEJBQ3hCLDhEQUFDRjtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7MEJBQ3hCLDhEQUFDSCxvREFBT0E7Z0JBQ05LLFVBQVM7Z0JBQ1RDLGNBQWM7b0JBQ1pDLFVBQVU7b0JBQ1ZDLE9BQU87d0JBQ0xDLFlBQVk7d0JBQ1pDLE9BQU87b0JBQ1Q7b0JBQ0FDLFNBQVM7d0JBQ1BKLFVBQVU7d0JBQ1ZLLFdBQVc7NEJBQ1RDLFNBQVM7NEJBQ1RDLFdBQVc7d0JBQ2I7b0JBQ0Y7b0JBQ0FDLE9BQU87d0JBQ0xSLFVBQVU7d0JBQ1ZLLFdBQVc7NEJBQ1RDLFNBQVM7NEJBQ1RDLFdBQVc7d0JBQ2I7b0JBQ0Y7Z0JBQ0Y7Ozs7Ozs7Ozs7OztBQUlSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ0Avc3R5bGVzL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCdcblxuZGVjbGFyZSBtb2R1bGUgJ25leHQtYXV0aCcge1xuICBpbnRlcmZhY2UgU2Vzc2lvbiB7XG4gICAgdXNlcjoge1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgZW1haWw6IHN0cmluZ1xuICAgICAgbmFtZT86IHN0cmluZyB8IG51bGxcbiAgICAgIHJvbGU/OiBzdHJpbmdcbiAgICAgIGNvbXBhbnk/OiBzdHJpbmdcbiAgICB9XG4gIH1cblxuICBpbnRlcmZhY2UgVXNlciB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBuYW1lPzogc3RyaW5nIHwgbnVsbFxuICAgIHJvbGU/OiBzdHJpbmdcbiAgICBjb21wYW55Pzogc3RyaW5nXG4gIH1cbn1cblxuZGVjbGFyZSBtb2R1bGUgJ25leHQtYXV0aC9qd3QnIHtcbiAgaW50ZXJmYWNlIEpXVCB7XG4gICAgcm9sZT86IHN0cmluZ1xuICAgIGNvbXBhbnk/OiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoe1xuICBDb21wb25lbnQsXG4gIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSxcbn06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDxUb2FzdGVyXG4gICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjMzYzNjM2JyxcbiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgfSxcbiAgICAgICAgICBzdWNjZXNzOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogMzAwMCxcbiAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICBwcmltYXJ5OiAnIzRhZGU4MCcsXG4gICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogNTAwMCxcbiAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICBwcmltYXJ5OiAnI2VmNDQ0NCcsXG4gICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlRvYXN0ZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Calculator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = async ()=>{\n        try {\n            // Charger depuis la base de données via l'API\n            const response = await fetch(\"/api/shipment-receptions\");\n            if (!response.ok) {\n                throw new Error(\"Erreur lors du chargement des r\\xe9ceptions\");\n            }\n            const savedReceptions = await response.json();\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplier_name,\n                    orderNumber: reception.order_number,\n                    invoiceNumber: reception.invoice_number,\n                    exchangeRate: reception.exchange_rate,\n                    currency: reception.currency,\n                    fobAmount: reception.fob_amount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customs_duties_1_ttc,\n                    customsDuties1Tva: reception.customs_duties_1_tva,\n                    customsDuties1Ht: reception.customs_duties_1_ttc - reception.customs_duties_1_tva,\n                    customsDuties2Ht: reception.customs_duties_2_ht,\n                    importDeliveryTtc: reception.import_delivery_ttc,\n                    importDeliveryTva: reception.import_delivery_tva,\n                    importDeliveryHt: reception.import_delivery_ttc - reception.import_delivery_tva,\n                    customsInspectionTtc: reception.customs_inspection_ttc,\n                    customsInspectionTva: reception.customs_inspection_tva,\n                    customsInspectionHt: reception.customs_inspection_ttc - reception.customs_inspection_tva,\n                    shippingAgencyTtc: reception.shipping_agency_ttc,\n                    shippingAgencyTva: reception.shipping_agency_tva,\n                    shippingAgencyHt: reception.shipping_agency_ttc - reception.shipping_agency_tva,\n                    emptyContainersTtc: reception.empty_containers_ttc,\n                    emptyContainersTva: reception.empty_containers_tva,\n                    emptyContainersHt: reception.empty_containers_ttc - reception.empty_containers_tva,\n                    demurrageHt: reception.demurrage_ht,\n                    miscExpensesTtc: reception.misc_expenses_ttc,\n                    miscExpensesTva: reception.misc_expenses_tva,\n                    miscExpensesHt: reception.misc_expenses_ttc - reception.misc_expenses_tva,\n                    transitServicesTtc: reception.transit_services_ttc,\n                    transitServicesTva: reception.transit_services_tva,\n                    transitServicesHt: reception.transit_services_ttc - reception.transit_services_tva,\n                    createdAt: reception.created_at\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n            setError(\"Erreur lors du chargement des r\\xe9ceptions depuis la base de donn\\xe9es\");\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans la base de données via l'API\n            const response = await fetch(`/api/shipment-receptions?invoiceNumber=${encodeURIComponent(invoiceNumber.trim())}`);\n            if (!response.ok) {\n                if (response.status === 404) {\n                    setError(`Aucune réception trouvée pour la facture: ${invoiceNumber}`);\n                } else {\n                    throw new Error(\"Erreur lors de la recherche\");\n                }\n                setIsLoading(false);\n                return;\n            }\n            const reception = await response.json();\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplier_name,\n                orderNumber: reception.order_number,\n                invoiceNumber: reception.invoice_number,\n                exchangeRate: reception.exchange_rate,\n                currency: reception.currency,\n                fobAmount: reception.fob_amount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customs_duties_1_ttc,\n                customsDuties1Tva: reception.customs_duties_1_tva,\n                customsDuties1Ht: reception.customs_duties_1_ttc - reception.customs_duties_1_tva,\n                customsDuties2Ht: reception.customs_duties_2_ht,\n                importDeliveryTtc: reception.import_delivery_ttc,\n                importDeliveryTva: reception.import_delivery_tva,\n                importDeliveryHt: reception.import_delivery_ttc - reception.import_delivery_tva,\n                customsInspectionTtc: reception.customs_inspection_ttc,\n                customsInspectionTva: reception.customs_inspection_tva,\n                customsInspectionHt: reception.customs_inspection_ttc - reception.customs_inspection_tva,\n                shippingAgencyTtc: reception.shipping_agency_ttc,\n                shippingAgencyTva: reception.shipping_agency_tva,\n                shippingAgencyHt: reception.shipping_agency_ttc - reception.shipping_agency_tva,\n                emptyContainersTtc: reception.empty_containers_ttc,\n                emptyContainersTva: reception.empty_containers_tva,\n                emptyContainersHt: reception.empty_containers_ttc - reception.empty_containers_tva,\n                demurrageHt: reception.demurrage_ht,\n                miscExpensesTtc: reception.misc_expenses_ttc,\n                miscExpensesTva: reception.misc_expenses_tva,\n                miscExpensesHt: reception.misc_expenses_ttc - reception.misc_expenses_tva,\n                transitServicesTtc: reception.transit_services_ttc,\n                transitServicesTva: reception.transit_services_tva,\n                transitServicesHt: reception.transit_services_ttc - reception.transit_services_tva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception dans la base de donn\\xe9es\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(`Coûts sauvegardés avec succès pour la facture: ${generatedCosts.invoiceNumber}`);\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(`/shipment-reception?edit=${reception.id}`);\n    };\n    const deleteReception = async (reception)=>{\n        if (confirm(`Êtes-vous sûr de vouloir supprimer la réception \"${reception.supplierName} - ${reception.orderNumber}\" de la base de données ?`)) {\n            try {\n                // Supprimer via l'API\n                const response = await fetch(`/api/shipment-receptions?id=${reception.id}`, {\n                    method: \"DELETE\"\n                });\n                if (!response.ok) {\n                    throw new Error(\"Erreur lors de la suppression\");\n                }\n                // Recharger la liste\n                await loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if (receptionData?.id === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(`Réception supprimée avec succès de la base de données`);\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception: \" + error.message);\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const fobDzd = reception.fobAmount * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const landedCostHt = cifDzd + totalAllFees;\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Créer les données pour Excel (format CSV compatible)\n            const excelData = [\n                // En-têtes\n                [\n                    \"SHIPMENT RECEPTION DETAILED REPORT\"\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GENERAL INFORMATION\"\n                ],\n                [\n                    \"Supplier Name\",\n                    reception.supplierName\n                ],\n                [\n                    \"Order Number\",\n                    reception.orderNumber\n                ],\n                [\n                    \"Invoice Number\",\n                    reception.invoiceNumber\n                ],\n                [\n                    \"Currency\",\n                    reception.currency\n                ],\n                [\n                    \"Exchange Rate\",\n                    reception.exchangeRate.toFixed(5)\n                ],\n                [\n                    \"Created At\",\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GOODS PRICE\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (\" + reception.currency + \")\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"FOB Amount\",\n                    reception.fobAmount.toFixed(2),\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"Freight\",\n                    reception.freight.toFixed(2),\n                    (reception.freight * reception.exchangeRate).toFixed(2)\n                ],\n                [\n                    \"CIF Amount\",\n                    (reception.fobAmount + reception.freight).toFixed(2),\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"CUSTOMS DUTIES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Customs Duties 1\",\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2)\n                ],\n                [\n                    \"Customs Duties 2\",\n                    \"\",\n                    \"\",\n                    reception.customsDuties2Ht.toFixed(2)\n                ],\n                [\n                    \"TOTAL CUSTOMS DUTIES\",\n                    \"\",\n                    \"\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"PORT FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Import Delivery\",\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2)\n                ],\n                [\n                    \"Customs Inspection\",\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL PORT FEES\",\n                    \"\",\n                    \"\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"SHIPPING COMPANY FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Shipping Agency\",\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2)\n                ],\n                [\n                    \"Empty Containers\",\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2)\n                ],\n                [\n                    \"Demurrage\",\n                    \"\",\n                    \"\",\n                    reception.demurrageHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL SHIPPING FEES\",\n                    \"\",\n                    \"\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"MISCELLANEOUS EXPENSES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Misc Expenses\",\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"TRANSIT SERVICES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Transit Services\",\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"COST BREAKDOWN SUMMARY\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"CIF Amount\",\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"Customs Duties HT\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"Port Fees HT\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"Shipping Fees HT\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"Misc Expenses HT\",\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"Transit Services HT\",\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL ALL FEES HT\",\n                    totalAllFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"FINAL RESULTS\"\n                ],\n                [\n                    \"Description\",\n                    \"Value\"\n                ],\n                [\n                    \"FOB Amount (DZD)\",\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"LANDED COST HT (DZD)\",\n                    landedCostHt.toFixed(2)\n                ],\n                [\n                    \"Coefficient of Landed Cost\",\n                    landedCostCoefficient.toFixed(4)\n                ],\n                [\n                    \"Formula\",\n                    \"Landed Cost (DZD) / FOB (DZD)\"\n                ],\n                [\n                    \"Calculation\",\n                    landedCostHt.toFixed(2) + \" / \" + fobDzd.toFixed(2) + \" = \" + landedCostCoefficient.toFixed(4)\n                ]\n            ];\n            // Convertir en CSV pour Excel\n            const csvContent = excelData.map((row)=>row.map((cell)=>typeof cell === \"string\" && (cell.includes(\",\") || cell.includes('\"')) ? `\"${cell.replace(/\"/g, '\"\"')}\"` : cell).join(\",\")).join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", `reception_${reception.supplierName}_${reception.orderNumber}_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(`Export Excel réussi pour la réception: ${reception.supplierName} - ${reception.orderNumber}`);\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? `\"${cell}\"` : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", `shipment_receptions_export_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(`Export Excel réussi ! ${allReceptions.length} réceptions exportées.`);\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 590,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 589,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 765,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 599,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 598,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();