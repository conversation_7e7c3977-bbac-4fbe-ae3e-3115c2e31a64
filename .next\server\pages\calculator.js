/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/calculator";
exports.ids = ["pages/calculator"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: () => (/* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Edit: () => (/* reexport safe */ _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   RefreshCw: () => (/* reexport safe */ _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Save: () => (/* reexport safe */ _icons_save_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/download.js */ \"./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/pen-square.js */ \"./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/refresh-cw.js */ \"./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _icons_save_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/save.js */ \"./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/search.js */ \"./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxDYWxjdWxhdG9yLENoZWNrQ2lyY2xlLERvd25sb2FkLEVkaXQsRXllLEZpbGVUZXh0LFJlZnJlc2hDdyxTYXZlLFNlYXJjaCxUcmFzaDIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0g7QUFDRztBQUNQO0FBQ0Y7QUFDUjtBQUNXO0FBQ0U7QUFDWDtBQUNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZjFiMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxlcnRDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9hbGVydC1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxjdWxhdG9yIH0gZnJvbSBcIi4vaWNvbnMvY2FsY3VsYXRvci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2stY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG93bmxvYWQgfSBmcm9tIFwiLi9pY29ucy9kb3dubG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEVkaXQgfSBmcm9tIFwiLi9pY29ucy9wZW4tc3F1YXJlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllIH0gZnJvbSBcIi4vaWNvbnMvZXllLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVRleHQgfSBmcm9tIFwiLi9pY29ucy9maWxlLXRleHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBSZWZyZXNoQ3cgfSBmcm9tIFwiLi9pY29ucy9yZWZyZXNoLWN3LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2F2ZSB9IGZyb20gXCIuL2ljb25zL3NhdmUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   FileSpreadsheet: () => (/* reexport safe */ _icons_file_spreadsheet_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Ship: () => (/* reexport safe */ _icons_ship_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_file_spreadsheet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/file-spreadsheet.js */ \"./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_ship_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/ship.js */ \"./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsY3VsYXRvcixGaWxlU3ByZWFkc2hlZXQsRmlsZVRleHQsTG9nT3V0LE1lbnUsUGFja2FnZSxTZXR0aW5ncyxTaGlwLFVzZXIsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDQTtBQUNXO0FBQ2Q7QUFDSjtBQUNMO0FBQ007QUFDRTtBQUNSO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbXBvcnQtbG9naXN0aWNzLWNhbGN1bGF0b3IvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9hMjFlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJDaGFydDMgfSBmcm9tIFwiLi9pY29ucy9iYXItY2hhcnQtMy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGN1bGF0b3IgfSBmcm9tIFwiLi9pY29ucy9jYWxjdWxhdG9yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVNwcmVhZHNoZWV0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS1zcHJlYWRzaGVldC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpcCB9IGZyb20gXCIuL2ljb25zL3NoaXAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\calculator.tsx */ \"./pages/calculator.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/calculator\",\n        pathname: \"/calculator\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calculator,FileSpreadsheet,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst getNavigation = (userRole)=>{\n    const baseNavigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.BarChart3\n        },\n        {\n            name: \"Shipment Reception\",\n            href: \"/shipment-reception\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Ship\n        },\n        {\n            name: \"Cost Calculator\",\n            href: \"/calculator\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator\n        },\n        {\n            name: \"Cost Distribution\",\n            href: \"/cost-distribution\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileSpreadsheet\n        },\n        {\n            name: \"Order Management\",\n            href: \"/orders\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Package\n        },\n        {\n            name: \"Reports\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileText\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings\n        }\n    ];\n    // Add admin-only navigation for super admin\n    if (userRole === \"super_admin\") {\n        baseNavigation.splice(-1, 0, {\n            name: \"User Management\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User\n        });\n    }\n    return baseNavigation;\n};\nfunction Layout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [sidebarOpen, setSidebarOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const navigation = getNavigation(session?.user?.role);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-0 z-50 lg:hidden\", sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-16 items-center justify-between px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Import Calculator\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 space-y-1 px-2 py-4\",\n                                children: navigation.map((item)=>{\n                                    const isActive = router.pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center flex-shrink-0 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator, {\n                                    className: \"h-8 w-8 text-primary mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Import Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-8 flex-1 space-y-1 px-2\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 flex-shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Menu, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: session.user?.name || session.user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        session.user?.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800\",\n                                                            children: session.user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                    className: \"text-gray-500 hover:text-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileSpreadsheet_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Layout.tsx\n");

/***/ }),

/***/ "./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNhO0FBQ3NCO0FBQ2pDO0FBRWhDLE1BQU1JLGlCQUFpQkYsNkRBQUdBLENBQ3hCLDBSQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGFBQ0U7WUFDRkMsU0FDRTtZQUNGQyxXQUNFO1lBQ0ZDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLE1BQU07WUFDSk4sU0FBUztZQUNUTyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZYLFNBQVM7UUFDVE8sTUFBTTtJQUNSO0FBQ0Y7QUFTRixNQUFNSyx1QkFBU2xCLDZDQUFnQixDQUM3QixDQUFDLEVBQUVvQixTQUFTLEVBQUVkLE9BQU8sRUFBRU8sSUFBSSxFQUFFUSxVQUFVLEtBQUssRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hELE1BQU1DLE9BQU9ILFVBQVVwQixzREFBSUEsR0FBRztJQUM5QixxQkFDRSw4REFBQ3VCO1FBQ0NKLFdBQVdqQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTTztZQUFNTztRQUFVO1FBQ3hERyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE9BQU9PLFdBQVcsR0FBRztBQUVZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vY29tcG9uZW50cy91aS9idXR0b24udHN4Pzg5NDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICB7XG4gICAgdmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IHtcbiAgICAgICAgZGVmYXVsdDogXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvOTBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJiZy1kZXN0cnVjdGl2ZSB0ZXh0LWRlc3RydWN0aXZlLWZvcmVncm91bmQgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzZWNvbmRhcnk6XG4gICAgICAgICAgXCJiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1zZWNvbmRhcnkvODBcIixcbiAgICAgICAgZ2hvc3Q6IFwiaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgbGluazogXCJ0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTEwIHB4LTQgcHktMlwiLFxuICAgICAgICBzbTogXCJoLTkgcm91bmRlZC1tZCBweC0zXCIsXG4gICAgICAgIGxnOiBcImgtMTEgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XG4gIGFzQ2hpbGQ/OiBib29sZWFuXG59XG5cbmNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcbiAgICByZXR1cm4gKFxuICAgICAgPENvbXBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJyZWYiLCJDb21wIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/button.tsx\n");

/***/ }),

/***/ "./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/card.tsx\n");

/***/ }),

/***/ "./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/input.tsx\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4ade80\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Calculator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = ()=>{\n        try {\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplierName,\n                    orderNumber: reception.orderNumber,\n                    invoiceNumber: reception.invoiceNumber,\n                    exchangeRate: reception.exchangeRate,\n                    currency: reception.currency,\n                    fobAmount: reception.fobAmount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customsDuties1Ttc,\n                    customsDuties1Tva: reception.customsDuties1Tva,\n                    customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                    customsDuties2Ht: reception.customsDuties2Ht,\n                    importDeliveryTtc: reception.importDeliveryTtc,\n                    importDeliveryTva: reception.importDeliveryTva,\n                    importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                    customsInspectionTtc: reception.customsInspectionTtc,\n                    customsInspectionTva: reception.customsInspectionTva,\n                    customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                    shippingAgencyTtc: reception.shippingAgencyTtc,\n                    shippingAgencyTva: reception.shippingAgencyTva,\n                    shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                    emptyContainersTtc: reception.emptyContainersTtc,\n                    emptyContainersTva: reception.emptyContainersTva,\n                    emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                    demurrageHt: reception.demurrageHt,\n                    miscExpensesTtc: reception.miscExpensesTtc,\n                    miscExpensesTva: reception.miscExpensesTva,\n                    miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                    transitServicesTtc: reception.transitServicesTtc,\n                    transitServicesTva: reception.transitServicesTva,\n                    transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva,\n                    createdAt: reception.createdAt\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const reception = savedReceptions.find((r)=>r.invoiceNumber === invoiceNumber.trim());\n            if (!reception) {\n                setError(`Aucune réception trouvée pour la facture: ${invoiceNumber}`);\n                setIsLoading(false);\n                return;\n            }\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplierName,\n                orderNumber: reception.orderNumber,\n                invoiceNumber: reception.invoiceNumber,\n                exchangeRate: reception.exchangeRate,\n                currency: reception.currency,\n                fobAmount: reception.fobAmount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customsDuties1Ttc,\n                customsDuties1Tva: reception.customsDuties1Tva,\n                customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                customsDuties2Ht: reception.customsDuties2Ht,\n                importDeliveryTtc: reception.importDeliveryTtc,\n                importDeliveryTva: reception.importDeliveryTva,\n                importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                customsInspectionTtc: reception.customsInspectionTtc,\n                customsInspectionTva: reception.customsInspectionTva,\n                customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                shippingAgencyTtc: reception.shippingAgencyTtc,\n                shippingAgencyTva: reception.shippingAgencyTva,\n                shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                emptyContainersTtc: reception.emptyContainersTtc,\n                emptyContainersTva: reception.emptyContainersTva,\n                emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                demurrageHt: reception.demurrageHt,\n                miscExpensesTtc: reception.miscExpensesTtc,\n                miscExpensesTva: reception.miscExpensesTva,\n                miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                transitServicesTtc: reception.transitServicesTtc,\n                transitServicesTva: reception.transitServicesTva,\n                transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(`Coûts sauvegardés avec succès pour la facture: ${generatedCosts.invoiceNumber}`);\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(`/shipment-reception?edit=${reception.id}`);\n    };\n    const deleteReception = (reception)=>{\n        if (confirm(`Êtes-vous sûr de vouloir supprimer la réception \"${reception.supplierName} - ${reception.orderNumber}\" ?`)) {\n            try {\n                const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n                const updatedReceptions = savedReceptions.filter((r)=>r.id !== reception.id);\n                localStorage.setItem(\"shipment-receptions\", JSON.stringify(updatedReceptions));\n                // Recharger la liste\n                loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if (receptionData?.id === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(`Réception supprimée avec succès`);\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception\");\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const fobDzd = reception.fobAmount * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const landedCostHt = cifDzd + totalAllFees;\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Créer les données pour Excel (format CSV compatible)\n            const excelData = [\n                // En-têtes\n                [\n                    \"SHIPMENT RECEPTION DETAILED REPORT\"\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GENERAL INFORMATION\"\n                ],\n                [\n                    \"Supplier Name\",\n                    reception.supplierName\n                ],\n                [\n                    \"Order Number\",\n                    reception.orderNumber\n                ],\n                [\n                    \"Invoice Number\",\n                    reception.invoiceNumber\n                ],\n                [\n                    \"Currency\",\n                    reception.currency\n                ],\n                [\n                    \"Exchange Rate\",\n                    reception.exchangeRate.toFixed(5)\n                ],\n                [\n                    \"Created At\",\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GOODS PRICE\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (\" + reception.currency + \")\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"FOB Amount\",\n                    reception.fobAmount.toFixed(2),\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"Freight\",\n                    reception.freight.toFixed(2),\n                    (reception.freight * reception.exchangeRate).toFixed(2)\n                ],\n                [\n                    \"CIF Amount\",\n                    (reception.fobAmount + reception.freight).toFixed(2),\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"CUSTOMS DUTIES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Customs Duties 1\",\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2)\n                ],\n                [\n                    \"Customs Duties 2\",\n                    \"\",\n                    \"\",\n                    reception.customsDuties2Ht.toFixed(2)\n                ],\n                [\n                    \"TOTAL CUSTOMS DUTIES\",\n                    \"\",\n                    \"\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"PORT FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Import Delivery\",\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2)\n                ],\n                [\n                    \"Customs Inspection\",\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL PORT FEES\",\n                    \"\",\n                    \"\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"SHIPPING COMPANY FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Shipping Agency\",\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2)\n                ],\n                [\n                    \"Empty Containers\",\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2)\n                ],\n                [\n                    \"Demurrage\",\n                    \"\",\n                    \"\",\n                    reception.demurrageHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL SHIPPING FEES\",\n                    \"\",\n                    \"\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"MISCELLANEOUS EXPENSES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Misc Expenses\",\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"TRANSIT SERVICES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Transit Services\",\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"COST BREAKDOWN SUMMARY\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"CIF Amount\",\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"Customs Duties HT\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"Port Fees HT\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"Shipping Fees HT\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"Misc Expenses HT\",\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"Transit Services HT\",\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL ALL FEES HT\",\n                    totalAllFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"FINAL RESULTS\"\n                ],\n                [\n                    \"Description\",\n                    \"Value\"\n                ],\n                [\n                    \"FOB Amount (DZD)\",\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"LANDED COST HT (DZD)\",\n                    landedCostHt.toFixed(2)\n                ],\n                [\n                    \"Coefficient of Landed Cost\",\n                    landedCostCoefficient.toFixed(4)\n                ],\n                [\n                    \"Formula\",\n                    \"Landed Cost (DZD) / FOB (DZD)\"\n                ],\n                [\n                    \"Calculation\",\n                    landedCostHt.toFixed(2) + \" / \" + fobDzd.toFixed(2) + \" = \" + landedCostCoefficient.toFixed(4)\n                ]\n            ];\n            // Convertir en CSV pour Excel\n            const csvContent = excelData.map((row)=>row.map((cell)=>typeof cell === \"string\" && (cell.includes(\",\") || cell.includes('\"')) ? `\"${cell.replace(/\"/g, '\"\"')}\"` : cell).join(\",\")).join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", `reception_${reception.supplierName}_${reception.orderNumber}_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(`Export Excel réussi pour la réception: ${reception.supplierName} - ${reception.orderNumber}`);\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? `\"${cell}\"` : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", `shipment_receptions_export_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(`Export Excel réussi ! ${allReceptions.length} réceptions exportées.`);\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 572,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 748,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 582,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 581,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();