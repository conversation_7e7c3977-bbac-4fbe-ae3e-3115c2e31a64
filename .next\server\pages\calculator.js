/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/calculator";
exports.ids = ["pages/calculator"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsY3VsYXRvcixGaWxlVGV4dCxMb2dPdXQsTWVudSxQYWNrYWdlLFNldHRpbmdzLFVzZXIsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNBO0FBQ0g7QUFDSjtBQUNMO0FBQ007QUFDRTtBQUNSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/NjlmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxjdWxhdG9yIH0gZnJvbSBcIi4vaWNvbnMvY2FsY3VsYXRvci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlciB9IGZyb20gXCIuL2ljb25zL3VzZXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Save: () => (/* reexport safe */ _icons_save_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_save_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/save.js */ \"./node_modules/lucide-react/dist/esm/icons/save.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DYWxjdWxhdG9yLEZpbGVUZXh0LFNhdmUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDNkQ7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2ltcG9ydC1sb2dpc3RpY3MtY2FsY3VsYXRvci8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzI2M2EiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGN1bGF0b3IgfSBmcm9tIFwiLi9pY29ucy9jYWxjdWxhdG9yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVRleHQgfSBmcm9tIFwiLi9pY29ucy9maWxlLXRleHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTYXZlIH0gZnJvbSBcIi4vaWNvbnMvc2F2ZS5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\calculator.tsx */ \"./pages/calculator.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/calculator\",\n        pathname: \"/calculator\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_calculator_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,User,X!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst getNavigation = (userRole)=>{\n    const baseNavigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.BarChart3\n        },\n        {\n            name: \"Cost Calculator\",\n            href: \"/calculator\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator\n        },\n        {\n            name: \"Order Management\",\n            href: \"/orders\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Package\n        },\n        {\n            name: \"Reports\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileText\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings\n        }\n    ];\n    // Add admin-only navigation for super admin\n    if (userRole === \"super_admin\") {\n        baseNavigation.splice(-1, 0, {\n            name: \"User Management\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User\n        });\n    }\n    return baseNavigation;\n};\nfunction Layout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [sidebarOpen, setSidebarOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const navigation = getNavigation(session?.user?.role);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-0 z-50 lg:hidden\", sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-16 items-center justify-between px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Import Calculator\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 space-y-1 px-2 py-4\",\n                                children: navigation.map((item)=>{\n                                    const isActive = router.pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center flex-shrink-0 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator, {\n                                    className: \"h-8 w-8 text-primary mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Import Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-8 flex-1 space-y-1 px-2\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 flex-shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Menu, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: session.user?.name || session.user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        session.user?.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800\",\n                                                            children: session.user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                    className: \"text-gray-500 hover:text-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Layout.tsx\n");

/***/ }),

/***/ "./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/button.tsx\n");

/***/ }),

/***/ "./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/card.tsx\n");

/***/ }),

/***/ "./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/input.tsx\n");

/***/ }),

/***/ "./lib/calculations.ts":
/*!*****************************!*\
  !*** ./lib/calculations.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateLandedCost: () => (/* binding */ calculateLandedCost),\n/* harmony export */   calculateOrderItemCosts: () => (/* binding */ calculateOrderItemCosts),\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   calculateShipmentMetrics: () => (/* binding */ calculateShipmentMetrics),\n/* harmony export */   convertToDzd: () => (/* binding */ convertToDzd),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatExchangeRate: () => (/* binding */ formatExchangeRate),\n/* harmony export */   validateExchangeRate: () => (/* binding */ validateExchangeRate)\n/* harmony export */ });\n/**\n * Calculate the total landed cost for a shipment\n */ function calculateLandedCost(shipment, customsDuties, portFees, shippingFees, miscExpenses, transitExpenses) {\n    // Base amounts (already calculated in database)\n    const fobAmountDzd = shipment.fob_amount_dzd;\n    const freightDzd = shipment.freight_dzd;\n    const totalCifDzd = shipment.total_cif_dzd;\n    // Calculate customs duties totals\n    const customsDutiesHt = customsDuties?.total_ht || 0;\n    const customsDutiesTtc = customsDuties?.total_ttc || 0;\n    // Calculate port fees totals\n    const portFeesHt = portFees ? portFees.import_delivery_ht + portFees.customs_inspection_ht : 0;\n    const portFeesTtc = portFees ? portFees.import_delivery_ttc + portFees.customs_inspection_ttc : 0;\n    // Calculate shipping fees totals\n    const shippingFeesHt = shippingFees ? shippingFees.shipping_agency_ht + shippingFees.empty_containers_ht + shippingFees.demurrage_ht : 0;\n    const shippingFeesTtc = shippingFees ? shippingFees.shipping_agency_ttc + shippingFees.empty_containers_ttc : 0;\n    // Miscellaneous and transit expenses\n    const miscExpensesHt = miscExpenses?.amount_ht || 0;\n    const miscExpensesTtc = miscExpenses?.amount_ttc || 0;\n    const transitExpensesHt = transitExpenses?.amount_ht || 0;\n    const transitExpensesTtc = transitExpenses?.amount_ttc || 0;\n    // Calculate total landed cost HT\n    const landedCostHt = totalCifDzd + customsDutiesHt + portFeesHt + shippingFeesHt + miscExpensesHt + transitExpensesHt;\n    // Calculate landed cost coefficient\n    const landedCostCoefficient = fobAmountDzd > 0 ? landedCostHt / fobAmountDzd : 0;\n    // Calculate total paid TTC\n    const totalPaidTtc = totalCifDzd + customsDutiesTtc + portFeesTtc + shippingFeesTtc + miscExpensesTtc + transitExpensesTtc;\n    return {\n        fobAmountDzd,\n        freightDzd,\n        totalCifDzd,\n        customsDutiesHt,\n        portFeesHt,\n        shippingFeesHt,\n        miscExpensesHt,\n        transitExpensesHt,\n        landedCostHt,\n        landedCostCoefficient,\n        customsDutiesTtc,\n        portFeesTtc,\n        shippingFeesTtc,\n        miscExpensesTtc,\n        transitExpensesTtc,\n        totalPaidTtc\n    };\n}\n/**\n * Calculate unit cost in DZD for each order item\n */ function calculateOrderItemCosts(orderItems, landedCostCalculation) {\n    const { landedCostCoefficient } = landedCostCalculation;\n    return orderItems.map((item)=>{\n        // Convert unit FOB from USD to DZD using the landed cost coefficient\n        const unitCostDzd = item.unit_fob_usd * landedCostCoefficient;\n        const amountDzd = unitCostDzd * item.quantity;\n        return {\n            ...item,\n            unitCostDzd: Math.round(unitCostDzd * 100) / 100,\n            amountDzd: Math.round(amountDzd * 100) / 100\n        };\n    });\n}\n/**\n * Format currency with French locale (5 decimal places for exchange rates)\n */ function formatCurrency(amount, decimals = 2) {\n    return new Intl.NumberFormat(\"fr-FR\", {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(amount);\n}\n/**\n * Format exchange rate with 5 decimal places\n */ function formatExchangeRate(rate) {\n    return formatCurrency(rate, 5);\n}\n/**\n * Convert amount from foreign currency to DZD\n */ function convertToDzd(amount, exchangeRate) {\n    return Math.round(amount * exchangeRate * 100) / 100;\n}\n/**\n * Calculate percentage\n */ function calculatePercentage(part, total) {\n    return total > 0 ? Math.round(part / total * 100 * 100) / 100 : 0;\n}\n/**\n * Validate exchange rate (must be positive and reasonable)\n */ function validateExchangeRate(rate, currency) {\n    const minRates = {\n        USD: 100,\n        EUR: 120,\n        CNY: 15\n    };\n    const maxRates = {\n        USD: 200,\n        EUR: 200,\n        CNY: 30\n    };\n    const min = minRates[currency] || 0;\n    const max = maxRates[currency] || Infinity;\n    return rate >= min && rate <= max;\n}\n/**\n * Calculate total weight/volume metrics if needed\n */ function calculateShipmentMetrics(shipment) {\n    return {\n        totalContainers: shipment.containers_20ft + shipment.containers_40ft,\n        containerMix: `${shipment.containers_20ft}x20ft + ${shipment.containers_40ft}x40ft`,\n        totalPackages: shipment.number_packages || 0,\n        totalQuantity: shipment.quantity_pcs || 0\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/calculations.ts\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4ade80\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFFb0I7QUFDUjtBQTZCMUIsU0FBU0UsSUFBSSxFQUMxQkMsU0FBUyxFQUNUQyxXQUFXLEVBQUVDLE9BQU8sRUFBRSxHQUFHRCxXQUFXLEVBQzNCO0lBQ1QscUJBQ0UsOERBQUNKLDREQUFlQTtRQUFDSyxTQUFTQTs7MEJBQ3hCLDhEQUFDRjtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7MEJBQ3hCLDhEQUFDSCxvREFBT0E7Z0JBQ05LLFVBQVM7Z0JBQ1RDLGNBQWM7b0JBQ1pDLFVBQVU7b0JBQ1ZDLE9BQU87d0JBQ0xDLFlBQVk7d0JBQ1pDLE9BQU87b0JBQ1Q7b0JBQ0FDLFNBQVM7d0JBQ1BKLFVBQVU7d0JBQ1ZLLFdBQVc7NEJBQ1RDLFNBQVM7NEJBQ1RDLFdBQVc7d0JBQ2I7b0JBQ0Y7b0JBQ0FDLE9BQU87d0JBQ0xSLFVBQVU7d0JBQ1ZLLFdBQVc7NEJBQ1RDLFNBQVM7NEJBQ1RDLFdBQVc7d0JBQ2I7b0JBQ0Y7Z0JBQ0Y7Ozs7Ozs7Ozs7OztBQUlSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ0Avc3R5bGVzL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCdcblxuZGVjbGFyZSBtb2R1bGUgJ25leHQtYXV0aCcge1xuICBpbnRlcmZhY2UgU2Vzc2lvbiB7XG4gICAgdXNlcjoge1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgZW1haWw6IHN0cmluZ1xuICAgICAgbmFtZT86IHN0cmluZyB8IG51bGxcbiAgICAgIHJvbGU/OiBzdHJpbmdcbiAgICAgIGNvbXBhbnk/OiBzdHJpbmdcbiAgICB9XG4gIH1cblxuICBpbnRlcmZhY2UgVXNlciB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBuYW1lPzogc3RyaW5nIHwgbnVsbFxuICAgIHJvbGU/OiBzdHJpbmdcbiAgICBjb21wYW55Pzogc3RyaW5nXG4gIH1cbn1cblxuZGVjbGFyZSBtb2R1bGUgJ25leHQtYXV0aC9qd3QnIHtcbiAgaW50ZXJmYWNlIEpXVCB7XG4gICAgcm9sZT86IHN0cmluZ1xuICAgIGNvbXBhbnk/OiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoe1xuICBDb21wb25lbnQsXG4gIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSxcbn06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDxUb2FzdGVyXG4gICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjMzYzNjM2JyxcbiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgfSxcbiAgICAgICAgICBzdWNjZXNzOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogMzAwMCxcbiAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICBwcmltYXJ5OiAnIzRhZGU4MCcsXG4gICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogNTAwMCxcbiAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICBwcmltYXJ5OiAnI2VmNDQ0NCcsXG4gICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlRvYXN0ZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Calculator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,Save!=!lucide-react */ \"__barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_calculations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/calculations */ \"./lib/calculations.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    supplierName: \"\",\n    orderNumber: \"\",\n    orderDate: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    currency: \"USD\",\n    exchangeRate: 134.50000,\n    fobAmount: 0,\n    freight: 0,\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    demurrageHt: 0,\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    transitExpensesTtc: 0,\n    transitExpensesTva: 0\n};\nfunction Calculator() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [calculation, setCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCalculating, setIsCalculating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const calculateCosts = ()=>{\n        setIsCalculating(true);\n        // Simulate calculation delay\n        setTimeout(()=>{\n            // Create mock shipment data\n            const shipment = {\n                fob_amount: form.fobAmount,\n                freight: form.freight,\n                exchange_rate_used: form.exchangeRate,\n                fob_amount_dzd: form.fobAmount * form.exchangeRate,\n                freight_dzd: form.freight * form.exchangeRate,\n                total_cif_dzd: (form.fobAmount + form.freight) * form.exchangeRate\n            };\n            // Create mock cost data\n            const customsDuties = {\n                customs_duties1_ttc: form.customsDuties1Ttc,\n                customs_duties1_tva: form.customsDuties1Tva,\n                customs_duties2_ttc: form.customsDuties2Ttc,\n                customs_duties2_tva: 0,\n                total_ttc: form.customsDuties1Ttc + form.customsDuties2Ttc,\n                total_tva: form.customsDuties1Tva + 0,\n                total_ht: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc // customsDuties2Ttc est déjà HT\n            };\n            const portFees = {\n                import_delivery_ttc: form.importDeliveryTtc,\n                import_delivery_tva: form.importDeliveryTva,\n                import_delivery_ht: form.importDeliveryTtc - form.importDeliveryTva,\n                customs_inspection_ttc: form.customsInspectionTtc,\n                customs_inspection_tva: form.customsInspectionTva,\n                customs_inspection_ht: form.customsInspectionTtc - form.customsInspectionTva\n            };\n            const shippingFees = {\n                shipping_agency_ttc: form.shippingAgencyTtc,\n                shipping_agency_tva: form.shippingAgencyTva,\n                shipping_agency_ht: form.shippingAgencyTtc - form.shippingAgencyTva,\n                empty_containers_ttc: form.emptyContainersTtc,\n                empty_containers_tva: form.emptyContainersTva,\n                empty_containers_ht: form.emptyContainersTtc - form.emptyContainersTva,\n                demurrage_ht: form.demurrageHt\n            };\n            const miscExpenses = {\n                amount_ttc: form.miscExpensesTtc,\n                amount_tva: form.miscExpensesTva,\n                amount_ht: form.miscExpensesTtc - form.miscExpensesTva\n            };\n            const transitExpenses = {\n                amount_ttc: form.transitExpensesTtc,\n                amount_tva: form.transitExpensesTva,\n                amount_ht: form.transitExpensesTtc - form.transitExpensesTva\n            };\n            const result = (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.calculateLandedCost)(shipment, customsDuties, portFees, shippingFees, miscExpenses, transitExpenses);\n            setCalculation(result);\n            setIsCalculating(false);\n        }, 1000);\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Cost Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Calculate landed costs for your imports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: calculateCosts,\n                                    disabled: isCalculating,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        isCalculating ? \"Calculating...\" : \"Calculate\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"General Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Basic shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Supplier Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Number *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.orderDate,\n                                                                onChange: (e)=>handleInputChange(\"orderDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Exchange Rate (to DZD) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"134.50000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        form.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"FOB and freight costs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"FOB Amount *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.fobAmount,\n                                                                    onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Freight\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.freight,\n                                                                    onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-4 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Total CIF (\",\n                                                                        form.currency,\n                                                                        \"):\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(form.fobAmount + form.freight)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)((form.fobAmount + form.freight) * form.exchangeRate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Customs Duties (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Algeria customs fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: 'Other Customs Duties (HT) \"T.E.L , A.M.D, .....\"'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.customsDuties2Ttc,\n                                                                            onChange: (e)=>handleInputChange(\"customsDuties2Ttc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 406,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Calculation Results\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: calculation ? \"Landed cost breakdown\" : \"Enter values and click Calculate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: calculation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.fobAmountDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Freight (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.freightDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalCifDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Cost Components (HT)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.customsDutiesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.portFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.shippingFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc. Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.miscExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.transitExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-primary\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost (HT):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.landedCostHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        calculation.landedCostCoefficient.toFixed(5),\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total Paid (TTC):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalPaidTtc)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        className: \"w-full\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.FileText, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Generate Report\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Enter your shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Fill in the form and click Calculate to see results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcalculator&preferredRegion=&absolutePagePath=.%2Fpages%5Ccalculator.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();