"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Calculs détaillés basés sur les formules de l'application\n        const exchangeRate = 134.50000 // Taux de change USD vers DZD\n        ;\n        const fobAmountDzd = shipment.fobAmount * exchangeRate;\n        const freightDzd = shipment.fobAmount * 0.15 * exchangeRate // Estimation 15% du FOB\n        ;\n        const totalCifDzd = fobAmountDzd + freightDzd;\n        // Valeurs manuelles simulées (dans une vraie application, ces valeurs viendraient des champs de saisie)\n        // PORT FEES - Valeurs TTC et TVA saisies manuellement\n        const importDeliveryTtc = 482344 // Valeur TTC saisie manuellement\n        ;\n        const importDeliveryTva = 77175 // TVA saisie manuellement (pas calculée automatiquement)\n        ;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406 // Valeur TTC saisie manuellement\n        ;\n        const customsInspectionTva = 46305 // TVA saisie manuellement\n        ;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES - Valeurs TTC et TVA saisies manuellement\n        const shippingAgencyTtc = 675281 // Valeur TTC saisie manuellement\n        ;\n        const shippingAgencyTva = 108045 // TVA saisie manuellement\n        ;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875 // Valeur TTC saisie manuellement\n        ;\n        const emptyContainersTva = 61740 // TVA saisie manuellement\n        ;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // Valeur HT saisie manuellement (pas de TVA sur surestaries)\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES - Valeurs TTC et TVA saisies manuellement\n        const miscExpensesTtc = 482344 // Valeur TTC saisie manuellement\n        ;\n        const miscExpensesTva = 77175 // TVA saisie manuellement\n        ;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES - Valeurs TTC et TVA saisies manuellement\n        const transitExpensesTtc = 385875 // Valeur TTC saisie manuellement\n        ;\n        const transitExpensesTva = 61740 // TVA saisie manuellement\n        ;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs des totaux selon les formules demandées\n        const landedCostTtc = totalCifDzd + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const shipmentData = [\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INFORMATIONS G\\xc9N\\xc9RALES\"\n            ],\n            [\n                \"N\\xb0 Commande:\",\n                shipment.orderNumber\n            ],\n            [\n                \"Fournisseur:\",\n                shipment.supplier\n            ],\n            [\n                \"Date:\",\n                shipment.date\n            ],\n            [\n                \"Statut:\",\n                shipment.status\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"D\\xc9TAIL FINANCIER\"\n            ],\n            [\n                \"Montant FOB (USD):\",\n                shipment.fobAmount.toLocaleString(\"en-US\")\n            ],\n            [\n                \"Taux de change (USD/DZD):\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"Montant FOB (DZD):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Fret (DZD):\",\n                freightDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight):\",\n                totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CO\\xdbTS D\\xc9TAILL\\xc9S (Valeurs saisies manuellement)\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Livraison Import:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                importDeliveryTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                importDeliveryHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Inspection Douani\\xe8re:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                customsInspectionTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                customsInspectionHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'PORT FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                portFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Agence Maritime:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Conteneurs Vides:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                emptyContainersTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                emptyContainersHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Surestaries:\"\n            ],\n            [\n                \"  HT (saisi manuellement, pas de TVA):\",\n                demurrageHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'SHIPPING COMPANY FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                miscExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                miscExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"  TTC (saisi manuellement):\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA (saisie manuellement):\",\n                transitExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT (TTC - TVA):\",\n                transitExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CALCULS FINAUX\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees TTC + Shipping Fees TTC + Misc Expenses TTC + Transit Expenses TTC\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesTtc.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees HT + Shipping Fees HT + Misc Expenses HT + Transit Expenses HT\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesHt.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Coefficient TTC:\",\n                (landedCostTtc / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Coefficient HT:\",\n                (landedCostHt / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Marge vs FOB (TTC):\",\n                \"\".concat(((landedCostTtc / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ],\n            [\n                \"Marge vs FOB (HT):\",\n                \"\".concat(((landedCostHt / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 399,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Export\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"V9rHqNTl7h2x7X2rKtHE3y76I5M=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9yZXBvcnRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ0s7QUFDTDtBQUNRO0FBQ2lEO0FBQ2pEO0FBQ0Y7QUFVeEI7QUFDa0g7QUFDM0c7QUFDTztBQUVuQyxNQUFNK0IsV0FBVztJQUNmQyxrQkFBa0I7UUFDaEI7WUFBRUMsT0FBTztZQUFPQyxXQUFXO1lBQUlDLE9BQU87UUFBUTtRQUM5QztZQUFFRixPQUFPO1lBQU9DLFdBQVc7WUFBR0MsT0FBTztRQUFPO1FBQzVDO1lBQUVGLE9BQU87WUFBT0MsV0FBVztZQUFJQyxPQUFPO1FBQVE7UUFDOUM7WUFBRUYsT0FBTztZQUFPQyxXQUFXO1lBQUlDLE9BQU87UUFBUTtRQUM5QztZQUFFRixPQUFPO1lBQU9DLFdBQVc7WUFBSUMsT0FBTztRQUFRO1FBQzlDO1lBQUVGLE9BQU87WUFBT0MsV0FBVztZQUFJQyxPQUFPO1FBQVE7S0FDL0M7SUFDREMsZUFBZTtRQUNiO1lBQUVDLE1BQU07WUFBY0YsT0FBTztZQUFJRyxRQUFRO1FBQVE7UUFDakQ7WUFBRUQsTUFBTTtZQUFrQkYsT0FBTztZQUFJRyxRQUFRO1FBQVE7UUFDckQ7WUFBRUQsTUFBTTtZQUFhRixPQUFPO1lBQUdHLFFBQVE7UUFBTztRQUM5QztZQUFFRCxNQUFNO1lBQWlCRixPQUFPO1lBQUdHLFFBQVE7UUFBTztRQUNsRDtZQUFFRCxNQUFNO1lBQWVGLE9BQU87WUFBR0csUUFBUTtRQUFPO0tBQ2pEO0lBQ0RDLGlCQUFpQjtRQUNmO1lBQ0VDLElBQUk7WUFDSkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsV0FBVztZQUNYQyxZQUFZO1lBQ1pDLGFBQWE7WUFDYkMsUUFBUTtRQUNWO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLFlBQVk7WUFDWkMsYUFBYTtZQUNiQyxRQUFRO1FBQ1Y7UUFDQTtZQUNFUCxJQUFJO1lBQ0pDLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLFFBQVE7UUFDVjtLQUNEO0FBQ0g7QUFFQSxNQUFNQyxTQUFTO0lBQUM7SUFBVztJQUFXO0lBQVc7SUFBVztDQUFVO0FBRXZELFNBQVNDOztJQUN0QixNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRUosTUFBTSxFQUFFLEdBQUc3QywyREFBVUE7SUFDNUMsTUFBTWtELFNBQVNqRCxzREFBU0E7SUFDeEIsTUFBTSxDQUFDa0QsV0FBV0MsYUFBYSxHQUFHckQsK0NBQVFBLENBQUM7UUFDekNzRCxNQUFNO1FBQ05DLElBQUk7SUFDTjtJQUVBeEQsc0RBQWUsQ0FBQztRQUNkLElBQUkrQyxXQUFXLG1CQUFtQjtZQUNoQ0ssT0FBT00sSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNYO1FBQVFLO0tBQU87SUFFbkIsTUFBTU8saUJBQWlCLENBQUNyQjtRQUN0QixPQUFPLElBQUlzQixLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNwQ0MsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLHVCQUF1QjtZQUN2QkMsdUJBQXVCO1FBQ3pCLEdBQUdDLE1BQU0sQ0FBQzVCO0lBQ1o7SUFFQSxNQUFNNkIsWUFBWSxDQUFDN0I7UUFDakIsT0FBTyxJQUFJc0IsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyx1QkFBdUI7WUFDdkJDLHVCQUF1QjtRQUN6QixHQUFHQyxNQUFNLENBQUM1QjtJQUNaO0lBRUEsTUFBTThCLHFCQUFxQixDQUFDQztRQUMxQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxHQUF3QixPQUFyQkQsWUFBVyxZQUFpRCxPQUF2QyxJQUFJRSxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFDO1lBQ2hGLElBQUlDO1lBRUosSUFBSUwsZUFBZSxpQkFBaUI7Z0JBQ2xDSyxXQUFXQztZQUNiLE9BQU8sSUFBSU4sZUFBZSxpQkFBaUI7Z0JBQ3pDSyxXQUFXRTtZQUNiLE9BQU8sSUFBSVAsZUFBZSxtQkFBbUI7Z0JBQzNDSyxXQUFXRztZQUNiLE9BQU8sSUFBSVIsV0FBV1MsVUFBVSxDQUFDLGNBQWM7Z0JBQzdDLE1BQU1yQyxjQUFjNEIsV0FBV1UsT0FBTyxDQUFDLGFBQWE7Z0JBQ3BETCxXQUFXTSx1QkFBdUJ2QztZQUNwQyxPQUFPO2dCQUNMaUMsV0FBV0M7WUFDYjtZQUVBLGlDQUFpQztZQUNqQzlDLDJDQUFjLENBQUM2QyxVQUFVSjtZQUN6QnhDLCtEQUFhLENBQUMsWUFBcUIsT0FBVHdDLFVBQVM7UUFDckMsRUFBRSxPQUFPYSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDckQsNkRBQVcsQ0FBQztRQUNkO0lBQ0Y7SUFFQSxNQUFNNkMsOEJBQThCO1FBQ2xDLE1BQU1VLEtBQUt4RCx1Q0FBVSxDQUFDMEQsUUFBUTtRQUU5QixnQkFBZ0I7UUFDaEIsTUFBTUMsY0FBYztZQUNsQjtnQkFBQzthQUFrRDtZQUNuRDtnQkFBQztnQkFBYSxHQUFzQm5DLE9BQXBCQSxVQUFVRSxJQUFJLEVBQUMsVUFBa0IsT0FBYkYsVUFBVUcsRUFBRTthQUFHO1lBQ25EO2dCQUFDO2dCQUF1QixJQUFJZSxPQUFPa0Isa0JBQWtCLENBQUM7YUFBUztZQUMvRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBa0I7WUFDbkI7Z0JBQUM7Z0JBQTBCQzthQUFlO1lBQzFDO2dCQUFDO2dCQUF3QkMsV0FBV0MsY0FBYyxDQUFDO2FBQVM7WUFDNUQ7Z0JBQUM7Z0JBQXNCQyxlQUFlQyxPQUFPLENBQUM7YUFBRztZQUNqRDtnQkFBQztnQkFBeUJDLEtBQUtDLEtBQUssQ0FBQ04saUJBQWlCO2FBQUc7WUFDekQ7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXdCO2VBQ3RCM0QsU0FBU0ssYUFBYSxDQUFDNkQsR0FBRyxDQUFDQyxDQUFBQSxPQUFRO29CQUFDQSxLQUFLN0QsSUFBSTtvQkFBRyxHQUFhLE9BQVg2RCxLQUFLL0QsS0FBSyxFQUFDO29CQUFJK0QsS0FBSzVELE1BQU0sQ0FBQ3NELGNBQWMsQ0FBQyxXQUFXO2lCQUFPO1NBQ2xIO1FBRUQsTUFBTU8sWUFBWXRFLHVDQUFVLENBQUN1RSxZQUFZLENBQUNaO1FBQzFDM0QsdUNBQVUsQ0FBQ3dFLGlCQUFpQixDQUFDaEIsSUFBSWMsV0FBVztRQUU1Qyx5QkFBeUI7UUFDekIsTUFBTUcsZ0JBQWdCO1lBQ3BCO2dCQUFDO2dCQUFlO2dCQUFlO2dCQUFRO2dCQUFxQjtnQkFBdUI7Z0JBQWU7YUFBUztlQUN4R3ZFLFNBQVNRLGVBQWUsQ0FBQzBELEdBQUcsQ0FBQ00sQ0FBQUEsSUFBSztvQkFDbkNBLEVBQUU5RCxXQUFXO29CQUNiOEQsRUFBRTdELFFBQVE7b0JBQ1Y2RCxFQUFFNUQsSUFBSTtvQkFDTjRELEVBQUUzRCxTQUFTO29CQUNYMkQsRUFBRTFELFVBQVU7b0JBQ1owRCxFQUFFekQsV0FBVyxDQUFDZ0QsT0FBTyxDQUFDO29CQUN0QlMsRUFBRXhELE1BQU07aUJBQ1Q7U0FDRjtRQUVELE1BQU15RCxjQUFjM0UsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ0U7UUFDNUN6RSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJbUIsYUFBYTtRQUU5QyxxQkFBcUI7UUFDckIsTUFBTUMsY0FBYztZQUNsQjtnQkFBQztnQkFBUTtnQkFBeUI7YUFBZTtlQUM5QzFFLFNBQVNDLGdCQUFnQixDQUFDaUUsR0FBRyxDQUFDUyxDQUFBQSxJQUFLO29CQUFDQSxFQUFFekUsS0FBSztvQkFBRXlFLEVBQUV4RSxTQUFTO29CQUFFd0UsRUFBRXZFLEtBQUs7aUJBQUM7U0FDdEU7UUFFRCxNQUFNd0UsWUFBWTlFLHVDQUFVLENBQUN1RSxZQUFZLENBQUNLO1FBQzFDNUUsdUNBQVUsQ0FBQ3dFLGlCQUFpQixDQUFDaEIsSUFBSXNCLFdBQVc7UUFFNUMsT0FBT3RCO0lBQ1Q7SUFFQSxNQUFNVCw2QkFBNkI7UUFDakMsTUFBTVMsS0FBS3hELHVDQUFVLENBQUMwRCxRQUFRO1FBRTlCLE1BQU1xQixXQUFXO1lBQ2Y7Z0JBQUM7YUFBb0Q7WUFDckQ7Z0JBQUM7Z0JBQWEsR0FBc0J2RCxPQUFwQkEsVUFBVUUsSUFBSSxFQUFDLFVBQWtCLE9BQWJGLFVBQVVHLEVBQUU7YUFBRztZQUNuRDtnQkFBQztnQkFBdUIsSUFBSWUsT0FBT2tCLGtCQUFrQixDQUFDO2FBQVM7WUFDL0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQWtDO1lBQ25DO2dCQUFDO2dCQUFhO2dCQUFlO2dCQUFpQjthQUFjO2VBQ3pEMUQsU0FBU0ssYUFBYSxDQUFDNkQsR0FBRyxDQUFDQyxDQUFBQSxPQUFRO29CQUNwQ0EsS0FBSzdELElBQUk7b0JBQ1IsR0FBYSxPQUFYNkQsS0FBSy9ELEtBQUssRUFBQztvQkFDZCtELEtBQUs1RCxNQUFNLENBQUNzRCxjQUFjLENBQUM7b0JBQzNCaUIsd0JBQXdCWCxLQUFLN0QsSUFBSTtpQkFDbEM7WUFDRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBeUI7WUFDMUI7Z0JBQUM7Z0JBQWU7Z0JBQWE7Z0JBQXVCO2dCQUFlO2FBQWU7ZUFDL0VOLFNBQVNRLGVBQWUsQ0FBQzBELEdBQUcsQ0FBQ00sQ0FBQUEsSUFBSztvQkFDbkNBLEVBQUU5RCxXQUFXO29CQUNiOEQsRUFBRTNELFNBQVMsQ0FBQ2dELGNBQWMsQ0FBQztvQkFDM0JXLEVBQUUxRCxVQUFVLENBQUMrQyxjQUFjLENBQUM7b0JBQzVCVyxFQUFFekQsV0FBVyxDQUFDZ0QsT0FBTyxDQUFDO29CQUNyQixHQUF5QyxPQUF2QyxDQUFDLENBQUNTLEVBQUV6RCxXQUFXLEdBQUcsS0FBSyxHQUFFLEVBQUdnRCxPQUFPLENBQUMsSUFBRztpQkFDM0M7U0FDRjtRQUVELE1BQU1nQixLQUFLakYsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ1E7UUFDbkMvRSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJeUIsSUFBSTtRQUVyQyxPQUFPekI7SUFDVDtJQUVBLE1BQU1SLCtCQUErQjtRQUNuQyxNQUFNUSxLQUFLeEQsdUNBQVUsQ0FBQzBELFFBQVE7UUFFOUIsTUFBTWtCLGNBQWM7WUFDbEI7Z0JBQUM7YUFBaUQ7WUFDbEQ7Z0JBQUM7Z0JBQWEsR0FBc0JwRCxPQUFwQkEsVUFBVUUsSUFBSSxFQUFDLFVBQWtCLE9BQWJGLFVBQVVHLEVBQUU7YUFBRztZQUNuRDtnQkFBQztnQkFBdUIsSUFBSWUsT0FBT2tCLGtCQUFrQixDQUFDO2FBQVM7WUFDL0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXdCO1lBQ3pCO2dCQUFDO2dCQUFRO2dCQUFlO2dCQUFnQjtnQkFBa0I7YUFBWTtlQUNuRTFELFNBQVNDLGdCQUFnQixDQUFDaUUsR0FBRyxDQUFDLENBQUNTLEdBQUdLLFFBQVU7b0JBQzdDTCxFQUFFekUsS0FBSztvQkFDUHlFLEVBQUV4RSxTQUFTO29CQUNYd0UsRUFBRXZFLEtBQUssQ0FBQ3lELGNBQWMsQ0FBQztvQkFDdkJHLEtBQUtDLEtBQUssQ0FBQ1UsRUFBRXZFLEtBQUssR0FBR3VFLEVBQUV4RSxTQUFTLEVBQUUwRCxjQUFjLENBQUM7b0JBQ2pEbUIsUUFBUSxJQUFJLEdBQXdILE9BQXJILENBQUMsQ0FBRUwsRUFBRXZFLEtBQUssR0FBR0osU0FBU0MsZ0JBQWdCLENBQUMrRSxRQUFNLEVBQUUsQ0FBQzVFLEtBQUssSUFBSUosU0FBU0MsZ0JBQWdCLENBQUMrRSxRQUFNLEVBQUUsQ0FBQzVFLEtBQUssR0FBSSxHQUFFLEVBQUcyRCxPQUFPLENBQUMsSUFBRyxPQUFLO2lCQUMxSTtZQUNEO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFTO1lBQ1Y7Z0JBQUM7Z0JBQXNCSjthQUFlO1lBQ3RDO2dCQUFDO2dCQUF1QkMsV0FBV0MsY0FBYyxDQUFDO2FBQVM7WUFDM0Q7Z0JBQUM7Z0JBQXNCRyxLQUFLQyxLQUFLLENBQUNMLGFBQWEsR0FBR0MsY0FBYyxDQUFDO2FBQVM7WUFDMUU7Z0JBQUM7Z0JBQXNCQyxlQUFlQyxPQUFPLENBQUM7YUFBRztTQUNsRDtRQUVELE1BQU1nQixLQUFLakYsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ0s7UUFDbkM1RSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJeUIsSUFBSTtRQUVyQyxPQUFPekI7SUFDVDtJQUVBLE1BQU1MLHlCQUF5QixDQUFDdkM7UUFDOUIsTUFBTXVFLFdBQVdqRixTQUFTUSxlQUFlLENBQUMwRSxJQUFJLENBQUNWLENBQUFBLElBQUtBLEVBQUU5RCxXQUFXLEtBQUtBO1FBQ3RFLElBQUksQ0FBQ3VFLFVBQVU7WUFDYixNQUFNLElBQUlFLE1BQU07UUFDbEI7UUFFQSxNQUFNN0IsS0FBS3hELHVDQUFVLENBQUMwRCxRQUFRO1FBRTlCLDREQUE0RDtRQUM1RCxNQUFNNEIsZUFBZSxVQUFVLDhCQUE4Qjs7UUFDN0QsTUFBTUMsZUFBZUosU0FBU3BFLFNBQVMsR0FBR3VFO1FBQzFDLE1BQU1FLGFBQWFMLFNBQVNwRSxTQUFTLEdBQUcsT0FBT3VFLGFBQWEsd0JBQXdCOztRQUNwRixNQUFNRyxjQUFjRixlQUFlQztRQUVuQyx3R0FBd0c7UUFDeEcsc0RBQXNEO1FBQ3RELE1BQU1FLG9CQUFvQixPQUFPLGlDQUFpQzs7UUFDbEUsTUFBTUMsb0JBQW9CLE1BQU0seURBQXlEOztRQUN6RixNQUFNQyxtQkFBbUJGLG9CQUFvQkM7UUFFN0MsTUFBTUUsdUJBQXVCLE9BQU8saUNBQWlDOztRQUNyRSxNQUFNQyx1QkFBdUIsTUFBTSwwQkFBMEI7O1FBQzdELE1BQU1DLHNCQUFzQkYsdUJBQXVCQztRQUVuRCxNQUFNRSxtQkFBbUJOLG9CQUFvQkc7UUFDN0MsTUFBTUksa0JBQWtCTCxtQkFBbUJHO1FBRTNDLGtFQUFrRTtRQUNsRSxNQUFNRyxvQkFBb0IsT0FBTyxpQ0FBaUM7O1FBQ2xFLE1BQU1DLG9CQUFvQixPQUFPLDBCQUEwQjs7UUFDM0QsTUFBTUMsbUJBQW1CRixvQkFBb0JDO1FBRTdDLE1BQU1FLHFCQUFxQixPQUFPLGlDQUFpQzs7UUFDbkUsTUFBTUMscUJBQXFCLE1BQU0sMEJBQTBCOztRQUMzRCxNQUFNQyxvQkFBb0JGLHFCQUFxQkM7UUFFL0MsTUFBTUUsY0FBYyxPQUFPLDZEQUE2RDs7UUFFeEYsTUFBTUMsdUJBQXVCUCxvQkFBb0JHLHFCQUFxQkc7UUFDdEUsTUFBTUUsc0JBQXNCTixtQkFBbUJHLG9CQUFvQkM7UUFFbkUseUVBQXlFO1FBQ3pFLE1BQU1HLGtCQUFrQixPQUFPLGlDQUFpQzs7UUFDaEUsTUFBTUMsa0JBQWtCLE1BQU0sMEJBQTBCOztRQUN4RCxNQUFNQyxpQkFBaUJGLGtCQUFrQkM7UUFFekMsc0VBQXNFO1FBQ3RFLE1BQU1FLHFCQUFxQixPQUFPLGlDQUFpQzs7UUFDbkUsTUFBTUMscUJBQXFCLE1BQU0sMEJBQTBCOztRQUMzRCxNQUFNQyxvQkFBb0JGLHFCQUFxQkM7UUFFL0Msa0RBQWtEO1FBQ2xELE1BQU1FLGdCQUFnQnhCLGNBQWNPLG1CQUFtQlMsdUJBQXVCRSxrQkFBa0JHO1FBQ2hHLE1BQU1JLGVBQWV6QixjQUFjUSxrQkFBa0JTLHNCQUFzQkcsaUJBQWlCRztRQUU1RixNQUFNRyxlQUFlO1lBQ25CO2dCQUFFLDZCQUFxQyxPQUFadkc7YUFBYztZQUN6QztnQkFBQztnQkFBdUIsSUFBSThCLE9BQU9rQixrQkFBa0IsQ0FBQzthQUFTO1lBQy9EO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUF5QjtZQUMxQjtnQkFBQztnQkFBZ0J1QixTQUFTdkUsV0FBVzthQUFDO1lBQ3RDO2dCQUFDO2dCQUFnQnVFLFNBQVN0RSxRQUFRO2FBQUM7WUFDbkM7Z0JBQUM7Z0JBQVNzRSxTQUFTckUsSUFBSTthQUFDO1lBQ3hCO2dCQUFDO2dCQUFXcUUsU0FBU2pFLE1BQU07YUFBQztZQUM1QjtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBbUI7WUFDcEI7Z0JBQUM7Z0JBQXNCaUUsU0FBU3BFLFNBQVMsQ0FBQ2dELGNBQWMsQ0FBQzthQUFTO1lBQ2xFO2dCQUFDO2dCQUE2QnVCLGFBQWFyQixPQUFPLENBQUM7YUFBRztZQUN0RDtnQkFBQztnQkFBc0JzQixhQUFheEIsY0FBYyxDQUFDO2FBQVM7WUFDNUQ7Z0JBQUM7Z0JBQWV5QixXQUFXekIsY0FBYyxDQUFDO2FBQVM7WUFDbkQ7Z0JBQUM7Z0JBQXlDMEIsWUFBWTFCLGNBQWMsQ0FBQzthQUFTO1lBQzlFO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFpRDtZQUNsRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBZ0M7WUFDakM7Z0JBQUM7Z0JBQStCMkIsa0JBQWtCM0IsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNuRjtnQkFBQztnQkFBZ0M0QixrQkFBa0I1QixjQUFjLENBQUMsV0FBVzthQUFPO1lBQ3BGO2dCQUFDO2dCQUFxQjZCLGlCQUFpQjdCLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDeEU7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQW9DO1lBQ3JDO2dCQUFDO2dCQUErQjhCLHFCQUFxQjlCLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDdEY7Z0JBQUM7Z0JBQWdDK0IscUJBQXFCL0IsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUN2RjtnQkFBQztnQkFBcUJnQyxvQkFBb0JoQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQzNFO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUE4QjtZQUMvQjtnQkFBQztnQkFBVWlDLGlCQUFpQmpDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDN0Q7Z0JBQUM7Z0JBQVNrQyxnQkFBZ0JsQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQzNEO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUEyQztZQUM1QztnQkFBQztnQkFBK0JtQyxrQkFBa0JuQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ25GO2dCQUFDO2dCQUFnQ29DLGtCQUFrQnBDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDcEY7Z0JBQUM7Z0JBQXFCcUMsaUJBQWlCckMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUN4RTtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBNEM7WUFDN0M7Z0JBQUM7Z0JBQStCc0MsbUJBQW1CdEMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNwRjtnQkFBQztnQkFBZ0N1QyxtQkFBbUJ2QyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ3JGO2dCQUFDO2dCQUFxQndDLGtCQUFrQnhDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDekU7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXVDO1lBQ3hDO2dCQUFDO2dCQUEwQ3lDLFlBQVl6QyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ3hGO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUEwQztZQUMzQztnQkFBQztnQkFBVTBDLHFCQUFxQjFDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDakU7Z0JBQUM7Z0JBQVMyQyxvQkFBb0IzQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQy9EO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFnQztZQUNqQztnQkFBQztnQkFBK0I0QyxnQkFBZ0I1QyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ2pGO2dCQUFDO2dCQUFnQzZDLGdCQUFnQjdDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDbEY7Z0JBQUM7Z0JBQXFCOEMsZUFBZTlDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDdEU7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQTZCO1lBQzlCO2dCQUFDO2dCQUErQitDLG1CQUFtQi9DLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDcEY7Z0JBQUM7Z0JBQWdDZ0QsbUJBQW1CaEQsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNyRjtnQkFBQztnQkFBcUJpRCxrQkFBa0JqRCxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ3pFO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFpQjtZQUNsQjtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7Z0JBQW9Ca0QsY0FBY2xELGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDcEU7Z0JBQUM7YUFBeUY7WUFDMUY7Z0JBQUMsU0FBUzBCLFlBQVkxQixjQUFjLENBQUMsV0FBVyxRQUFRaUMsaUJBQWlCakMsY0FBYyxDQUFDLFdBQVcsUUFBUTBDLHFCQUFxQjFDLGNBQWMsQ0FBQyxXQUFXLFFBQVE0QyxnQkFBZ0I1QyxjQUFjLENBQUMsV0FBVyxRQUFRK0MsbUJBQW1CL0MsY0FBYyxDQUFDO2FBQVM7WUFDL1A7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2dCQUFtQm1ELGFBQWFuRCxjQUFjLENBQUMsV0FBVzthQUFPO1lBQ2xFO2dCQUFDO2FBQXFGO1lBQ3RGO2dCQUFDLFNBQVMwQixZQUFZMUIsY0FBYyxDQUFDLFdBQVcsUUFBUWtDLGdCQUFnQmxDLGNBQWMsQ0FBQyxXQUFXLFFBQVEyQyxvQkFBb0IzQyxjQUFjLENBQUMsV0FBVyxRQUFROEMsZUFBZTlDLGNBQWMsQ0FBQyxXQUFXLFFBQVFpRCxrQkFBa0JqRCxjQUFjLENBQUM7YUFBUztZQUMzUDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7Z0JBQXFCa0QsQ0FBQUEsZ0JBQWdCMUIsWUFBVyxFQUFHdEIsT0FBTyxDQUFDO2FBQUc7WUFDL0Q7Z0JBQUM7Z0JBQW9CaUQsQ0FBQUEsZUFBZTNCLFlBQVcsRUFBR3RCLE9BQU8sQ0FBQzthQUFHO1lBQzdEO2dCQUFDO2dCQUF3QixHQUEwRCxPQUF4RCxDQUFDLENBQUMsZ0JBQWlCc0IsZUFBZ0IsS0FBSyxHQUFFLEVBQUd0QixPQUFPLENBQUMsSUFBRzthQUFHO1lBQ3RGO2dCQUFDO2dCQUF1QixHQUF5RCxPQUF2RCxDQUFDLENBQUMsZUFBZ0JzQixlQUFnQixLQUFLLEdBQUUsRUFBR3RCLE9BQU8sQ0FBQyxJQUFHO2FBQUc7U0FDckY7UUFFRCxNQUFNZ0IsS0FBS2pGLHVDQUFVLENBQUN1RSxZQUFZLENBQUM0QztRQUNuQ25ILHVDQUFVLENBQUN3RSxpQkFBaUIsQ0FBQ2hCLElBQUl5QixJQUFJLGlCQUEwQixPQUFackU7UUFFbkQsT0FBTzRDO0lBQ1Q7SUFFQSxNQUFNd0IsMEJBQTBCLENBQUNvQztRQUMvQixNQUFNQyxlQUEwQztZQUM5QyxjQUFjO1lBQ2Qsa0JBQWtCO1lBQ2xCLGFBQWE7WUFDYixpQkFBaUI7WUFDakIsZUFBZTtRQUNqQjtRQUNBLE9BQU9BLFlBQVksQ0FBQ0QsVUFBVSxJQUFJO0lBQ3BDO0lBRUEsSUFBSWxHLFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQzNDLGlFQUFNQTtzQkFDTCw0RUFBQytJO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztJQUl2QjtJQUVBLE1BQU0xRCxpQkFBaUIzRCxTQUFTQyxnQkFBZ0IsQ0FBQ3FILE1BQU0sQ0FBQyxDQUFDQyxLQUFLcEQsT0FBU29ELE1BQU1wRCxLQUFLaEUsU0FBUyxFQUFFO0lBQzdGLE1BQU15RCxhQUFhNUQsU0FBU0MsZ0JBQWdCLENBQUNxSCxNQUFNLENBQUMsQ0FBQ0MsS0FBS3BELE9BQVNvRCxNQUFNcEQsS0FBSy9ELEtBQUssRUFBRTtJQUNyRixNQUFNMEQsaUJBQWlCO0lBRXZCLHFCQUNFLDhEQUFDekYsaUVBQU1BO2tCQUNMLDRFQUFDK0k7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDSTtvQ0FBR0gsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FDakQsOERBQUNJO29DQUFFSixXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7O3NDQUUvQiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3pJLHVEQUFLQTt3Q0FDSjhJLE1BQUs7d0NBQ0x0SCxPQUFPa0IsVUFBVUUsSUFBSTt3Q0FDckJtRyxVQUFVLENBQUNDLElBQU1yRyxhQUFhc0csQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFckcsTUFBTW9HLEVBQUVFLE1BQU0sQ0FBQzFILEtBQUs7Z0RBQUM7d0NBQ3ZFaUgsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDekksdURBQUtBO3dDQUNKOEksTUFBSzt3Q0FDTHRILE9BQU9rQixVQUFVRyxFQUFFO3dDQUNuQmtHLFVBQVUsQ0FBQ0MsSUFBTXJHLGFBQWFzRyxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVwRyxJQUFJbUcsRUFBRUUsTUFBTSxDQUFDMUgsS0FBSztnREFBQzt3Q0FDckVpSCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPbEIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQy9JLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTtvQ0FBQzRJLFdBQVU7O3NEQUNwQiw4REFBQzNJLDBEQUFTQTs0Q0FBQzJJLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzNDLDhEQUFDbkkscUpBQU9BOzRDQUFDbUksV0FBVTs7Ozs7Ozs7Ozs7OzhDQUVyQiw4REFBQzlJLDREQUFXQTs7c0RBQ1YsOERBQUM2STs0Q0FBSUMsV0FBVTtzREFBc0IxRDs7Ozs7O3NEQUNyQyw4REFBQzhEOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1qRCw4REFBQy9JLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTtvQ0FBQzRJLFdBQVU7O3NEQUNwQiw4REFBQzNJLDBEQUFTQTs0Q0FBQzJJLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzNDLDhEQUFDcEksd0pBQVVBOzRDQUFDb0ksV0FBVTs7Ozs7Ozs7Ozs7OzhDQUV4Qiw4REFBQzlJLDREQUFXQTs7c0RBQ1YsOERBQUM2STs0Q0FBSUMsV0FBVTtzREFBc0J6RixlQUFlZ0M7Ozs7OztzREFDcEQsOERBQUM2RDs0Q0FBRUosV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNakQsOERBQUMvSSxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7b0NBQUM0SSxXQUFVOztzREFDcEIsOERBQUMzSSwwREFBU0E7NENBQUMySSxXQUFVO3NEQUFzQjs7Ozs7O3NEQUMzQyw4REFBQ3JJLHdKQUFVQTs0Q0FBQ3FJLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFeEIsOERBQUM5SSw0REFBV0E7O3NEQUNWLDhEQUFDNkk7NENBQUlDLFdBQVU7O2dEQUFzQnZELGVBQWVDLE9BQU8sQ0FBQztnREFBRzs7Ozs7OztzREFDL0QsOERBQUMwRDs0Q0FBRUosV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNakQsOERBQUMvSSxxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7b0NBQUM0SSxXQUFVOztzREFDcEIsOERBQUMzSSwwREFBU0E7NENBQUMySSxXQUFVO3NEQUFzQjs7Ozs7O3NEQUMzQyw4REFBQ3RJLHNKQUFRQTs0Q0FBQ3NJLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFdEIsOERBQUM5SSw0REFBV0E7O3NEQUNWLDhEQUFDNkk7NENBQUlDLFdBQVU7c0RBQXNCckQsS0FBS0MsS0FBSyxDQUFDTixpQkFBaUI7Ozs7OztzREFDakUsOERBQUM4RDs0Q0FBRUosV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRbkQsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQy9JLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTs7c0RBQ1QsOERBQUNDLDBEQUFTQTs0Q0FBQzJJLFdBQVU7OzhEQUNuQiw4REFBQ2xJLHVKQUFTQTtvREFBQ2tJLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBR3hDLDhEQUFDN0ksZ0VBQWVBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBSW5CLDhEQUFDRCw0REFBV0E7OENBQ1YsNEVBQUNvQixrS0FBbUJBO3dDQUFDb0ksT0FBTTt3Q0FBT0MsUUFBUTtrREFDeEMsNEVBQUMzSSx1SkFBUUE7NENBQUM4QixNQUFNbkIsU0FBU0MsZ0JBQWdCOzs4REFDdkMsOERBQUNSLDRKQUFhQTtvREFBQ3dJLGlCQUFnQjs7Ozs7OzhEQUMvQiw4REFBQzFJLG9KQUFLQTtvREFBQzJJLFNBQVE7Ozs7Ozs4REFDZiw4REFBQzFJLG9KQUFLQTs7Ozs7OERBQ04sOERBQUNFLHNKQUFPQTtvREFDTnlJLFdBQVcsQ0FBQy9ILE9BQU9FLE9BQVM7NERBQzFCQSxTQUFTLGNBQWNGLFFBQVF3QixlQUFleEI7NERBQzlDRSxTQUFTLGNBQWMsY0FBYzt5REFDdEM7Ozs7Ozs4REFFSCw4REFBQ2hCLGtKQUFHQTtvREFBQzRJLFNBQVE7b0RBQVlFLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3RDLDhEQUFDOUoscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBOztzREFDVCw4REFBQ0MsMERBQVNBOzRDQUFDMkksV0FBVTs7OERBQ25CLDhEQUFDakksc0pBQVFBO29EQUFDaUksV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7OztzREFHdkMsOERBQUM3SSxnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTs7c0RBQ1YsOERBQUNvQixrS0FBbUJBOzRDQUFDb0ksT0FBTTs0Q0FBT0MsUUFBUTtzREFDeEMsNEVBQUNwSSx1SkFBZ0JBOztrRUFDZiw4REFBQ0Ysc0pBQU9BO3dEQUNOeUksV0FBVyxDQUFDL0gsT0FBT0UsT0FBUztnRUFDekIsR0FBUSxPQUFORixPQUFNO2dFQUNURTs2REFDRDs7Ozs7O2tFQUVILDhEQUFDVix1SkFBZ0JBO3dEQUFDdUIsTUFBTW5CLFNBQVNLLGFBQWE7a0VBQzNDTCxTQUFTSyxhQUFhLENBQUM2RCxHQUFHLENBQUMsQ0FBQ21FLE9BQU9yRCxzQkFDbEMsOERBQUNuRixtSkFBSUE7Z0VBQXVCdUksTUFBTW5ILE1BQU0sQ0FBQytELFFBQVEvRCxPQUFPcUgsTUFBTSxDQUFDOytEQUFwRCxRQUFjLE9BQU50RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUszQiw4REFBQ29DOzRDQUFJQyxXQUFVO3NEQUNackgsU0FBU0ssYUFBYSxDQUFDNkQsR0FBRyxDQUFDLENBQUNDLE1BQU1hLHNCQUNqQyw4REFBQ29DO29EQUFvQkMsV0FBVTs7c0VBQzdCLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUNDQyxXQUFVO29FQUNWdEYsT0FBTzt3RUFBRXdHLGlCQUFpQnRILE1BQU0sQ0FBQytELFFBQVEvRCxPQUFPcUgsTUFBTSxDQUFDO29FQUFDOzs7Ozs7OEVBRTFELDhEQUFDRTs4RUFBTXJFLEtBQUs3RCxJQUFJOzs7Ozs7Ozs7Ozs7c0VBRWxCLDhEQUFDa0k7NERBQUtuQixXQUFVOztnRUFBZWxELEtBQUsvRCxLQUFLO2dFQUFDOzs7Ozs7OzttREFSbEMrRCxLQUFLN0QsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFpQjdCLDhEQUFDaEMscURBQUlBOztzQ0FDSCw4REFBQ0csMkRBQVVBOzs4Q0FDVCw4REFBQ0MsMERBQVNBOzhDQUFDOzs7Ozs7OENBQ1gsOERBQUNGLGdFQUFlQTs4Q0FBQzs7Ozs7Ozs7Ozs7O3NDQUluQiw4REFBQ0QsNERBQVdBO3NDQUNWLDRFQUFDNkk7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDMUkseURBQU1BO3dDQUNMOEosU0FBUTt3Q0FDUnBCLFdBQVU7d0NBQ1ZxQixTQUFTLElBQU1yRyxtQkFBbUI7OzBEQUVsQyw4REFBQ3hELHNKQUFRQTtnREFBQ3dJLFdBQVU7Ozs7OzswREFDcEIsOERBQUNtQjswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTtnREFBS25CLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBRzFDLDhEQUFDMUkseURBQU1BO3dDQUNMOEosU0FBUTt3Q0FDUnBCLFdBQVU7d0NBQ1ZxQixTQUFTLElBQU1yRyxtQkFBbUI7OzBEQUVsQyw4REFBQ2xELHVKQUFTQTtnREFBQ2tJLFdBQVU7Ozs7OzswREFDckIsOERBQUNtQjswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTtnREFBS25CLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBRzFDLDhEQUFDMUkseURBQU1BO3dDQUNMOEosU0FBUTt3Q0FDUnBCLFdBQVU7d0NBQ1ZxQixTQUFTLElBQU1yRyxtQkFBbUI7OzBEQUVsQyw4REFBQ3RELHNKQUFRQTtnREFBQ3NJLFdBQVU7Ozs7OzswREFDcEIsOERBQUNtQjswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTtnREFBS25CLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPaEQsOERBQUMvSSxxREFBSUE7O3NDQUNILDhEQUFDRywyREFBVUE7OzhDQUNULDhEQUFDQywwREFBU0E7OENBQUM7Ozs7Ozs4Q0FDWCw4REFBQ0YsZ0VBQWVBOzhDQUFDOzs7Ozs7Ozs7Ozs7c0NBSW5CLDhEQUFDRCw0REFBV0E7c0NBQ1YsNEVBQUM2STtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3NCO29DQUFNdEIsV0FBVTs7c0RBQ2YsOERBQUN1QjtzREFDQyw0RUFBQ0M7Z0RBQUd4QixXQUFVOztrRUFDWiw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBc0I7Ozs7OztrRUFDcEMsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQXNCOzs7Ozs7a0VBQ3BDLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUFzQjs7Ozs7O2tFQUNwQyw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBdUI7Ozs7OztrRUFDckMsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQXVCOzs7Ozs7a0VBQ3JDLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUF1Qjs7Ozs7O2tFQUNyQyw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBc0I7Ozs7OztrRUFDcEMsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs7Ozs7OztzREFHeEMsOERBQUMwQjtzREFDRS9JLFNBQVNRLGVBQWUsQ0FBQzBELEdBQUcsQ0FBQyxDQUFDZSx5QkFDN0IsOERBQUM0RDtvREFBcUJ4QixXQUFVOztzRUFDOUIsOERBQUMyQjs0REFBRzNCLFdBQVU7c0VBQXlCcEMsU0FBU3ZFLFdBQVc7Ozs7OztzRUFDM0QsOERBQUNzSTs0REFBRzNCLFdBQVU7c0VBQWFwQyxTQUFTdEUsUUFBUTs7Ozs7O3NFQUM1Qyw4REFBQ3FJOzREQUFHM0IsV0FBVTtzRUFBYSxJQUFJN0UsS0FBS3lDLFNBQVNyRSxJQUFJLEVBQUU4QyxrQkFBa0I7Ozs7OztzRUFDckUsOERBQUNzRjs0REFBRzNCLFdBQVU7c0VBQXdCakYsVUFBVTZDLFNBQVNwRSxTQUFTOzs7Ozs7c0VBQ2xFLDhEQUFDbUk7NERBQUczQixXQUFVO3NFQUF3QnpGLGVBQWVxRCxTQUFTbkUsVUFBVTs7Ozs7O3NFQUN4RSw4REFBQ2tJOzREQUFHM0IsV0FBVTs7Z0VBQXdCcEMsU0FBU2xFLFdBQVcsQ0FBQ2dELE9BQU8sQ0FBQztnRUFBRzs7Ozs7OztzRUFDdEUsOERBQUNpRjs0REFBRzNCLFdBQVU7c0VBQ1osNEVBQUNtQjtnRUFBS25CLFdBQVcsMkVBSWhCLE9BSENwQyxTQUFTakUsTUFBTSxLQUFLLGNBQ2hCLGdDQUNBOzBFQUVIaUUsU0FBU2pFLE1BQU07Ozs7Ozs7Ozs7O3NFQUdwQiw4REFBQ2dJOzREQUFHM0IsV0FBVTtzRUFDWiw0RUFBQzFJLHlEQUFNQTtnRUFDTHNLLE1BQUs7Z0VBQ0xSLFNBQVE7Z0VBQ1JDLFNBQVMsSUFBTXJHLG1CQUFtQixZQUFpQyxPQUFyQjRDLFNBQVN2RSxXQUFXOztrRkFFbEUsOERBQUM1QixzSkFBUUE7d0VBQUN1SSxXQUFVOzs7Ozs7b0VBQWlCOzs7Ozs7Ozs7Ozs7O21EQXRCbENwQyxTQUFTeEUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW9DeEM7R0F2bEJ3QlM7O1FBQ1kvQyx1REFBVUE7UUFDN0JDLGtEQUFTQTs7O0tBRkY4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9yZXBvcnRzLnRzeD9kZGJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvcm91dGVyJ1xuaW1wb3J0IExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0xheW91dCdcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xuaW1wb3J0IHtcbiAgRmlsZVRleHQsXG4gIERvd25sb2FkLFxuICBDYWxlbmRhcixcbiAgVHJlbmRpbmdVcCxcbiAgRG9sbGFyU2lnbixcbiAgUGFja2FnZSxcbiAgQmFyQ2hhcnQzLFxuICBQaWVDaGFydFxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBCYXJDaGFydCwgQmFyLCBYQXhpcywgWUF4aXMsIENhcnRlc2lhbkdyaWQsIFRvb2x0aXAsIFJlc3BvbnNpdmVDb250YWluZXIsIFBpZUNoYXJ0IGFzIFJlY2hhcnRzUGllQ2hhcnQsIENlbGwgfSBmcm9tICdyZWNoYXJ0cydcbmltcG9ydCAqIGFzIFhMU1ggZnJvbSAneGxzeCdcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmNvbnN0IG1vY2tEYXRhID0ge1xuICBtb250aGx5U2hpcG1lbnRzOiBbXG4gICAgeyBtb250aDogJ0phbicsIHNoaXBtZW50czogMTIsIHZhbHVlOiAxMjAwMDAwIH0sXG4gICAgeyBtb250aDogJ0ZlYicsIHNoaXBtZW50czogOCwgdmFsdWU6IDg1MDAwMCB9LFxuICAgIHsgbW9udGg6ICdNYXInLCBzaGlwbWVudHM6IDE1LCB2YWx1ZTogMTY1MDAwMCB9LFxuICAgIHsgbW9udGg6ICdBcHInLCBzaGlwbWVudHM6IDEwLCB2YWx1ZTogMTEwMDAwMCB9LFxuICAgIHsgbW9udGg6ICdNYXknLCBzaGlwbWVudHM6IDE4LCB2YWx1ZTogMjEwMDAwMCB9LFxuICAgIHsgbW9udGg6ICdKdW4nLCBzaGlwbWVudHM6IDE0LCB2YWx1ZTogMTgwMDAwMCB9XG4gIF0sXG4gIGNvc3RCcmVha2Rvd246IFtcbiAgICB7IG5hbWU6ICdGT0IgQW1vdW50JywgdmFsdWU6IDY1LCBhbW91bnQ6IDY1MDAwMDAgfSxcbiAgICB7IG5hbWU6ICdDdXN0b21zIER1dGllcycsIHZhbHVlOiAxNSwgYW1vdW50OiAxNTAwMDAwIH0sXG4gICAgeyBuYW1lOiAnUG9ydCBGZWVzJywgdmFsdWU6IDgsIGFtb3VudDogODAwMDAwIH0sXG4gICAgeyBuYW1lOiAnU2hpcHBpbmcgRmVlcycsIHZhbHVlOiA3LCBhbW91bnQ6IDcwMDAwMCB9LFxuICAgIHsgbmFtZTogJ090aGVyIENvc3RzJywgdmFsdWU6IDUsIGFtb3VudDogNTAwMDAwIH1cbiAgXSxcbiAgcmVjZW50U2hpcG1lbnRzOiBbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIG9yZGVyTnVtYmVyOiAnT1JELTIwMjQtMDAxJyxcbiAgICAgIHN1cHBsaWVyOiAnQUJDIE1vdG9ycyBMdGQnLFxuICAgICAgZGF0ZTogJzIwMjQtMDEtMTUnLFxuICAgICAgZm9iQW1vdW50OiAxMjUwMDAsXG4gICAgICBsYW5kZWRDb3N0OiAxNjg3NTAsXG4gICAgICBjb2VmZmljaWVudDogMS4zNSxcbiAgICAgIHN0YXR1czogJ0NvbXBsZXRlZCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMicsXG4gICAgICBvcmRlck51bWJlcjogJ09SRC0yMDI0LTAwMicsXG4gICAgICBzdXBwbGllcjogJ1hZWiBQYXJ0cyBDbycsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0xMicsXG4gICAgICBmb2JBbW91bnQ6IDg5MDAwLFxuICAgICAgbGFuZGVkQ29zdDogMTIwMTUwLFxuICAgICAgY29lZmZpY2llbnQ6IDEuMzUsXG4gICAgICBzdGF0dXM6ICdDb21wbGV0ZWQnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzMnLFxuICAgICAgb3JkZXJOdW1iZXI6ICdPUkQtMjAyNC0wMDMnLFxuICAgICAgc3VwcGxpZXI6ICdHbG9iYWwgU3VwcGx5IEluYycsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0xMCcsXG4gICAgICBmb2JBbW91bnQ6IDE1NjAwMCxcbiAgICAgIGxhbmRlZENvc3Q6IDIxMDYwMCxcbiAgICAgIGNvZWZmaWNpZW50OiAxLjM1LFxuICAgICAgc3RhdHVzOiAnSW4gUHJvZ3Jlc3MnXG4gICAgfVxuICBdXG59XG5cbmNvbnN0IENPTE9SUyA9IFsnIzNiODJmNicsICcjZWY0NDQ0JywgJyNmNTllMGInLCAnIzEwYjk4MScsICcjOGI1Y2Y2J11cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVwb3J0cygpIHtcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBbZGF0ZVJhbmdlLCBzZXREYXRlUmFuZ2VdID0gdXNlU3RhdGUoe1xuICAgIGZyb206ICcyMDI0LTAxLTAxJyxcbiAgICB0bzogJzIwMjQtMDYtMzAnXG4gIH0pXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgICAgcm91dGVyLnB1c2goJy9hdXRoL3NpZ25pbicpXG4gICAgfVxuICB9LCBbc3RhdHVzLCByb3V0ZXJdKVxuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZnItRlInLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnRFpEJyxcbiAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMCxcbiAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMFxuICAgIH0pLmZvcm1hdChhbW91bnQpXG4gIH1cblxuICBjb25zdCBmb3JtYXRVU0QgPSAoYW1vdW50OiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1VUycsIHtcbiAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgY3VycmVuY3k6ICdVU0QnLFxuICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAwLFxuICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAwXG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydFJlcG9ydCA9IChyZXBvcnRUeXBlOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZmlsZU5hbWUgPSBgJHtyZXBvcnRUeXBlfV9SZXBvcnRfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0ueGxzeGBcbiAgICAgIGxldCB3b3JrYm9vazogWExTWC5Xb3JrQm9va1xuXG4gICAgICBpZiAocmVwb3J0VHlwZSA9PT0gJ0NvbXByZWhlbnNpdmUnKSB7XG4gICAgICAgIHdvcmtib29rID0gZ2VuZXJhdGVDb21wcmVoZW5zaXZlUmVwb3J0KClcbiAgICAgIH0gZWxzZSBpZiAocmVwb3J0VHlwZSA9PT0gJ0Nvc3RfQW5hbHlzaXMnKSB7XG4gICAgICAgIHdvcmtib29rID0gZ2VuZXJhdGVDb3N0QW5hbHlzaXNSZXBvcnQoKVxuICAgICAgfSBlbHNlIGlmIChyZXBvcnRUeXBlID09PSAnTW9udGhseV9TdW1tYXJ5Jykge1xuICAgICAgICB3b3JrYm9vayA9IGdlbmVyYXRlTW9udGhseVN1bW1hcnlSZXBvcnQoKVxuICAgICAgfSBlbHNlIGlmIChyZXBvcnRUeXBlLnN0YXJ0c1dpdGgoJ1NoaXBtZW50XycpKSB7XG4gICAgICAgIGNvbnN0IG9yZGVyTnVtYmVyID0gcmVwb3J0VHlwZS5yZXBsYWNlKCdTaGlwbWVudF8nLCAnJylcbiAgICAgICAgd29ya2Jvb2sgPSBnZW5lcmF0ZVNoaXBtZW50UmVwb3J0KG9yZGVyTnVtYmVyKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgd29ya2Jvb2sgPSBnZW5lcmF0ZUNvbXByZWhlbnNpdmVSZXBvcnQoKVxuICAgICAgfVxuXG4gICAgICAvLyBHZW5lcmF0ZSBhbmQgZG93bmxvYWQgdGhlIGZpbGVcbiAgICAgIFhMU1gud3JpdGVGaWxlKHdvcmtib29rLCBmaWxlTmFtZSlcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYFJhcHBvcnQgXCIke2ZpbGVOYW1lfVwiIGfDqW7DqXLDqSBldCB0w6lsw6ljaGFyZ8OpIGF2ZWMgc3VjY8OocyFgKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIHJlcG9ydDonLCBlcnJvcilcbiAgICAgIHRvYXN0LmVycm9yKCdFcnJldXIgbG9ycyBkZSBsYSBnw6luw6lyYXRpb24gZHUgcmFwcG9ydCcpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2VuZXJhdGVDb21wcmVoZW5zaXZlUmVwb3J0ID0gKCk6IFhMU1guV29ya0Jvb2sgPT4ge1xuICAgIGNvbnN0IHdiID0gWExTWC51dGlscy5ib29rX25ldygpXG5cbiAgICAvLyBTdW1tYXJ5IHNoZWV0XG4gICAgY29uc3Qgc3VtbWFyeURhdGEgPSBbXG4gICAgICBbJ1JBUFBPUlQgQ09NUExFVCAtIElNUE9SVCAmIExPR0lTVElDUyBDQUxDVUxBVE9SJ10sXG4gICAgICBbJ1DDqXJpb2RlOicsIGAke2RhdGVSYW5nZS5mcm9tfSDDoCAke2RhdGVSYW5nZS50b31gXSxcbiAgICAgIFsnRGF0ZSBkZSBnw6luw6lyYXRpb246JywgbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoJ2ZyLUZSJyldLFxuICAgICAgWycnXSxcbiAgICAgIFsnUsOJU1VNw4kgRVjDiUNVVElGJ10sXG4gICAgICBbJ1RvdGFsIGRlcyBleHDDqWRpdGlvbnM6JywgdG90YWxTaGlwbWVudHNdLFxuICAgICAgWydWYWxldXIgdG90YWxlIChEWkQpOicsIHRvdGFsVmFsdWUudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJyldLFxuICAgICAgWydDb2VmZmljaWVudCBtb3llbjonLCBhdmdDb2VmZmljaWVudC50b0ZpeGVkKDMpXSxcbiAgICAgIFsnRXhww6lkaXRpb25zIHBhciBtb2lzOicsIE1hdGgucm91bmQodG90YWxTaGlwbWVudHMgLyA2KV0sXG4gICAgICBbJyddLFxuICAgICAgWydSw4lQQVJUSVRJT04gREVTIENPw5tUUyddLFxuICAgICAgLi4ubW9ja0RhdGEuY29zdEJyZWFrZG93bi5tYXAoaXRlbSA9PiBbaXRlbS5uYW1lLCBgJHtpdGVtLnZhbHVlfSVgLCBpdGVtLmFtb3VudC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10pXG4gICAgXVxuXG4gICAgY29uc3Qgc3VtbWFyeVdzID0gWExTWC51dGlscy5hb2FfdG9fc2hlZXQoc3VtbWFyeURhdGEpXG4gICAgWExTWC51dGlscy5ib29rX2FwcGVuZF9zaGVldCh3Yiwgc3VtbWFyeVdzLCAnUsOpc3Vtw6knKVxuXG4gICAgLy8gU2hpcG1lbnRzIGRldGFpbCBzaGVldFxuICAgIGNvbnN0IHNoaXBtZW50c0RhdGEgPSBbXG4gICAgICBbJ07CsCBDb21tYW5kZScsICdGb3Vybmlzc2V1cicsICdEYXRlJywgJ01vbnRhbnQgRk9CIChVU0QpJywgJ0Nvw7t0IETDqWJhcnF1w6kgKERaRCknLCAnQ29lZmZpY2llbnQnLCAnU3RhdHV0J10sXG4gICAgICAuLi5tb2NrRGF0YS5yZWNlbnRTaGlwbWVudHMubWFwKHMgPT4gW1xuICAgICAgICBzLm9yZGVyTnVtYmVyLFxuICAgICAgICBzLnN1cHBsaWVyLFxuICAgICAgICBzLmRhdGUsXG4gICAgICAgIHMuZm9iQW1vdW50LFxuICAgICAgICBzLmxhbmRlZENvc3QsXG4gICAgICAgIHMuY29lZmZpY2llbnQudG9GaXhlZCgzKSxcbiAgICAgICAgcy5zdGF0dXNcbiAgICAgIF0pXG4gICAgXVxuXG4gICAgY29uc3Qgc2hpcG1lbnRzV3MgPSBYTFNYLnV0aWxzLmFvYV90b19zaGVldChzaGlwbWVudHNEYXRhKVxuICAgIFhMU1gudXRpbHMuYm9va19hcHBlbmRfc2hlZXQod2IsIHNoaXBtZW50c1dzLCAnRMOpdGFpbCBFeHDDqWRpdGlvbnMnKVxuXG4gICAgLy8gTW9udGhseSBkYXRhIHNoZWV0XG4gICAgY29uc3QgbW9udGhseURhdGEgPSBbXG4gICAgICBbJ01vaXMnLCAnTm9tYnJlIGRcXCdleHDDqWRpdGlvbnMnLCAnVmFsZXVyIChEWkQpJ10sXG4gICAgICAuLi5tb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzLm1hcChtID0+IFttLm1vbnRoLCBtLnNoaXBtZW50cywgbS52YWx1ZV0pXG4gICAgXVxuXG4gICAgY29uc3QgbW9udGhseVdzID0gWExTWC51dGlscy5hb2FfdG9fc2hlZXQobW9udGhseURhdGEpXG4gICAgWExTWC51dGlscy5ib29rX2FwcGVuZF9zaGVldCh3YiwgbW9udGhseVdzLCAnRG9ubsOpZXMgTWVuc3VlbGxlcycpXG5cbiAgICByZXR1cm4gd2JcbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlQ29zdEFuYWx5c2lzUmVwb3J0ID0gKCk6IFhMU1guV29ya0Jvb2sgPT4ge1xuICAgIGNvbnN0IHdiID0gWExTWC51dGlscy5ib29rX25ldygpXG5cbiAgICBjb25zdCBjb3N0RGF0YSA9IFtcbiAgICAgIFsnQU5BTFlTRSBERVMgQ0/Dm1RTIC0gSU1QT1JUICYgTE9HSVNUSUNTIENBTENVTEFUT1InXSxcbiAgICAgIFsnUMOpcmlvZGU6JywgYCR7ZGF0ZVJhbmdlLmZyb219IMOgICR7ZGF0ZVJhbmdlLnRvfWBdLFxuICAgICAgWydEYXRlIGRlIGfDqW7DqXJhdGlvbjonLCBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydSw4lQQVJUSVRJT04gRMOJVEFJTEzDiUUgREVTIENPw5tUUyddLFxuICAgICAgWydDb21wb3NhbnQnLCAnUG91cmNlbnRhZ2UnLCAnTW9udGFudCAoRFpEKScsICdEZXNjcmlwdGlvbiddLFxuICAgICAgLi4ubW9ja0RhdGEuY29zdEJyZWFrZG93bi5tYXAoaXRlbSA9PiBbXG4gICAgICAgIGl0ZW0ubmFtZSxcbiAgICAgICAgYCR7aXRlbS52YWx1ZX0lYCxcbiAgICAgICAgaXRlbS5hbW91bnQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJyksXG4gICAgICAgIGdldENvbXBvbmVudERlc2NyaXB0aW9uKGl0ZW0ubmFtZSlcbiAgICAgIF0pLFxuICAgICAgWycnXSxcbiAgICAgIFsnQU5BTFlTRSBQQVIgRVhQw4lESVRJT04nXSxcbiAgICAgIFsnTsKwIENvbW1hbmRlJywgJ0ZPQiAoVVNEKScsICdDb8O7dCBEw6liYXJxdcOpIChEWkQpJywgJ0NvZWZmaWNpZW50JywgJ01hcmdlIHZzIEZPQiddLFxuICAgICAgLi4ubW9ja0RhdGEucmVjZW50U2hpcG1lbnRzLm1hcChzID0+IFtcbiAgICAgICAgcy5vcmRlck51bWJlcixcbiAgICAgICAgcy5mb2JBbW91bnQudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJyksXG4gICAgICAgIHMubGFuZGVkQ29zdC50b0xvY2FsZVN0cmluZygnZnItRlInKSxcbiAgICAgICAgcy5jb2VmZmljaWVudC50b0ZpeGVkKDMpLFxuICAgICAgICBgJHsoKHMuY29lZmZpY2llbnQgLSAxKSAqIDEwMCkudG9GaXhlZCgxKX0lYFxuICAgICAgXSlcbiAgICBdXG5cbiAgICBjb25zdCB3cyA9IFhMU1gudXRpbHMuYW9hX3RvX3NoZWV0KGNvc3REYXRhKVxuICAgIFhMU1gudXRpbHMuYm9va19hcHBlbmRfc2hlZXQod2IsIHdzLCAnQW5hbHlzZSBDb8O7dHMnKVxuXG4gICAgcmV0dXJuIHdiXG4gIH1cblxuICBjb25zdCBnZW5lcmF0ZU1vbnRobHlTdW1tYXJ5UmVwb3J0ID0gKCk6IFhMU1guV29ya0Jvb2sgPT4ge1xuICAgIGNvbnN0IHdiID0gWExTWC51dGlscy5ib29rX25ldygpXG5cbiAgICBjb25zdCBtb250aGx5RGF0YSA9IFtcbiAgICAgIFsnUsOJU1VNw4kgTUVOU1VFTCAtIElNUE9SVCAmIExPR0lTVElDUyBDQUxDVUxBVE9SJ10sXG4gICAgICBbJ1DDqXJpb2RlOicsIGAke2RhdGVSYW5nZS5mcm9tfSDDoCAke2RhdGVSYW5nZS50b31gXSxcbiAgICAgIFsnRGF0ZSBkZSBnw6luw6lyYXRpb246JywgbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoJ2ZyLUZSJyldLFxuICAgICAgWycnXSxcbiAgICAgIFsnUEVSRk9STUFOQ0UgTUVOU1VFTExFJ10sXG4gICAgICBbJ01vaXMnLCAnRXhww6lkaXRpb25zJywgJ1ZhbGV1ciAoRFpEKScsICdWYWxldXIgTW95ZW5uZScsICfDiXZvbHV0aW9uJ10sXG4gICAgICAuLi5tb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzLm1hcCgobSwgaW5kZXgpID0+IFtcbiAgICAgICAgbS5tb250aCxcbiAgICAgICAgbS5zaGlwbWVudHMsXG4gICAgICAgIG0udmFsdWUudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJyksXG4gICAgICAgIE1hdGgucm91bmQobS52YWx1ZSAvIG0uc2hpcG1lbnRzKS50b0xvY2FsZVN0cmluZygnZnItRlInKSxcbiAgICAgICAgaW5kZXggPiAwID8gYCR7KCgobS52YWx1ZSAtIG1vY2tEYXRhLm1vbnRobHlTaGlwbWVudHNbaW5kZXgtMV0udmFsdWUpIC8gbW9ja0RhdGEubW9udGhseVNoaXBtZW50c1tpbmRleC0xXS52YWx1ZSkgKiAxMDApLnRvRml4ZWQoMSl9JWAgOiAnTi9BJ1xuICAgICAgXSksXG4gICAgICBbJyddLFxuICAgICAgWydUT1RBVVgnXSxcbiAgICAgIFsnVG90YWwgZXhww6lkaXRpb25zOicsIHRvdGFsU2hpcG1lbnRzXSxcbiAgICAgIFsnVG90YWwgdmFsZXVyIChEWkQpOicsIHRvdGFsVmFsdWUudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJyldLFxuICAgICAgWydNb3llbm5lIG1lbnN1ZWxsZTonLCBNYXRoLnJvdW5kKHRvdGFsVmFsdWUgLyA2KS50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJ0NvZWZmaWNpZW50IG1veWVuOicsIGF2Z0NvZWZmaWNpZW50LnRvRml4ZWQoMyldXG4gICAgXVxuXG4gICAgY29uc3Qgd3MgPSBYTFNYLnV0aWxzLmFvYV90b19zaGVldChtb250aGx5RGF0YSlcbiAgICBYTFNYLnV0aWxzLmJvb2tfYXBwZW5kX3NoZWV0KHdiLCB3cywgJ1LDqXN1bcOpIE1lbnN1ZWwnKVxuXG4gICAgcmV0dXJuIHdiXG4gIH1cblxuICBjb25zdCBnZW5lcmF0ZVNoaXBtZW50UmVwb3J0ID0gKG9yZGVyTnVtYmVyOiBzdHJpbmcpOiBYTFNYLldvcmtCb29rID0+IHtcbiAgICBjb25zdCBzaGlwbWVudCA9IG1vY2tEYXRhLnJlY2VudFNoaXBtZW50cy5maW5kKHMgPT4gcy5vcmRlck51bWJlciA9PT0gb3JkZXJOdW1iZXIpXG4gICAgaWYgKCFzaGlwbWVudCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdFeHDDqWRpdGlvbiBub24gdHJvdXbDqWUnKVxuICAgIH1cblxuICAgIGNvbnN0IHdiID0gWExTWC51dGlscy5ib29rX25ldygpXG5cbiAgICAvLyBDYWxjdWxzIGTDqXRhaWxsw6lzIGJhc8OpcyBzdXIgbGVzIGZvcm11bGVzIGRlIGwnYXBwbGljYXRpb25cbiAgICBjb25zdCBleGNoYW5nZVJhdGUgPSAxMzQuNTAwMDAgLy8gVGF1eCBkZSBjaGFuZ2UgVVNEIHZlcnMgRFpEXG4gICAgY29uc3QgZm9iQW1vdW50RHpkID0gc2hpcG1lbnQuZm9iQW1vdW50ICogZXhjaGFuZ2VSYXRlXG4gICAgY29uc3QgZnJlaWdodER6ZCA9IHNoaXBtZW50LmZvYkFtb3VudCAqIDAuMTUgKiBleGNoYW5nZVJhdGUgLy8gRXN0aW1hdGlvbiAxNSUgZHUgRk9CXG4gICAgY29uc3QgdG90YWxDaWZEemQgPSBmb2JBbW91bnREemQgKyBmcmVpZ2h0RHpkXG5cbiAgICAvLyBWYWxldXJzIG1hbnVlbGxlcyBzaW11bMOpZXMgKGRhbnMgdW5lIHZyYWllIGFwcGxpY2F0aW9uLCBjZXMgdmFsZXVycyB2aWVuZHJhaWVudCBkZXMgY2hhbXBzIGRlIHNhaXNpZSlcbiAgICAvLyBQT1JUIEZFRVMgLSBWYWxldXJzIFRUQyBldCBUVkEgc2Fpc2llcyBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBpbXBvcnREZWxpdmVyeVR0YyA9IDQ4MjM0NCAvLyBWYWxldXIgVFRDIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBpbXBvcnREZWxpdmVyeVR2YSA9IDc3MTc1IC8vIFRWQSBzYWlzaWUgbWFudWVsbGVtZW50IChwYXMgY2FsY3Vsw6llIGF1dG9tYXRpcXVlbWVudClcbiAgICBjb25zdCBpbXBvcnREZWxpdmVyeUh0ID0gaW1wb3J0RGVsaXZlcnlUdGMgLSBpbXBvcnREZWxpdmVyeVR2YVxuXG4gICAgY29uc3QgY3VzdG9tc0luc3BlY3Rpb25UdGMgPSAyODk0MDYgLy8gVmFsZXVyIFRUQyBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgY3VzdG9tc0luc3BlY3Rpb25UdmEgPSA0NjMwNSAvLyBUVkEgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IGN1c3RvbXNJbnNwZWN0aW9uSHQgPSBjdXN0b21zSW5zcGVjdGlvblR0YyAtIGN1c3RvbXNJbnNwZWN0aW9uVHZhXG5cbiAgICBjb25zdCBwb3J0RmVlc1RvdGFsVHRjID0gaW1wb3J0RGVsaXZlcnlUdGMgKyBjdXN0b21zSW5zcGVjdGlvblR0Y1xuICAgIGNvbnN0IHBvcnRGZWVzVG90YWxIdCA9IGltcG9ydERlbGl2ZXJ5SHQgKyBjdXN0b21zSW5zcGVjdGlvbkh0XG5cbiAgICAvLyBTSElQUElORyBDT01QQU5ZIEZFRVMgLSBWYWxldXJzIFRUQyBldCBUVkEgc2Fpc2llcyBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBzaGlwcGluZ0FnZW5jeVR0YyA9IDY3NTI4MSAvLyBWYWxldXIgVFRDIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBzaGlwcGluZ0FnZW5jeVR2YSA9IDEwODA0NSAvLyBUVkEgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IHNoaXBwaW5nQWdlbmN5SHQgPSBzaGlwcGluZ0FnZW5jeVR0YyAtIHNoaXBwaW5nQWdlbmN5VHZhXG5cbiAgICBjb25zdCBlbXB0eUNvbnRhaW5lcnNUdGMgPSAzODU4NzUgLy8gVmFsZXVyIFRUQyBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgZW1wdHlDb250YWluZXJzVHZhID0gNjE3NDAgLy8gVFZBIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBlbXB0eUNvbnRhaW5lcnNIdCA9IGVtcHR5Q29udGFpbmVyc1R0YyAtIGVtcHR5Q29udGFpbmVyc1R2YVxuXG4gICAgY29uc3QgZGVtdXJyYWdlSHQgPSAxOTI5MzggLy8gVmFsZXVyIEhUIHNhaXNpZSBtYW51ZWxsZW1lbnQgKHBhcyBkZSBUVkEgc3VyIHN1cmVzdGFyaWVzKVxuXG4gICAgY29uc3Qgc2hpcHBpbmdGZWVzVG90YWxUdGMgPSBzaGlwcGluZ0FnZW5jeVR0YyArIGVtcHR5Q29udGFpbmVyc1R0YyArIGRlbXVycmFnZUh0XG4gICAgY29uc3Qgc2hpcHBpbmdGZWVzVG90YWxIdCA9IHNoaXBwaW5nQWdlbmN5SHQgKyBlbXB0eUNvbnRhaW5lcnNIdCArIGRlbXVycmFnZUh0XG5cbiAgICAvLyBPVEhFUiBNSVNDRUxMQU5FT1VTIEVYUEVOU0VTIC0gVmFsZXVycyBUVEMgZXQgVFZBIHNhaXNpZXMgbWFudWVsbGVtZW50XG4gICAgY29uc3QgbWlzY0V4cGVuc2VzVHRjID0gNDgyMzQ0IC8vIFZhbGV1ciBUVEMgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IG1pc2NFeHBlbnNlc1R2YSA9IDc3MTc1IC8vIFRWQSBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgbWlzY0V4cGVuc2VzSHQgPSBtaXNjRXhwZW5zZXNUdGMgLSBtaXNjRXhwZW5zZXNUdmFcblxuICAgIC8vIFRSQU5TSVQgU0VSVklDRVMgRVhQRU5TRVMgLSBWYWxldXJzIFRUQyBldCBUVkEgc2Fpc2llcyBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCB0cmFuc2l0RXhwZW5zZXNUdGMgPSAzODU4NzUgLy8gVmFsZXVyIFRUQyBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgdHJhbnNpdEV4cGVuc2VzVHZhID0gNjE3NDAgLy8gVFZBIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCB0cmFuc2l0RXhwZW5zZXNIdCA9IHRyYW5zaXRFeHBlbnNlc1R0YyAtIHRyYW5zaXRFeHBlbnNlc1R2YVxuXG4gICAgLy8gQ2FsY3VscyBkZXMgdG90YXV4IHNlbG9uIGxlcyBmb3JtdWxlcyBkZW1hbmTDqWVzXG4gICAgY29uc3QgbGFuZGVkQ29zdFR0YyA9IHRvdGFsQ2lmRHpkICsgcG9ydEZlZXNUb3RhbFR0YyArIHNoaXBwaW5nRmVlc1RvdGFsVHRjICsgbWlzY0V4cGVuc2VzVHRjICsgdHJhbnNpdEV4cGVuc2VzVHRjXG4gICAgY29uc3QgbGFuZGVkQ29zdEh0ID0gdG90YWxDaWZEemQgKyBwb3J0RmVlc1RvdGFsSHQgKyBzaGlwcGluZ0ZlZXNUb3RhbEh0ICsgbWlzY0V4cGVuc2VzSHQgKyB0cmFuc2l0RXhwZW5zZXNIdFxuXG4gICAgY29uc3Qgc2hpcG1lbnREYXRhID0gW1xuICAgICAgW2BSQVBQT1JUIEQnRVhQw4lESVRJT04gLSAke29yZGVyTnVtYmVyfWBdLFxuICAgICAgWydEYXRlIGRlIGfDqW7DqXJhdGlvbjonLCBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydJTkZPUk1BVElPTlMgR8OJTsOJUkFMRVMnXSxcbiAgICAgIFsnTsKwIENvbW1hbmRlOicsIHNoaXBtZW50Lm9yZGVyTnVtYmVyXSxcbiAgICAgIFsnRm91cm5pc3NldXI6Jywgc2hpcG1lbnQuc3VwcGxpZXJdLFxuICAgICAgWydEYXRlOicsIHNoaXBtZW50LmRhdGVdLFxuICAgICAgWydTdGF0dXQ6Jywgc2hpcG1lbnQuc3RhdHVzXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ0TDiVRBSUwgRklOQU5DSUVSJ10sXG4gICAgICBbJ01vbnRhbnQgRk9CIChVU0QpOicsIHNoaXBtZW50LmZvYkFtb3VudC50b0xvY2FsZVN0cmluZygnZW4tVVMnKV0sXG4gICAgICBbJ1RhdXggZGUgY2hhbmdlIChVU0QvRFpEKTonLCBleGNoYW5nZVJhdGUudG9GaXhlZCg1KV0sXG4gICAgICBbJ01vbnRhbnQgRk9CIChEWkQpOicsIGZvYkFtb3VudER6ZC50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJ0ZyZXQgKERaRCk6JywgZnJlaWdodER6ZC50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJ1RPVEFMIEFNT1VOVCBDSUYgRFpEIChGT0IgKyBGcmVpZ2h0KTonLCB0b3RhbENpZkR6ZC50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydDT8ObVFMgRMOJVEFJTEzDiVMgKFZhbGV1cnMgc2Fpc2llcyBtYW51ZWxsZW1lbnQpJ10sXG4gICAgICBbJyddLFxuICAgICAgWydQT1JUIEZFRVMgLSBMaXZyYWlzb24gSW1wb3J0OiddLFxuICAgICAgWycgIFRUQyAoc2Fpc2kgbWFudWVsbGVtZW50KTonLCBpbXBvcnREZWxpdmVyeVR0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgVFZBIChzYWlzaWUgbWFudWVsbGVtZW50KTonLCBpbXBvcnREZWxpdmVyeVR2YS50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgSFQgKFRUQyAtIFRWQSk6JywgaW1wb3J0RGVsaXZlcnlIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyddLFxuICAgICAgWydQT1JUIEZFRVMgLSBJbnNwZWN0aW9uIERvdWFuacOocmU6J10sXG4gICAgICBbJyAgVFRDIChzYWlzaSBtYW51ZWxsZW1lbnQpOicsIGN1c3RvbXNJbnNwZWN0aW9uVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBUVkEgKHNhaXNpZSBtYW51ZWxsZW1lbnQpOicsIGN1c3RvbXNJbnNwZWN0aW9uVHZhLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBIVCAoVFRDIC0gVFZBKTonLCBjdXN0b21zSW5zcGVjdGlvbkh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1BPUlQgRkVFUyBcIk92ZXJhbGwgVG90YWxzXCI6J10sXG4gICAgICBbJyAgVFRDOicsIHBvcnRGZWVzVG90YWxUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUOicsIHBvcnRGZWVzVG90YWxIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyddLFxuICAgICAgWydTSElQUElORyBDT01QQU5ZIEZFRVMgLSBBZ2VuY2UgTWFyaXRpbWU6J10sXG4gICAgICBbJyAgVFRDIChzYWlzaSBtYW51ZWxsZW1lbnQpOicsIHNoaXBwaW5nQWdlbmN5VHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBUVkEgKHNhaXNpZSBtYW51ZWxsZW1lbnQpOicsIHNoaXBwaW5nQWdlbmN5VHZhLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBIVCAoVFRDIC0gVFZBKTonLCBzaGlwcGluZ0FnZW5jeUh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyAtIENvbnRlbmV1cnMgVmlkZXM6J10sXG4gICAgICBbJyAgVFRDIChzYWlzaSBtYW51ZWxsZW1lbnQpOicsIGVtcHR5Q29udGFpbmVyc1R0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgVFZBIChzYWlzaWUgbWFudWVsbGVtZW50KTonLCBlbXB0eUNvbnRhaW5lcnNUdmEudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUIChUVEMgLSBUVkEpOicsIGVtcHR5Q29udGFpbmVyc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyAtIFN1cmVzdGFyaWVzOiddLFxuICAgICAgWycgIEhUIChzYWlzaSBtYW51ZWxsZW1lbnQsIHBhcyBkZSBUVkEpOicsIGRlbXVycmFnZUh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyBcIk92ZXJhbGwgVG90YWxzXCI6J10sXG4gICAgICBbJyAgVFRDOicsIHNoaXBwaW5nRmVlc1RvdGFsVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBIVDonLCBzaGlwcGluZ0ZlZXNUb3RhbEh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ09USEVSIE1JU0NFTExBTkVPVVMgRVhQRU5TRVM6J10sXG4gICAgICBbJyAgVFRDIChzYWlzaSBtYW51ZWxsZW1lbnQpOicsIG1pc2NFeHBlbnNlc1R0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgVFZBIChzYWlzaWUgbWFudWVsbGVtZW50KTonLCBtaXNjRXhwZW5zZXNUdmEudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUIChUVEMgLSBUVkEpOicsIG1pc2NFeHBlbnNlc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1RSQU5TSVQgU0VSVklDRVMgRVhQRU5TRVM6J10sXG4gICAgICBbJyAgVFRDIChzYWlzaSBtYW51ZWxsZW1lbnQpOicsIHRyYW5zaXRFeHBlbnNlc1R0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgVFZBIChzYWlzaWUgbWFudWVsbGVtZW50KTonLCB0cmFuc2l0RXhwZW5zZXNUdmEudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUIChUVEMgLSBUVkEpOicsIHRyYW5zaXRFeHBlbnNlc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ0NBTENVTFMgRklOQVVYJ10sXG4gICAgICBbJyddLFxuICAgICAgWydMYW5kZWQgQ29zdCBUVEM6JywgbGFuZGVkQ29zdFR0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgPSBDSUYgKyBQb3J0IEZlZXMgVFRDICsgU2hpcHBpbmcgRmVlcyBUVEMgKyBNaXNjIEV4cGVuc2VzIFRUQyArIFRyYW5zaXQgRXhwZW5zZXMgVFRDJ10sXG4gICAgICBbJyAgPSAnICsgdG90YWxDaWZEemQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHBvcnRGZWVzVG90YWxUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHNoaXBwaW5nRmVlc1RvdGFsVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyBtaXNjRXhwZW5zZXNUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHRyYW5zaXRFeHBlbnNlc1R0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydMYW5kZWQgQ29zdCBIVDonLCBsYW5kZWRDb3N0SHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgID0gQ0lGICsgUG9ydCBGZWVzIEhUICsgU2hpcHBpbmcgRmVlcyBIVCArIE1pc2MgRXhwZW5zZXMgSFQgKyBUcmFuc2l0IEV4cGVuc2VzIEhUJ10sXG4gICAgICBbJyAgPSAnICsgdG90YWxDaWZEemQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHBvcnRGZWVzVG90YWxIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgKyAnICsgc2hpcHBpbmdGZWVzVG90YWxIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgKyAnICsgbWlzY0V4cGVuc2VzSHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHRyYW5zaXRFeHBlbnNlc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ0NvZWZmaWNpZW50IFRUQzonLCAobGFuZGVkQ29zdFR0YyAvIGZvYkFtb3VudER6ZCkudG9GaXhlZCgzKV0sXG4gICAgICBbJ0NvZWZmaWNpZW50IEhUOicsIChsYW5kZWRDb3N0SHQgLyBmb2JBbW91bnREemQpLnRvRml4ZWQoMyldLFxuICAgICAgWydNYXJnZSB2cyBGT0IgKFRUQyk6JywgYCR7KCgobGFuZGVkQ29zdFR0YyAvIGZvYkFtb3VudER6ZCkgLSAxKSAqIDEwMCkudG9GaXhlZCgxKX0lYF0sXG4gICAgICBbJ01hcmdlIHZzIEZPQiAoSFQpOicsIGAkeygoKGxhbmRlZENvc3RIdCAvIGZvYkFtb3VudER6ZCkgLSAxKSAqIDEwMCkudG9GaXhlZCgxKX0lYF1cbiAgICBdXG5cbiAgICBjb25zdCB3cyA9IFhMU1gudXRpbHMuYW9hX3RvX3NoZWV0KHNoaXBtZW50RGF0YSlcbiAgICBYTFNYLnV0aWxzLmJvb2tfYXBwZW5kX3NoZWV0KHdiLCB3cywgYEV4cMOpZGl0aW9uICR7b3JkZXJOdW1iZXJ9YClcblxuICAgIHJldHVybiB3YlxuICB9XG5cbiAgY29uc3QgZ2V0Q29tcG9uZW50RGVzY3JpcHRpb24gPSAoY29tcG9uZW50OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIGNvbnN0IGRlc2NyaXB0aW9uczogeyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfSA9IHtcbiAgICAgICdGT0IgQW1vdW50JzogJ1ZhbGV1ciBGcmVlIE9uIEJvYXJkIC0gUHJpeCBtYXJjaGFuZGlzZSBhdSBwb3J0IGRlIGTDqXBhcnQnLFxuICAgICAgJ0N1c3RvbXMgRHV0aWVzJzogJ0Ryb2l0cyBkZSBkb3VhbmUgZXQgdGF4ZXMgZFxcJ2ltcG9ydGF0aW9uJyxcbiAgICAgICdQb3J0IEZlZXMnOiAnRnJhaXMgcG9ydHVhaXJlcyBldCBkZSBtYW51dGVudGlvbicsXG4gICAgICAnU2hpcHBpbmcgRmVlcyc6ICdGcmFpcyBkZSB0cmFuc3BvcnQgbWFyaXRpbWUgZXQgdGVycmVzdHJlJyxcbiAgICAgICdPdGhlciBDb3N0cyc6ICdGcmFpcyBkaXZlcnMgKGFzc3VyYW5jZSwgdHJhbnNpdCwgZXRjLiknXG4gICAgfVxuICAgIHJldHVybiBkZXNjcmlwdGlvbnNbY29tcG9uZW50XSB8fCAnRGVzY3JpcHRpb24gbm9uIGRpc3BvbmlibGUnXG4gIH1cblxuICBpZiAoc3RhdHVzID09PSAnbG9hZGluZycpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPExheW91dD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMzIgdy0zMiBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5XCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9MYXlvdXQ+XG4gICAgKVxuICB9XG5cbiAgY29uc3QgdG90YWxTaGlwbWVudHMgPSBtb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnNoaXBtZW50cywgMClcbiAgY29uc3QgdG90YWxWYWx1ZSA9IG1vY2tEYXRhLm1vbnRobHlTaGlwbWVudHMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIGl0ZW0udmFsdWUsIDApXG4gIGNvbnN0IGF2Z0NvZWZmaWNpZW50ID0gMS4zNVxuXG4gIHJldHVybiAoXG4gICAgPExheW91dD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlJlcG9ydHMgJiBBbmFseXRpY3M8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNvbXByZWhlbnNpdmUgaW5zaWdodHMgaW50byB5b3VyIGltcG9ydCBjb3N0czwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2RhdGVSYW5nZS5mcm9tfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RGF0ZVJhbmdlKHByZXYgPT4gKHsgLi4ucHJldiwgZnJvbTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNDBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2RhdGVSYW5nZS50b31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERhdGVSYW5nZShwcmV2ID0+ICh7IC4uLnByZXYsIHRvOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00MFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEtQSSBDYXJkcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+VG90YWwgU2hpcG1lbnRzPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57dG90YWxTaGlwbWVudHN9PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgKzEyJSBmcm9tIGxhc3QgcGVyaW9kXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+VG90YWwgVmFsdWU8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRDdXJyZW5jeSh0b3RhbFZhbHVlKX08L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICArOCUgZnJvbSBsYXN0IHBlcmlvZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkF2ZyBDb2VmZmljaWVudDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e2F2Z0NvZWZmaWNpZW50LnRvRml4ZWQoMil9eDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgIC0yJSBmcm9tIGxhc3QgcGVyaW9kXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+QXZnIE1vbnRobHk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57TWF0aC5yb3VuZCh0b3RhbFNoaXBtZW50cyAvIDYpfTwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgIHNoaXBtZW50cyBwZXIgbW9udGhcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDaGFydHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBNb250aGx5IFNoaXBtZW50cyBDaGFydCAqL31cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIE1vbnRobHkgU2hpcG1lbnRzXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIFNoaXBtZW50IHZvbHVtZSBhbmQgdmFsdWUgdHJlbmRzXG4gICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUNvbnRhaW5lciB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9ezMwMH0+XG4gICAgICAgICAgICAgICAgPEJhckNoYXJ0IGRhdGE9e21vY2tEYXRhLm1vbnRobHlTaGlwbWVudHN9PlxuICAgICAgICAgICAgICAgICAgPENhcnRlc2lhbkdyaWQgc3Ryb2tlRGFzaGFycmF5PVwiMyAzXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxYQXhpcyBkYXRhS2V5PVwibW9udGhcIiAvPlxuICAgICAgICAgICAgICAgICAgPFlBeGlzIC8+XG4gICAgICAgICAgICAgICAgICA8VG9vbHRpcFxuICAgICAgICAgICAgICAgICAgICBmb3JtYXR0ZXI9eyh2YWx1ZSwgbmFtZSkgPT4gW1xuICAgICAgICAgICAgICAgICAgICAgIG5hbWUgPT09ICdzaGlwbWVudHMnID8gdmFsdWUgOiBmb3JtYXRDdXJyZW5jeSh2YWx1ZSBhcyBudW1iZXIpLFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWUgPT09ICdzaGlwbWVudHMnID8gJ1NoaXBtZW50cycgOiAnVmFsdWUgKERaRCknXG4gICAgICAgICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPEJhciBkYXRhS2V5PVwic2hpcG1lbnRzXCIgZmlsbD1cIiMzYjgyZjZcIiAvPlxuICAgICAgICAgICAgICAgIDwvQmFyQ2hhcnQ+XG4gICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUNvbnRhaW5lcj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIENvc3QgQnJlYWtkb3duIENoYXJ0ICovfVxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8UGllQ2hhcnQgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBDb3N0IEJyZWFrZG93blxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBEaXN0cmlidXRpb24gb2YgaW1wb3J0IGNvc3RzXG4gICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUNvbnRhaW5lciB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9ezMwMH0+XG4gICAgICAgICAgICAgICAgPFJlY2hhcnRzUGllQ2hhcnQ+XG4gICAgICAgICAgICAgICAgICA8VG9vbHRpcFxuICAgICAgICAgICAgICAgICAgICBmb3JtYXR0ZXI9eyh2YWx1ZSwgbmFtZSkgPT4gW1xuICAgICAgICAgICAgICAgICAgICAgIGAke3ZhbHVlfSVgLFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWVcbiAgICAgICAgICAgICAgICAgICAgXX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8UmVjaGFydHNQaWVDaGFydCBkYXRhPXttb2NrRGF0YS5jb3N0QnJlYWtkb3dufT5cbiAgICAgICAgICAgICAgICAgICAge21vY2tEYXRhLmNvc3RCcmVha2Rvd24ubWFwKChlbnRyeSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2VsbCBrZXk9e2BjZWxsLSR7aW5kZXh9YH0gZmlsbD17Q09MT1JTW2luZGV4ICUgQ09MT1JTLmxlbmd0aF19IC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9SZWNoYXJ0c1BpZUNoYXJ0PlxuICAgICAgICAgICAgICAgIDwvUmVjaGFydHNQaWVDaGFydD5cbiAgICAgICAgICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAge21vY2tEYXRhLmNvc3RCcmVha2Rvd24ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0ubmFtZX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1mdWxsIG1yLTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBDT0xPUlNbaW5kZXggJSBDT0xPUlMubGVuZ3RoXSB9fVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntpdGVtLnZhbHVlfSU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJlcG9ydCBHZW5lcmF0aW9uICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGU+R2VuZXJhdGUgUmVwb3J0czwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgRXhwb3J0IGRldGFpbGVkIHJlcG9ydHMgZm9yIGFuYWx5c2lzIGFuZCByZWNvcmQga2VlcGluZ1xuICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMjAgZmxleC1jb2xcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUV4cG9ydFJlcG9ydCgnQ29tcHJlaGVuc2l2ZScpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNiB3LTYgbWItMlwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+Q29tcHJlaGVuc2l2ZSBSZXBvcnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+QWxsIHNoaXBtZW50cyAmIGNvc3RzPC9zcGFuPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMjAgZmxleC1jb2xcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUV4cG9ydFJlcG9ydCgnQ29zdF9BbmFseXNpcycpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTYgdy02IG1iLTJcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPkNvc3QgQW5hbHlzaXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+RGV0YWlsZWQgY29zdCBicmVha2Rvd248L3NwYW4+XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0yMCBmbGV4LWNvbFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRXhwb3J0UmVwb3J0KCdNb250aGx5X1N1bW1hcnknKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTYgdy02IG1iLTJcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPk1vbnRobHkgU3VtbWFyeTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5QZXJpb2QtYmFzZWQgYW5hbHlzaXM8L3NwYW4+XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBSZWNlbnQgU2hpcG1lbnRzIFRhYmxlICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGU+UmVjZW50IFNoaXBtZW50czwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgTGF0ZXN0IGltcG9ydCBjYWxjdWxhdGlvbnMgYW5kIHRoZWlyIHJlc3VsdHNcbiAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8dGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYm9yZGVyLWJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTRcIj5PcmRlciBOdW1iZXI8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPlN1cHBsaWVyPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTRcIj5EYXRlPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcHktMyBweC00XCI+Rk9CIEFtb3VudDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHB5LTMgcHgtNFwiPkxhbmRlZCBDb3N0PC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcHktMyBweC00XCI+Q29lZmZpY2llbnQ8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPlN0YXR1czwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00XCI+QWN0aW9uczwvdGg+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAgICAgICAge21vY2tEYXRhLnJlY2VudFNoaXBtZW50cy5tYXAoKHNoaXBtZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e3NoaXBtZW50LmlkfSBjbGFzc05hbWU9XCJib3JkZXItYiBob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCBmb250LW1lZGl1bVwiPntzaGlwbWVudC5vcmRlck51bWJlcn08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj57c2hpcG1lbnQuc3VwcGxpZXJ9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+e25ldyBEYXRlKHNoaXBtZW50LmRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXJpZ2h0XCI+e2Zvcm1hdFVTRChzaGlwbWVudC5mb2JBbW91bnQpfTwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXJpZ2h0XCI+e2Zvcm1hdEN1cnJlbmN5KHNoaXBtZW50LmxhbmRlZENvc3QpfTwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LXJpZ2h0XCI+e3NoaXBtZW50LmNvZWZmaWNpZW50LnRvRml4ZWQoMil9eDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2hpcG1lbnQuc3RhdHVzID09PSAnQ29tcGxldGVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NoaXBtZW50LnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFeHBvcnRSZXBvcnQoYFNoaXBtZW50XyR7c2hpcG1lbnQub3JkZXJOdW1iZXJ9YCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBFeHBvcnRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgPC9MYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlU2Vzc2lvbiIsInVzZVJvdXRlciIsIkxheW91dCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkZpbGVUZXh0IiwiRG93bmxvYWQiLCJDYWxlbmRhciIsIlRyZW5kaW5nVXAiLCJEb2xsYXJTaWduIiwiUGFja2FnZSIsIkJhckNoYXJ0MyIsIlBpZUNoYXJ0IiwiQmFyQ2hhcnQiLCJCYXIiLCJYQXhpcyIsIllBeGlzIiwiQ2FydGVzaWFuR3JpZCIsIlRvb2x0aXAiLCJSZXNwb25zaXZlQ29udGFpbmVyIiwiUmVjaGFydHNQaWVDaGFydCIsIkNlbGwiLCJYTFNYIiwidG9hc3QiLCJtb2NrRGF0YSIsIm1vbnRobHlTaGlwbWVudHMiLCJtb250aCIsInNoaXBtZW50cyIsInZhbHVlIiwiY29zdEJyZWFrZG93biIsIm5hbWUiLCJhbW91bnQiLCJyZWNlbnRTaGlwbWVudHMiLCJpZCIsIm9yZGVyTnVtYmVyIiwic3VwcGxpZXIiLCJkYXRlIiwiZm9iQW1vdW50IiwibGFuZGVkQ29zdCIsImNvZWZmaWNpZW50Iiwic3RhdHVzIiwiQ09MT1JTIiwiUmVwb3J0cyIsImRhdGEiLCJzZXNzaW9uIiwicm91dGVyIiwiZGF0ZVJhbmdlIiwic2V0RGF0ZVJhbmdlIiwiZnJvbSIsInRvIiwidXNlRWZmZWN0IiwicHVzaCIsImZvcm1hdEN1cnJlbmN5IiwiSW50bCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJtaW5pbXVtRnJhY3Rpb25EaWdpdHMiLCJtYXhpbXVtRnJhY3Rpb25EaWdpdHMiLCJmb3JtYXQiLCJmb3JtYXRVU0QiLCJoYW5kbGVFeHBvcnRSZXBvcnQiLCJyZXBvcnRUeXBlIiwiZmlsZU5hbWUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsIndvcmtib29rIiwiZ2VuZXJhdGVDb21wcmVoZW5zaXZlUmVwb3J0IiwiZ2VuZXJhdGVDb3N0QW5hbHlzaXNSZXBvcnQiLCJnZW5lcmF0ZU1vbnRobHlTdW1tYXJ5UmVwb3J0Iiwic3RhcnRzV2l0aCIsInJlcGxhY2UiLCJnZW5lcmF0ZVNoaXBtZW50UmVwb3J0Iiwid3JpdGVGaWxlIiwic3VjY2VzcyIsImVycm9yIiwiY29uc29sZSIsIndiIiwidXRpbHMiLCJib29rX25ldyIsInN1bW1hcnlEYXRhIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwidG90YWxTaGlwbWVudHMiLCJ0b3RhbFZhbHVlIiwidG9Mb2NhbGVTdHJpbmciLCJhdmdDb2VmZmljaWVudCIsInRvRml4ZWQiLCJNYXRoIiwicm91bmQiLCJtYXAiLCJpdGVtIiwic3VtbWFyeVdzIiwiYW9hX3RvX3NoZWV0IiwiYm9va19hcHBlbmRfc2hlZXQiLCJzaGlwbWVudHNEYXRhIiwicyIsInNoaXBtZW50c1dzIiwibW9udGhseURhdGEiLCJtIiwibW9udGhseVdzIiwiY29zdERhdGEiLCJnZXRDb21wb25lbnREZXNjcmlwdGlvbiIsIndzIiwiaW5kZXgiLCJzaGlwbWVudCIsImZpbmQiLCJFcnJvciIsImV4Y2hhbmdlUmF0ZSIsImZvYkFtb3VudER6ZCIsImZyZWlnaHREemQiLCJ0b3RhbENpZkR6ZCIsImltcG9ydERlbGl2ZXJ5VHRjIiwiaW1wb3J0RGVsaXZlcnlUdmEiLCJpbXBvcnREZWxpdmVyeUh0IiwiY3VzdG9tc0luc3BlY3Rpb25UdGMiLCJjdXN0b21zSW5zcGVjdGlvblR2YSIsImN1c3RvbXNJbnNwZWN0aW9uSHQiLCJwb3J0RmVlc1RvdGFsVHRjIiwicG9ydEZlZXNUb3RhbEh0Iiwic2hpcHBpbmdBZ2VuY3lUdGMiLCJzaGlwcGluZ0FnZW5jeVR2YSIsInNoaXBwaW5nQWdlbmN5SHQiLCJlbXB0eUNvbnRhaW5lcnNUdGMiLCJlbXB0eUNvbnRhaW5lcnNUdmEiLCJlbXB0eUNvbnRhaW5lcnNIdCIsImRlbXVycmFnZUh0Iiwic2hpcHBpbmdGZWVzVG90YWxUdGMiLCJzaGlwcGluZ0ZlZXNUb3RhbEh0IiwibWlzY0V4cGVuc2VzVHRjIiwibWlzY0V4cGVuc2VzVHZhIiwibWlzY0V4cGVuc2VzSHQiLCJ0cmFuc2l0RXhwZW5zZXNUdGMiLCJ0cmFuc2l0RXhwZW5zZXNUdmEiLCJ0cmFuc2l0RXhwZW5zZXNIdCIsImxhbmRlZENvc3RUdGMiLCJsYW5kZWRDb3N0SHQiLCJzaGlwbWVudERhdGEiLCJjb21wb25lbnQiLCJkZXNjcmlwdGlvbnMiLCJkaXYiLCJjbGFzc05hbWUiLCJyZWR1Y2UiLCJzdW0iLCJoMSIsInAiLCJ0eXBlIiwib25DaGFuZ2UiLCJlIiwicHJldiIsInRhcmdldCIsIndpZHRoIiwiaGVpZ2h0Iiwic3Ryb2tlRGFzaGFycmF5IiwiZGF0YUtleSIsImZvcm1hdHRlciIsImZpbGwiLCJlbnRyeSIsImxlbmd0aCIsImJhY2tncm91bmRDb2xvciIsInNwYW4iLCJ2YXJpYW50Iiwib25DbGljayIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJ0ZCIsInNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});