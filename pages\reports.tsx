import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/layout/Layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  FileText,
  Download,
  Calendar,
  TrendingUp,
  DollarSign,
  Package,
  BarChart3,
  PieChart
} from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart as RechartsPieChart, Cell } from 'recharts'
import * as XLSX from 'xlsx'
import toast from 'react-hot-toast'

const mockData = {
  monthlyShipments: [
    { month: 'Jan', shipments: 12, value: 1200000 },
    { month: 'Feb', shipments: 8, value: 850000 },
    { month: 'Mar', shipments: 15, value: 1650000 },
    { month: 'Apr', shipments: 10, value: 1100000 },
    { month: 'May', shipments: 18, value: 2100000 },
    { month: 'Jun', shipments: 14, value: 1800000 }
  ],
  costBreakdown: [
    { name: 'FOB Amount', value: 65, amount: 6500000 },
    { name: 'Customs Duties', value: 15, amount: 1500000 },
    { name: 'Port Fees', value: 8, amount: 800000 },
    { name: 'Shipping Fees', value: 7, amount: 700000 },
    { name: 'Other Costs', value: 5, amount: 500000 }
  ],
  recentShipments: [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      supplier: 'ABC Motors Ltd',
      date: '2024-01-15',
      fobAmount: 125000,
      landedCost: 168750,
      coefficient: 1.35,
      status: 'Completed'
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      supplier: 'XYZ Parts Co',
      date: '2024-01-12',
      fobAmount: 89000,
      landedCost: 120150,
      coefficient: 1.35,
      status: 'Completed'
    },
    {
      id: '3',
      orderNumber: 'ORD-2024-003',
      supplier: 'Global Supply Inc',
      date: '2024-01-10',
      fobAmount: 156000,
      landedCost: 210600,
      coefficient: 1.35,
      status: 'In Progress'
    }
  ]
}

const COLORS = ['#3b82f6', '#ef4444', '#f59e0b', '#10b981', '#8b5cf6']

export default function Reports() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [dateRange, setDateRange] = useState({
    from: '2024-01-01',
    to: '2024-06-30'
  })

  React.useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatUSD = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const handleExportReport = (reportType: string) => {
    try {
      const fileName = `${reportType}_Report_${new Date().toISOString().split('T')[0]}.xlsx`
      let workbook: XLSX.WorkBook

      if (reportType === 'Comprehensive') {
        workbook = generateComprehensiveReport()
      } else if (reportType === 'Cost_Analysis') {
        workbook = generateCostAnalysisReport()
      } else if (reportType === 'Monthly_Summary') {
        workbook = generateMonthlySummaryReport()
      } else if (reportType.startsWith('Shipment_')) {
        const orderNumber = reportType.replace('Shipment_', '')
        workbook = generateShipmentReport(orderNumber)
      } else {
        workbook = generateComprehensiveReport()
      }

      // Generate and download the file
      XLSX.writeFile(workbook, fileName)
      toast.success(`Rapport "${fileName}" généré et téléchargé avec succès!`)
    } catch (error) {
      console.error('Error generating report:', error)
      toast.error('Erreur lors de la génération du rapport')
    }
  }

  const generateComprehensiveReport = (): XLSX.WorkBook => {
    const wb = XLSX.utils.book_new()

    // Summary sheet
    const summaryData = [
      ['RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR'],
      ['Période:', `${dateRange.from} à ${dateRange.to}`],
      ['Date de génération:', new Date().toLocaleDateString('fr-FR')],
      [''],
      ['RÉSUMÉ EXÉCUTIF'],
      ['Total des expéditions:', totalShipments],
      ['Valeur totale (DZD):', totalValue.toLocaleString('fr-FR')],
      ['Coefficient moyen:', avgCoefficient.toFixed(3)],
      ['Expéditions par mois:', Math.round(totalShipments / 6)],
      [''],
      ['RÉPARTITION DES COÛTS'],
      ...mockData.costBreakdown.map(item => [item.name, `${item.value}%`, item.amount.toLocaleString('fr-FR') + ' DZD'])
    ]

    const summaryWs = XLSX.utils.aoa_to_sheet(summaryData)
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Résumé')

    // Shipments detail sheet
    const shipmentsData = [
      ['N° Commande', 'Fournisseur', 'Date', 'Montant FOB (USD)', 'Coût Débarqué (DZD)', 'Coefficient', 'Statut'],
      ...mockData.recentShipments.map(s => [
        s.orderNumber,
        s.supplier,
        s.date,
        s.fobAmount,
        s.landedCost,
        s.coefficient.toFixed(3),
        s.status
      ])
    ]

    const shipmentsWs = XLSX.utils.aoa_to_sheet(shipmentsData)
    XLSX.utils.book_append_sheet(wb, shipmentsWs, 'Détail Expéditions')

    // Monthly data sheet
    const monthlyData = [
      ['Mois', 'Nombre d\'expéditions', 'Valeur (DZD)'],
      ...mockData.monthlyShipments.map(m => [m.month, m.shipments, m.value])
    ]

    const monthlyWs = XLSX.utils.aoa_to_sheet(monthlyData)
    XLSX.utils.book_append_sheet(wb, monthlyWs, 'Données Mensuelles')

    return wb
  }

  const generateCostAnalysisReport = (): XLSX.WorkBook => {
    const wb = XLSX.utils.book_new()

    const costData = [
      ['ANALYSE DES COÛTS - IMPORT & LOGISTICS CALCULATOR'],
      ['Période:', `${dateRange.from} à ${dateRange.to}`],
      ['Date de génération:', new Date().toLocaleDateString('fr-FR')],
      [''],
      ['RÉPARTITION DÉTAILLÉE DES COÛTS'],
      ['Composant', 'Pourcentage', 'Montant (DZD)', 'Description'],
      ...mockData.costBreakdown.map(item => [
        item.name,
        `${item.value}%`,
        item.amount.toLocaleString('fr-FR'),
        getComponentDescription(item.name)
      ]),
      [''],
      ['ANALYSE PAR EXPÉDITION'],
      ['N° Commande', 'FOB (USD)', 'Coût Débarqué (DZD)', 'Coefficient', 'Marge vs FOB'],
      ...mockData.recentShipments.map(s => [
        s.orderNumber,
        s.fobAmount.toLocaleString('en-US'),
        s.landedCost.toLocaleString('fr-FR'),
        s.coefficient.toFixed(3),
        `${((s.coefficient - 1) * 100).toFixed(1)}%`
      ])
    ]

    const ws = XLSX.utils.aoa_to_sheet(costData)
    XLSX.utils.book_append_sheet(wb, ws, 'Analyse Coûts')

    return wb
  }

  const generateMonthlySummaryReport = (): XLSX.WorkBook => {
    const wb = XLSX.utils.book_new()

    const monthlyData = [
      ['RÉSUMÉ MENSUEL - IMPORT & LOGISTICS CALCULATOR'],
      ['Période:', `${dateRange.from} à ${dateRange.to}`],
      ['Date de génération:', new Date().toLocaleDateString('fr-FR')],
      [''],
      ['PERFORMANCE MENSUELLE'],
      ['Mois', 'Expéditions', 'Valeur (DZD)', 'Valeur Moyenne', 'Évolution'],
      ...mockData.monthlyShipments.map((m, index) => [
        m.month,
        m.shipments,
        m.value.toLocaleString('fr-FR'),
        Math.round(m.value / m.shipments).toLocaleString('fr-FR'),
        index > 0 ? `${(((m.value - mockData.monthlyShipments[index-1].value) / mockData.monthlyShipments[index-1].value) * 100).toFixed(1)}%` : 'N/A'
      ]),
      [''],
      ['TOTAUX'],
      ['Total expéditions:', totalShipments],
      ['Total valeur (DZD):', totalValue.toLocaleString('fr-FR')],
      ['Moyenne mensuelle:', Math.round(totalValue / 6).toLocaleString('fr-FR')],
      ['Coefficient moyen:', avgCoefficient.toFixed(3)]
    ]

    const ws = XLSX.utils.aoa_to_sheet(monthlyData)
    XLSX.utils.book_append_sheet(wb, ws, 'Résumé Mensuel')

    return wb
  }

  const generateShipmentReport = (orderNumber: string): XLSX.WorkBook => {
    const shipment = mockData.recentShipments.find(s => s.orderNumber === orderNumber)
    if (!shipment) {
      throw new Error('Expédition non trouvée')
    }

    const wb = XLSX.utils.book_new()

    // Configuration de base
    const currency = 'USD' // Dans une vraie app, ceci viendrait des données
    const exchangeRate = 134.50000 // Taux de change vers DZD
    const invoiceNumber = 'INV-2024-001' // Simulé

    // Calculs de base
    const fobAmount = shipment.fobAmount
    const freight = fobAmount * 0.15 // 15% du FOB
    const totalCif = fobAmount + freight

    // Conversions automatiques en DZD
    const fobAmountDzd = fobAmount * exchangeRate
    const freightDzd = freight * exchangeRate
    const totalCifDzd = totalCif * exchangeRate

    // Valeurs simulées pour les frais (dans une vraie app, ces valeurs viendraient des champs de saisie)
    // CUSTOMS DUTIES
    const customsDuties1Ttc = 850000
    const customsDuties1Tva = 136000
    const customsDuties1Ht = customsDuties1Ttc - customsDuties1Tva
    const customsDuties2Ttc = 650000
    const customsDuties2Tva = 104000
    const customsDuties2Ht = customsDuties2Ttc - customsDuties2Tva
    const customsDutiesTotalTtc = customsDuties1Ttc + customsDuties2Ttc
    const customsDutiesTotalTva = customsDuties1Tva + customsDuties2Tva
    const customsDutiesTotalHt = customsDuties1Ht + customsDuties2Ht

    // PORT FEES
    const importDeliveryTtc = 482344
    const importDeliveryTva = 77175
    const importDeliveryHt = importDeliveryTtc - importDeliveryTva
    const customsInspectionTtc = 289406
    const customsInspectionTva = 46305
    const customsInspectionHt = customsInspectionTtc - customsInspectionTva
    const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc
    const portFeesTotalTva = importDeliveryTva + customsInspectionTva
    const portFeesTotalHt = importDeliveryHt + customsInspectionHt

    // SHIPPING COMPANY FEES
    const shippingAgencyTtc = 675281
    const shippingAgencyTva = 108045
    const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva
    const emptyContainersTtc = 385875
    const emptyContainersTva = 61740
    const emptyContainersHt = emptyContainersTtc - emptyContainersTva
    const demurrageHt = 192938 // HT seulement
    const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt
    const shippingFeesTotalTva = shippingAgencyTva + emptyContainersTva
    const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt

    // OTHER MISCELLANEOUS EXPENSES
    const miscExpensesTtc = 482344
    const miscExpensesTva = 77175
    const miscExpensesHt = miscExpensesTtc - miscExpensesTva

    // TRANSIT SERVICES EXPENSES
    const transitExpensesTtc = 385875
    const transitExpensesTva = 61740
    const transitExpensesHt = transitExpensesTtc - transitExpensesTva

    // Calculs finaux
    const landedCostTtc = totalCifDzd + customsDutiesTotalTtc + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc
    const landedCostHt = totalCifDzd + customsDutiesTotalHt + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt
    const landedCostCoefficient = landedCostHt / fobAmountDzd

    const shipmentData = [
      // En-tête
      ['', '', 'Valeur DZD', `Équivalent ${currency}`],
      [`RAPPORT D'EXPÉDITION - ${orderNumber}`],
      ['Date de génération:', new Date().toLocaleDateString('fr-FR')],
      ['---------------------------------------------'],
      [''],
      ['INVOICE #:', invoiceNumber],
      ['Exchange Rate Used : DZD/' + currency + ':', exchangeRate.toFixed(5)],
      [''],

      // Goods Price in Currency
      ['Goods Price in Currency (' + currency + '):'],
      ['', 'FOB AMOUNT:', fobAmount.toLocaleString('fr-FR'), fobAmount.toLocaleString('fr-FR')],
      ['', 'FREIGHT:', freight.toLocaleString('fr-FR'), freight.toLocaleString('fr-FR')],
      ['', 'TOTAL AMOUNT CIF (FOB + Freight):', totalCif.toLocaleString('fr-FR'), totalCif.toLocaleString('fr-FR')],
      [''],

      // Conversion to DZD
      ['Conversion to DZD:'],
      ['', 'FOB AMOUNT DZD (Automatic conversion):', fobAmountDzd.toLocaleString('fr-FR'), (fobAmountDzd / exchangeRate).toLocaleString('fr-FR')],
      ['', 'FREIGHT DZD (Automatic conversion):', freightDzd.toLocaleString('fr-FR'), (freightDzd / exchangeRate).toLocaleString('fr-FR')],
      ['', 'TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):', totalCifDzd.toLocaleString('fr-FR'), (totalCifDzd / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['_____________________________________________________________________________________'],

      // Customs Duties
      ['Customs Duties:'],
      ['', 'COST ALLOCATION NAME (ALGERIA CUSTOMS):'],
      ['', 'D3 N#:', 'D3-2024-001'],
      ['', 'D3 Date:', '15/01/2024'],
      ['', 'QUITTANCE 1:'],
      ['', '', 'Customs Duties1 DZD Total TTC:', customsDuties1Ttc.toLocaleString('fr-FR'), (customsDuties1Ttc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties1 DZD TVA:', customsDuties1Tva.toLocaleString('fr-FR'), (customsDuties1Tva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties1 DZD HT (TTC - TVA):', customsDuties1Ht.toLocaleString('fr-FR'), (customsDuties1Ht / exchangeRate).toLocaleString('fr-FR')],
      ['', 'QUITTANCE 2:'],
      ['', '', 'Customs Duties2 DZD Total TTC:', customsDuties2Ttc.toLocaleString('fr-FR'), (customsDuties2Ttc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties2 DZD TVA:', customsDuties2Tva.toLocaleString('fr-FR'), (customsDuties2Tva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties2 DZD HT (TTC - TVA):', customsDuties2Ht.toLocaleString('fr-FR'), (customsDuties2Ht / exchangeRate).toLocaleString('fr-FR')],
      ['', 'OVERALL TOTALS OF CUSTOMS DUTIES:'],
      ['', '', 'Customs Duties "Overall Totals" DZD Total TTC:', customsDutiesTotalTtc.toLocaleString('fr-FR'), (customsDutiesTotalTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties "Overall Totals" DZD TVA:', customsDutiesTotalTva.toLocaleString('fr-FR'), (customsDutiesTotalTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'Customs Duties "Overall Totals" DZD HT (TTC - TVA):', customsDutiesTotalHt.toLocaleString('fr-FR'), (customsDutiesTotalHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['------------------------------------------------------------------------------------'],

      // Port Fees
      ['PORT FEES:'],
      ['', 'COST ALLOCATION NAME (IMPORT DELIVERY):'],
      ['', '', 'IMPORT DELIVERY INVOICE N#:', 'IMP-DEL-001'],
      ['', '', 'IMPORT DELIVERY INVOICE DATE:', '16/01/2024'],
      ['', '', 'IMPORT DELIVERY Total TTC:', importDeliveryTtc.toLocaleString('fr-FR'), (importDeliveryTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'IMPORT DELIVERY TVA:', importDeliveryTva.toLocaleString('fr-FR'), (importDeliveryTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'IMPORT DELIVERY HT (TTC - TVA):', importDeliveryHt.toLocaleString('fr-FR'), (importDeliveryHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['', 'COST ALLOCATION NAME (CUSTOMS INSPECTION):'],
      ['', '', 'CUSTOMS INSPECTION INVOICE N#:', 'CUST-INS-001'],
      ['', '', 'CUSTOMS INSPECTION INVOICE DATE:', '16/01/2024'],
      ['', '', 'CUSTOMS INSPECTION Total TTC:', customsInspectionTtc.toLocaleString('fr-FR'), (customsInspectionTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'CUSTOMS INSPECTION TVA:', customsInspectionTva.toLocaleString('fr-FR'), (customsInspectionTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'CUSTOMS INSPECTION HT (TTC - TVA):', customsInspectionHt.toLocaleString('fr-FR'), (customsInspectionHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['', 'OVERALL TOTALS OF PORT FEES:'],
      ['', '', 'PORT FEES "Overall Totals" DZD Total TTC:', portFeesTotalTtc.toLocaleString('fr-FR'), (portFeesTotalTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'PORT FEES "Overall Totals" DZD TVA:', portFeesTotalTva.toLocaleString('fr-FR'), (portFeesTotalTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'PORT FEES "Overall Totals" DZD HT (TTC - TVA):', portFeesTotalHt.toLocaleString('fr-FR'), (portFeesTotalHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['---------------------------------------------------------------------------------------------------------------'],

      // Shipping Company Fees
      ['SHIPPING COMPANY FEES:'],
      ['', 'COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):'],
      ['', '', 'SHIPPING AGENCY SERVICES INVOICE N#:', 'SHIP-AGE-001'],
      ['', '', 'SHIPPING AGENCY SERVICES INVOICE DATE:', '17/01/2024'],
      ['', '', 'SHIPPING AGENCY SERVICES Total TTC (DZD):', shippingAgencyTtc.toLocaleString('fr-FR'), (shippingAgencyTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'SHIPPING AGENCY SERVICES TVA (DZD):', shippingAgencyTva.toLocaleString('fr-FR'), (shippingAgencyTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):', shippingAgencyHt.toLocaleString('fr-FR'), (shippingAgencyHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['', 'COST ALLOCATION NAME (EMPTY CONTAINERS):'],
      ['', '', 'EMPTY CONTAINERS RETURN INVOICE N#:', 'EMPTY-CONT-001'],
      ['', '', 'EMPTY CONTAINERS RETURN INVOICE DATE:', '18/01/2024'],
      ['', '', 'EMPTY CONTAINERS RETURN Total TTC (DZD):', emptyContainersTtc.toLocaleString('fr-FR'), (emptyContainersTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'EMPTY CONTAINERS RETURN TVA (DZD):', emptyContainersTva.toLocaleString('fr-FR'), (emptyContainersTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA):', emptyContainersHt.toLocaleString('fr-FR'), (emptyContainersHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['', 'COST ALLOCATION NAME (DEMURRAGE IF PRESENT):'],
      ['', '', 'DEMURRAGE INVOICE N#:', 'DEMUR-001'],
      ['', '', 'DEMURRAGE INVOICE DATE:', '19/01/2024'],
      ['', '', 'DEMURRAGE HT (DZD) (This currency field must be entered manually.):', demurrageHt.toLocaleString('fr-FR'), (demurrageHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['', 'OVERALL TOTALS OF SHIPPING COMPANY FEES:'],
      ['', '', 'SHIPPING COMPANY FEES "Overall Totals" DZD Total TTC:', shippingFeesTotalTtc.toLocaleString('fr-FR'), (shippingFeesTotalTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'SHIPPING COMPANY FEES "Overall Totals" DZD TVA:', shippingFeesTotalTva.toLocaleString('fr-FR'), (shippingFeesTotalTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'SHIPPING COMPANY FEES "Overall Totals" DZD HT (TTC - TVA):', shippingFeesTotalHt.toLocaleString('fr-FR'), (shippingFeesTotalHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['-------------------------------------------------------'],

      // Other Miscellaneous Expenses
      ['OTHER MISCELLANEOUS EXPENSES:'],
      ['', 'COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):'],
      ['', '', 'OTHER MISCELLANEOUS EXPENSES INVOICE N#:', 'MISC-EXP-001'],
      ['', '', 'OTHER MISCELLANEOUS EXPENSES INVOICE DATE:', '20/01/2024'],
      ['', '', 'OTHER MISCELLANEOUS EXPENSES TTC (DZD):', miscExpensesTtc.toLocaleString('fr-FR'), (miscExpensesTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'OTHER MISCELLANEOUS EXPENSES TVA (DZD):', miscExpensesTva.toLocaleString('fr-FR'), (miscExpensesTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):', miscExpensesHt.toLocaleString('fr-FR'), (miscExpensesHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      ['------------------------------------------------------------------------------'],

      // Transit Services Expenses
      ['TRANSIT SERVICES EXPENSES:'],
      ['', 'COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):'],
      ['', '', 'TRANSIT SERVICES EXPENSES INVOICE N#:', 'TRANS-SERV-001'],
      ['', '', 'TRANSIT SERVICES EXPENSES INVOICE DATE:', '21/01/2024'],
      ['', '', 'TRANSIT SERVICES EXPENSES TTC (DZD):', transitExpensesTtc.toLocaleString('fr-FR'), (transitExpensesTtc / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'TRANSIT SERVICES EXPENSES TVA (DZD):', transitExpensesTva.toLocaleString('fr-FR'), (transitExpensesTva / exchangeRate).toLocaleString('fr-FR')],
      ['', '', 'TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):', transitExpensesHt.toLocaleString('fr-FR'), (transitExpensesHt / exchangeRate).toLocaleString('fr-FR')],
      [''],
      [''],

      // Calculs finaux
      ['CALCULS FINAUX:'],
      [''],
      ['Landed Cost HT:', landedCostHt.toLocaleString('fr-FR') + ' DZD', (landedCostHt / exchangeRate).toLocaleString('fr-FR') + ' ' + currency],
      ['= TRANSIT SERVICES EXPENSES HT + OTHER MISCELLANEOUS EXPENSES HT + SHIPPING COMPANY FEES Overall Totals HT + PORT FEES Overall Totals HT + CUSTOMS DUTIES Overall Totals HT + TOTAL AMOUNT CIF DZD'],
      ['= ' + transitExpensesHt.toLocaleString('fr-FR') + ' + ' + miscExpensesHt.toLocaleString('fr-FR') + ' + ' + shippingFeesTotalHt.toLocaleString('fr-FR') + ' + ' + portFeesTotalHt.toLocaleString('fr-FR') + ' + ' + customsDutiesTotalHt.toLocaleString('fr-FR') + ' + ' + totalCifDzd.toLocaleString('fr-FR')],
      [''],
      ['Landed Cost TTC:', landedCostTtc.toLocaleString('fr-FR') + ' DZD', (landedCostTtc / exchangeRate).toLocaleString('fr-FR') + ' ' + currency],
      ['= TRANSIT SERVICES EXPENSES TTC + OTHER MISCELLANEOUS EXPENSES TTC + SHIPPING COMPANY FEES Overall Totals TTC + PORT FEES Overall Totals TTC + CUSTOMS DUTIES Overall Totals TTC + TOTAL AMOUNT CIF DZD'],
      ['= ' + transitExpensesTtc.toLocaleString('fr-FR') + ' + ' + miscExpensesTtc.toLocaleString('fr-FR') + ' + ' + shippingFeesTotalTtc.toLocaleString('fr-FR') + ' + ' + portFeesTotalTtc.toLocaleString('fr-FR') + ' + ' + customsDutiesTotalTtc.toLocaleString('fr-FR') + ' + ' + totalCifDzd.toLocaleString('fr-FR')],
      [''],
      ['Landed cost coefficient:', landedCostCoefficient.toFixed(5), (landedCostCoefficient).toFixed(5)]
    ]

    const ws = XLSX.utils.aoa_to_sheet(shipmentData)
    XLSX.utils.book_append_sheet(wb, ws, `Expédition ${orderNumber}`)

    return wb
  }

  const getComponentDescription = (component: string): string => {
    const descriptions: { [key: string]: string } = {
      'FOB Amount': 'Valeur Free On Board - Prix marchandise au port de départ',
      'Customs Duties': 'Droits de douane et taxes d\'importation',
      'Port Fees': 'Frais portuaires et de manutention',
      'Shipping Fees': 'Frais de transport maritime et terrestre',
      'Other Costs': 'Frais divers (assurance, transit, etc.)'
    }
    return descriptions[component] || 'Description non disponible'
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </Layout>
    )
  }

  const totalShipments = mockData.monthlyShipments.reduce((sum, item) => sum + item.shipments, 0)
  const totalValue = mockData.monthlyShipments.reduce((sum, item) => sum + item.value, 0)
  const avgCoefficient = 1.35

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive insights into your import costs</p>
          </div>
          <div className="flex gap-3">
            <div className="flex gap-2">
              <Input
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                className="w-40"
              />
              <Input
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                className="w-40"
              />
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalShipments}</div>
              <p className="text-xs text-muted-foreground">
                +12% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                +8% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Coefficient</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgCoefficient.toFixed(2)}x</div>
              <p className="text-xs text-muted-foreground">
                -2% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Monthly</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(totalShipments / 6)}</div>
              <p className="text-xs text-muted-foreground">
                shipments per month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Shipments Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Shipments
              </CardTitle>
              <CardDescription>
                Shipment volume and value trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={mockData.monthlyShipments}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      name === 'shipments' ? value : formatCurrency(value as number),
                      name === 'shipments' ? 'Shipments' : 'Value (DZD)'
                    ]}
                  />
                  <Bar dataKey="shipments" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Cost Breakdown Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Cost Breakdown
              </CardTitle>
              <CardDescription>
                Distribution of import costs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Tooltip
                    formatter={(value, name) => [
                      `${value}%`,
                      name
                    ]}
                  />
                  <RechartsPieChart data={mockData.costBreakdown}>
                    {mockData.costBreakdown.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </RechartsPieChart>
                </RechartsPieChart>
              </ResponsiveContainer>
              <div className="mt-4 space-y-2">
                {mockData.costBreakdown.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: COLORS[index % COLORS.length] }}
                      />
                      <span>{item.name}</span>
                    </div>
                    <span className="font-medium">{item.value}%</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Generation */}
        <Card>
          <CardHeader>
            <CardTitle>Generate Reports</CardTitle>
            <CardDescription>
              Export detailed reports for analysis and record keeping
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => handleExportReport('Comprehensive')}
              >
                <FileText className="h-6 w-6 mb-2" />
                <span>Comprehensive Report</span>
                <span className="text-xs text-gray-500">All shipments & costs</span>
              </Button>

              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => handleExportReport('Cost_Analysis')}
              >
                <BarChart3 className="h-6 w-6 mb-2" />
                <span>Cost Analysis</span>
                <span className="text-xs text-gray-500">Detailed cost breakdown</span>
              </Button>

              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => handleExportReport('Monthly_Summary')}
              >
                <Calendar className="h-6 w-6 mb-2" />
                <span>Monthly Summary</span>
                <span className="text-xs text-gray-500">Period-based analysis</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Shipments Table */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Shipments</CardTitle>
            <CardDescription>
              Latest import calculations and their results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Order Number</th>
                    <th className="text-left py-3 px-4">Supplier</th>
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-right py-3 px-4">FOB Amount</th>
                    <th className="text-right py-3 px-4">Landed Cost</th>
                    <th className="text-right py-3 px-4">Coefficient</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {mockData.recentShipments.map((shipment) => (
                    <tr key={shipment.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium">{shipment.orderNumber}</td>
                      <td className="py-3 px-4">{shipment.supplier}</td>
                      <td className="py-3 px-4">{new Date(shipment.date).toLocaleDateString()}</td>
                      <td className="py-3 px-4 text-right">{formatUSD(shipment.fobAmount)}</td>
                      <td className="py-3 px-4 text-right">{formatCurrency(shipment.landedCost)}</td>
                      <td className="py-3 px-4 text-right">{shipment.coefficient.toFixed(2)}x</td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          shipment.status === 'Completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {shipment.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExportReport(`Shipment_${shipment.orderNumber}`)}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Export
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}
