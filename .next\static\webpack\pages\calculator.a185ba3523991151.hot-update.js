"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = ()=>{\n        try {\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplierName,\n                    orderNumber: reception.orderNumber,\n                    invoiceNumber: reception.invoiceNumber,\n                    exchangeRate: reception.exchangeRate,\n                    currency: reception.currency,\n                    fobAmount: reception.fobAmount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customsDuties1Ttc,\n                    customsDuties1Tva: reception.customsDuties1Tva,\n                    customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                    customsDuties2Ht: reception.customsDuties2Ht,\n                    importDeliveryTtc: reception.importDeliveryTtc,\n                    importDeliveryTva: reception.importDeliveryTva,\n                    importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                    customsInspectionTtc: reception.customsInspectionTtc,\n                    customsInspectionTva: reception.customsInspectionTva,\n                    customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                    shippingAgencyTtc: reception.shippingAgencyTtc,\n                    shippingAgencyTva: reception.shippingAgencyTva,\n                    shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                    emptyContainersTtc: reception.emptyContainersTtc,\n                    emptyContainersTva: reception.emptyContainersTva,\n                    emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                    demurrageHt: reception.demurrageHt,\n                    miscExpensesTtc: reception.miscExpensesTtc,\n                    miscExpensesTva: reception.miscExpensesTva,\n                    miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                    transitServicesTtc: reception.transitServicesTtc,\n                    transitServicesTva: reception.transitServicesTva,\n                    transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva,\n                    createdAt: reception.createdAt\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const reception = savedReceptions.find((r)=>r.invoiceNumber === invoiceNumber.trim());\n            if (!reception) {\n                setError(\"Aucune r\\xe9ception trouv\\xe9e pour la facture: \".concat(invoiceNumber));\n                setIsLoading(false);\n                return;\n            }\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplierName,\n                orderNumber: reception.orderNumber,\n                invoiceNumber: reception.invoiceNumber,\n                exchangeRate: reception.exchangeRate,\n                currency: reception.currency,\n                fobAmount: reception.fobAmount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customsDuties1Ttc,\n                customsDuties1Tva: reception.customsDuties1Tva,\n                customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                customsDuties2Ht: reception.customsDuties2Ht,\n                importDeliveryTtc: reception.importDeliveryTtc,\n                importDeliveryTva: reception.importDeliveryTva,\n                importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                customsInspectionTtc: reception.customsInspectionTtc,\n                customsInspectionTva: reception.customsInspectionTva,\n                customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                shippingAgencyTtc: reception.shippingAgencyTtc,\n                shippingAgencyTva: reception.shippingAgencyTva,\n                shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                emptyContainersTtc: reception.emptyContainersTtc,\n                emptyContainersTva: reception.emptyContainersTva,\n                emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                demurrageHt: reception.demurrageHt,\n                miscExpensesTtc: reception.miscExpensesTtc,\n                miscExpensesTva: reception.miscExpensesTva,\n                miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                transitServicesTtc: reception.transitServicesTtc,\n                transitServicesTva: reception.transitServicesTva,\n                transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(\"Co\\xfbts sauvegard\\xe9s avec succ\\xe8s pour la facture: \".concat(generatedCosts.invoiceNumber));\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(\"/shipment-reception?edit=\".concat(reception.id));\n    };\n    const deleteReception = (reception)=>{\n        if (confirm('\\xcates-vous s\\xfbr de vouloir supprimer la r\\xe9ception \"'.concat(reception.supplierName, \" - \").concat(reception.orderNumber, '\" ?'))) {\n            try {\n                const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n                const updatedReceptions = savedReceptions.filter((r)=>r.id !== reception.id);\n                localStorage.setItem(\"shipment-receptions\", JSON.stringify(updatedReceptions));\n                // Recharger la liste\n                loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if ((receptionData === null || receptionData === void 0 ? void 0 : receptionData.id) === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(\"R\\xe9ception supprim\\xe9e avec succ\\xe8s\");\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception\");\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const estimatedLandedCost = cifDzd + totalAllFees;\n            // Créer un rapport détaillé pour cette réception\n            const reportContent = [\n                \"=== SHIPMENT RECEPTION DETAILED REPORT ===\",\n                \"\",\n                \"--- GENERAL INFORMATION ---\",\n                \"Supplier Name: \".concat(reception.supplierName),\n                \"Order Number: \".concat(reception.orderNumber),\n                \"Invoice Number: \".concat(reception.invoiceNumber),\n                \"Currency: \".concat(reception.currency),\n                \"Exchange Rate: \".concat(reception.exchangeRate.toFixed(5)),\n                \"Created At: \".concat(new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")),\n                \"\",\n                \"--- GOODS PRICE ---\",\n                \"FOB Amount (\".concat(reception.currency, \"): \").concat(reception.fobAmount.toFixed(2)),\n                \"Freight (\".concat(reception.currency, \"): \").concat(reception.freight.toFixed(2)),\n                \"CIF Amount (DZD): \".concat(cifDzd.toFixed(2)),\n                \"\",\n                \"--- CUSTOMS DUTIES ---\",\n                \"Customs Duties 1 TTC (DZD): \".concat(reception.customsDuties1Ttc.toFixed(2)),\n                \"Customs Duties 1 TVA (DZD): \".concat(reception.customsDuties1Tva.toFixed(2)),\n                \"Customs Duties 1 HT (DZD): \".concat(reception.customsDuties1Ht.toFixed(2)),\n                \"Customs Duties 2 HT (DZD): \".concat(reception.customsDuties2Ht.toFixed(2)),\n                \"TOTAL CUSTOMS DUTIES HT: \".concat(totalCustomsDuties.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- PORT FEES ---\",\n                \"Import Delivery TTC (DZD): \".concat(reception.importDeliveryTtc.toFixed(2)),\n                \"Import Delivery TVA (DZD): \".concat(reception.importDeliveryTva.toFixed(2)),\n                \"Import Delivery HT (DZD): \".concat(reception.importDeliveryHt.toFixed(2)),\n                \"Customs Inspection TTC (DZD): \".concat(reception.customsInspectionTtc.toFixed(2)),\n                \"Customs Inspection TVA (DZD): \".concat(reception.customsInspectionTva.toFixed(2)),\n                \"Customs Inspection HT (DZD): \".concat(reception.customsInspectionHt.toFixed(2)),\n                \"TOTAL PORT FEES HT: \".concat(totalPortFees.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- SHIPPING COMPANY FEES ---\",\n                \"Shipping Agency TTC (DZD): \".concat(reception.shippingAgencyTtc.toFixed(2)),\n                \"Shipping Agency TVA (DZD): \".concat(reception.shippingAgencyTva.toFixed(2)),\n                \"Shipping Agency HT (DZD): \".concat(reception.shippingAgencyHt.toFixed(2)),\n                \"Empty Containers TTC (DZD): \".concat(reception.emptyContainersTtc.toFixed(2)),\n                \"Empty Containers TVA (DZD): \".concat(reception.emptyContainersTva.toFixed(2)),\n                \"Empty Containers HT (DZD): \".concat(reception.emptyContainersHt.toFixed(2)),\n                \"Demurrage HT (DZD): \".concat(reception.demurrageHt.toFixed(2)),\n                \"TOTAL SHIPPING FEES HT: \".concat(totalShippingFees.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- MISCELLANEOUS EXPENSES ---\",\n                \"Misc Expenses TTC (DZD): \".concat(reception.miscExpensesTtc.toFixed(2)),\n                \"Misc Expenses TVA (DZD): \".concat(reception.miscExpensesTva.toFixed(2)),\n                \"Misc Expenses HT (DZD): \".concat(reception.miscExpensesHt.toFixed(2)),\n                \"\",\n                \"--- TRANSIT SERVICES ---\",\n                \"Transit Services TTC (DZD): \".concat(reception.transitServicesTtc.toFixed(2)),\n                \"Transit Services TVA (DZD): \".concat(reception.transitServicesTva.toFixed(2)),\n                \"Transit Services HT (DZD): \".concat(reception.transitServicesHt.toFixed(2)),\n                \"\",\n                \"=== SUMMARY ===\",\n                \"CIF Amount (DZD): \".concat(cifDzd.toFixed(2)),\n                \"Total All Fees HT (DZD): \".concat(totalAllFees.toFixed(2)),\n                \"ESTIMATED LANDED COST HT: \".concat(estimatedLandedCost.toFixed(2), \" DZD\"),\n                \"ESTIMATED LANDED COST COEFFICIENT: \".concat((estimatedLandedCost / (reception.fobAmount * reception.exchangeRate)).toFixed(4)),\n                \"\",\n                \"--- BREAKDOWN ---\",\n                \"FOB (DZD): \".concat((reception.fobAmount * reception.exchangeRate).toFixed(2)),\n                \"Freight (DZD): \".concat((reception.freight * reception.exchangeRate).toFixed(2)),\n                \"Customs Duties: \".concat(totalCustomsDuties.toFixed(2)),\n                \"Port Fees: \".concat(totalPortFees.toFixed(2)),\n                \"Shipping Fees: \".concat(totalShippingFees.toFixed(2)),\n                \"Misc Expenses: \".concat(reception.miscExpensesHt.toFixed(2)),\n                \"Transit Services: \".concat(reception.transitServicesHt.toFixed(2)),\n                \"\",\n                \"=== END OF REPORT ===\"\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier\n            const blob = new Blob([\n                reportContent\n            ], {\n                type: \"text/plain;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"reception_\".concat(reception.supplierName, \"_\").concat(reception.orderNumber, \"_\").concat(new Date().toISOString().split(\"T\")[0], \".txt\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export r\\xe9ussi pour la r\\xe9ception: \".concat(reception.supplierName, \" - \").concat(reception.orderNumber));\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? '\"'.concat(cell, '\"') : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"shipment_receptions_export_\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export r\\xe9ussi ! \".concat(allReceptions.length, \" r\\xe9ceptions export\\xe9es.\"));\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 702,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 743,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"CITHuXboUsUYvZZyHh6Xlw/KVr8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});