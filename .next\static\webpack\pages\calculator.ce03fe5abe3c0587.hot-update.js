"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,Save!=!lucide-react */ \"__barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_calculations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/calculations */ \"./lib/calculations.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    supplierName: \"\",\n    orderNumber: \"\",\n    orderDate: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    currency: \"USD\",\n    exchangeRate: 134.50000,\n    fobAmount: 0,\n    freight: 0,\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    demurrageHt: 0,\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    transitExpensesTtc: 0,\n    transitExpensesTva: 0\n};\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [calculation, setCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCalculating, setIsCalculating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const calculateCosts = ()=>{\n        setIsCalculating(true);\n        // Simulate calculation delay\n        setTimeout(()=>{\n            // Create mock shipment data\n            const shipment = {\n                fob_amount: form.fobAmount,\n                freight: form.freight,\n                exchange_rate_used: form.exchangeRate,\n                fob_amount_dzd: form.fobAmount * form.exchangeRate,\n                freight_dzd: form.freight * form.exchangeRate,\n                total_cif_dzd: (form.fobAmount + form.freight) * form.exchangeRate\n            };\n            // Create mock cost data\n            const customsDuties = {\n                customs_duties1_ttc: form.customsDuties1Ttc,\n                customs_duties1_tva: form.customsDuties1Tva,\n                customs_duties2_ttc: form.customsDuties2Ttc,\n                customs_duties2_tva: 0,\n                total_ttc: form.customsDuties1Ttc + form.customsDuties2Ttc,\n                total_tva: form.customsDuties1Tva + 0,\n                total_ht: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc // customsDuties2Ttc est déjà HT\n            };\n            const portFees = {\n                import_delivery_ttc: form.importDeliveryTtc,\n                import_delivery_tva: form.importDeliveryTva,\n                import_delivery_ht: form.importDeliveryTtc - form.importDeliveryTva,\n                customs_inspection_ttc: form.customsInspectionTtc,\n                customs_inspection_tva: form.customsInspectionTva,\n                customs_inspection_ht: form.customsInspectionTtc - form.customsInspectionTva\n            };\n            const shippingFees = {\n                shipping_agency_ttc: form.shippingAgencyTtc,\n                shipping_agency_tva: form.shippingAgencyTva,\n                shipping_agency_ht: form.shippingAgencyTtc - form.shippingAgencyTva,\n                empty_containers_ttc: form.emptyContainersTtc,\n                empty_containers_tva: form.emptyContainersTva,\n                empty_containers_ht: form.emptyContainersTtc - form.emptyContainersTva,\n                demurrage_ht: form.demurrageHt\n            };\n            const miscExpenses = {\n                amount_ttc: form.miscExpensesTtc,\n                amount_tva: form.miscExpensesTva,\n                amount_ht: form.miscExpensesTtc - form.miscExpensesTva\n            };\n            const transitExpenses = {\n                amount_ttc: form.transitExpensesTtc,\n                amount_tva: form.transitExpensesTva,\n                amount_ht: form.transitExpensesTtc - form.transitExpensesTva\n            };\n            const result = (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.calculateLandedCost)(shipment, customsDuties, portFees, shippingFees, miscExpenses, transitExpenses);\n            setCalculation(result);\n            setIsCalculating(false);\n        }, 1000);\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Cost Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Calculate landed costs for your imports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: calculateCosts,\n                                    disabled: isCalculating,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        isCalculating ? \"Calculating...\" : \"Calculate\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"General Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Basic shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Supplier Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Number *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.orderDate,\n                                                                onChange: (e)=>handleInputChange(\"orderDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Exchange Rate (to DZD) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"134.50000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        form.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"FOB and freight costs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"FOB Amount *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.fobAmount,\n                                                                    onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Freight\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.freight,\n                                                                    onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-4 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Total CIF (\",\n                                                                        form.currency,\n                                                                        \"):\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(form.fobAmount + form.freight)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)((form.fobAmount + form.freight) * form.exchangeRate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Customs Duties (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Algeria customs fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: 'Other Customs Duties (HT) \"T.E.L , A.M.D, .....\"'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.customsDuties2Ttc,\n                                                                            onChange: (e)=>handleInputChange(\"customsDuties2Ttc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 406,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Port Fees (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Port and customs inspection fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Import Delivery\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc,\n                                                                                onChange: (e)=>handleInputChange(\"importDeliveryTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTva,\n                                                                                onChange: (e)=>handleInputChange(\"importDeliveryTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc - form.importDeliveryTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Customs Inspection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTtc,\n                                                                                onChange: (e)=>handleInputChange(\"customsInspectionTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 505,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTva,\n                                                                                onChange: (e)=>handleInputChange(\"customsInspectionTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTtc - form.customsInspectionTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF PORT FEES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc + form.customsInspectionTtc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 559,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTva + form.customsInspectionTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc - form.importDeliveryTva + (form.customsInspectionTtc - form.customsInspectionTva),\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Shipping Fees (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Shipping company and container fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Shipping Agency Services\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc,\n                                                                                onChange: (e)=>handleInputChange(\"shippingAgencyTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTva,\n                                                                                onChange: (e)=>handleInputChange(\"shippingAgencyTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 617,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 626,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc - form.shippingAgencyTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Empty Containers Return\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTtc,\n                                                                                onChange: (e)=>handleInputChange(\"emptyContainersTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 648,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTva,\n                                                                                onChange: (e)=>handleInputChange(\"emptyContainersTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 660,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 669,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTtc - form.emptyContainersTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Demurrage (if present)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Demurrage HT (manually entered)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.demurrageHt,\n                                                                            onChange: (e)=>handleInputChange(\"demurrageHt\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF SHIPPING FEES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 704,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 720,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTva + form.emptyContainersTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc - form.shippingAgencyTva + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Other Miscellaneous Expenses (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Various additional costs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Other Miscellaneous Expenses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Total TTC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTtc,\n                                                                            onChange: (e)=>handleInputChange(\"miscExpensesTtc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"TVA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTva,\n                                                                            onChange: (e)=>handleInputChange(\"miscExpensesTva\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 778,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"HT (TTC - TVA)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTtc - form.miscExpensesTva,\n                                                                            readOnly: true,\n                                                                            className: \"bg-gray-50 text-gray-600\",\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Calculation Results\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: calculation ? \"Landed cost breakdown\" : \"Enter values and click Calculate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: calculation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.fobAmountDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Freight (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.freightDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalCifDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Cost Components (HT)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.customsDutiesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.portFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.shippingFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc. Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.miscExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 847,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.transitExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-primary\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost (HT):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.landedCostHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        calculation.landedCostCoefficient.toFixed(5),\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total Paid (TTC):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalPaidTtc)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        className: \"w-full\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.FileText, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Generate Report\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Enter your shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Fill in the form and click Calculate to see results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"M8a7FNAIapeOCsazbbvi3+CsTfA=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});