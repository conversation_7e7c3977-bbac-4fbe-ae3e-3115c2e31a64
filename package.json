{"name": "import-logistics-calculator", "version": "1.0.0", "description": "Modern Import & Logistics Cost Calculator with Authentication", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.0", "@radix-ui/react-tabs": "^1.0.0", "@radix-ui/react-toast": "^1.1.0", "@tailwindcss/forms": "^0.5.0", "@types/better-sqlite3": "^7.6.13", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.292.0", "next": "^14.2.15", "next-auth": "^4.24.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "xlsx": "^0.18.5", "zod": "^3.22.0"}, "devDependencies": {"@types/xlsx": "^0.0.36", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}