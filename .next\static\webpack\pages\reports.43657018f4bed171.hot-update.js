"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    // État pour gérer la liste des expéditions\n    const [shipments, setShipments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockData.recentShipments);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Mettre à jour la liste quand les données mockées changent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShipments(mockData.recentShipments);\n    }, []);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const handleViewShipment = (shipment)=>{\n        // Créer une popup ou modal avec les détails de l'expédition\n        const details = \"\\nD\\xc9TAILS DE L'EXP\\xc9DITION\\n\\nN\\xb0 Commande: \".concat(shipment.orderNumber, \"\\nFournisseur: \").concat(shipment.supplier, \"\\nDate: \").concat(shipment.date, \"\\nStatut: \").concat(shipment.status, \"\\n\\nINFORMATIONS FINANCI\\xc8RES:\\nFOB Amount: \").concat(formatUSD(shipment.fobAmount), \"\\nLanded Cost: \").concat(formatCurrency(shipment.landedCost), \"\\nCoefficient: \").concat(shipment.coefficient.toFixed(3), \"x\\nMarge: \").concat(((shipment.coefficient - 1) * 100).toFixed(1), \"%\\n\\nR\\xc9PARTITION ESTIM\\xc9E DES CO\\xdbTS:\\n• FOB Amount: \").concat(formatCurrency(shipment.landedCost * 0.65), \"\\n• Droits de douane: \").concat(formatCurrency(shipment.landedCost * 0.15), \"\\n• Frais portuaires: \").concat(formatCurrency(shipment.landedCost * 0.08), \"\\n• Frais de transport: \").concat(formatCurrency(shipment.landedCost * 0.07), \"\\n• Autres co\\xfbts: \").concat(formatCurrency(shipment.landedCost * 0.05), \"\\n    \");\n        alert(details);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"D\\xe9tails de l'exp\\xe9dition affich\\xe9s\");\n    };\n    const handleEditShipment = (shipment)=>{\n        // Rediriger vers la page du calculateur avec les données pré-remplies\n        const editUrl = \"/calculator?edit=\".concat(shipment.orderNumber, \"&supplier=\").concat(encodeURIComponent(shipment.supplier), \"&fob=\").concat(shipment.fobAmount, \"&date=\").concat(shipment.date);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Redirection vers l'\\xe9dition de \".concat(shipment.orderNumber));\n        // Dans une vraie application, on utiliserait router.push()\n        // router.push(editUrl)\n        // Pour la démo, on simule l'ouverture\n        console.log(\"Edit URL:\", editUrl);\n        alert(\"Redirection vers l'\\xe9dition de l'exp\\xe9dition \".concat(shipment.orderNumber, \"\\n\\nURL: \").concat(editUrl));\n    };\n    const handleDeleteShipment = (shipment)=>{\n        const confirmDelete = confirm(\"\\xcates-vous s\\xfbr de vouloir supprimer l'exp\\xe9dition \".concat(shipment.orderNumber, \" ?\\n\\n\") + \"Fournisseur: \".concat(shipment.supplier, \"\\n\") + \"FOB Amount: \".concat(formatUSD(shipment.fobAmount), \"\\n\") + \"Date: \".concat(shipment.date, \"\\n\\n\") + \"Cette action est irr\\xe9versible.\");\n        if (confirmDelete) {\n            // Dans une vraie application, on ferait un appel API pour supprimer\n            // await deleteShipment(shipment.id)\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Exp\\xe9dition \".concat(shipment.orderNumber, \" supprim\\xe9e avec succ\\xe8s\"));\n            // Simuler la suppression en retirant l'élément de la liste\n            console.log(\"Shipment deleted:\", shipment.orderNumber);\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"Suppression annul\\xe9e\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Configuration de base\n        const currency = \"USD\" // Dans une vraie app, ceci viendrait des données\n        ;\n        const exchangeRate = 134.50000 // Taux de change vers DZD\n        ;\n        const invoiceNumber = \"INV-2024-001\" // Simulé\n        ;\n        // Calculs de base\n        const fobAmount = shipment.fobAmount;\n        const freight = fobAmount * 0.15 // 15% du FOB\n        ;\n        const totalCif = fobAmount + freight;\n        // Conversions automatiques en DZD\n        const fobAmountDzd = fobAmount * exchangeRate;\n        const freightDzd = freight * exchangeRate;\n        const totalCifDzd = totalCif * exchangeRate;\n        // Valeurs simulées pour les frais (dans une vraie app, ces valeurs viendraient des champs de saisie)\n        // CUSTOMS DUTIES\n        const customsDuties1Ttc = 850000;\n        const customsDuties1Tva = 136000;\n        const customsDuties1Ht = customsDuties1Ttc - customsDuties1Tva;\n        const customsDuties2Ttc = 650000;\n        const customsDuties2Tva = 104000;\n        const customsDuties2Ht = customsDuties2Ttc - customsDuties2Tva;\n        const customsDutiesTotalTtc = customsDuties1Ttc + customsDuties2Ttc;\n        const customsDutiesTotalTva = customsDuties1Tva + customsDuties2Tva;\n        const customsDutiesTotalHt = customsDuties1Ht + customsDuties2Ht;\n        // PORT FEES\n        const importDeliveryTtc = 482344;\n        const importDeliveryTva = 77175;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406;\n        const customsInspectionTva = 46305;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalTva = importDeliveryTva + customsInspectionTva;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES\n        const shippingAgencyTtc = 675281;\n        const shippingAgencyTva = 108045;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875;\n        const emptyContainersTva = 61740;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // HT seulement\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalTva = shippingAgencyTva + emptyContainersTva;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES\n        const miscExpensesTtc = 482344;\n        const miscExpensesTva = 77175;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES\n        const transitExpensesTtc = 385875;\n        const transitExpensesTva = 61740;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs finaux\n        const landedCostTtc = totalCifDzd + customsDutiesTotalTtc + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + customsDutiesTotalHt + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const landedCostCoefficient = landedCostHt / fobAmountDzd;\n        const shipmentData = [\n            // En-tête\n            [\n                \"\",\n                \"\",\n                \"Valeur DZD\",\n                \"\\xc9quivalent \".concat(currency)\n            ],\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"---------------------------------------------\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INVOICE #:\",\n                invoiceNumber\n            ],\n            [\n                \"Exchange Rate Used : DZD/\" + currency + \":\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"\"\n            ],\n            // Goods Price in Currency\n            [\n                \"Goods Price in Currency (\" + currency + \"):\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT:\",\n                fobAmount.toLocaleString(\"fr-FR\"),\n                fobAmount.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT:\",\n                freight.toLocaleString(\"fr-FR\"),\n                freight.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF (FOB + Freight):\",\n                totalCif.toLocaleString(\"fr-FR\"),\n                totalCif.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            // Conversion to DZD\n            [\n                \"Conversion to DZD:\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT DZD (Automatic conversion):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\"),\n                (fobAmountDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT DZD (Automatic conversion):\",\n                freightDzd.toLocaleString(\"fr-FR\"),\n                (freightDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):\",\n                totalCifDzd.toLocaleString(\"fr-FR\"),\n                (totalCifDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"_____________________________________________________________________________________\"\n            ],\n            // Customs Duties\n            [\n                \"Customs Duties:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (ALGERIA CUSTOMS):\"\n            ],\n            [\n                \"\",\n                \"D3 N#:\",\n                \"D3-2024-001\"\n            ],\n            [\n                \"\",\n                \"D3 Date:\",\n                \"15/01/2024\"\n            ],\n            [\n                \"\",\n                \"QUITTANCE 1:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD Total TTC:\",\n                customsDuties1Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD TVA:\",\n                customsDuties1Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties1Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD HT (TTC - TVA):\",\n                customsDuties1Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"QUITTANCE 2:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD Total TTC:\",\n                customsDuties2Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD TVA:\",\n                customsDuties2Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties2Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD HT (TTC - TVA):\",\n                customsDuties2Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD Total TTC:',\n                customsDutiesTotalTtc.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD TVA:',\n                customsDutiesTotalTva.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD HT (TTC - TVA):',\n                customsDutiesTotalHt.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------------\"\n            ],\n            // Port Fees\n            [\n                \"PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (IMPORT DELIVERY):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE N#:\",\n                \"IMP-DEL-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY Total TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\"),\n                (importDeliveryTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\"),\n                (importDeliveryTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY HT (TTC - TVA):\",\n                importDeliveryHt.toLocaleString(\"fr-FR\"),\n                (importDeliveryHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (CUSTOMS INSPECTION):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE N#:\",\n                \"CUST-INS-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION Total TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\"),\n                (customsInspectionTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\"),\n                (customsInspectionTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION HT (TTC - TVA):\",\n                customsInspectionHt.toLocaleString(\"fr-FR\"),\n                (customsInspectionHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD Total TTC:',\n                portFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD TVA:',\n                portFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                portFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (portFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"---------------------------------------------------------------------------------------------------------------\"\n            ],\n            // Shipping Company Fees\n            [\n                \"SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE N#:\",\n                \"SHIP-AGE-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE DATE:\",\n                \"17/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES Total TTC (DZD):\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES TVA (DZD):\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\"),\n                (shippingAgencyHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (EMPTY CONTAINERS):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE N#:\",\n                \"EMPTY-CONT-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE DATE:\",\n                \"18/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN Total TTC (DZD):\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\"),\n                (emptyContainersTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN TVA (DZD):\",\n                emptyContainersTva.toLocaleString(\"fr-FR\"),\n                (emptyContainersTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA):\",\n                emptyContainersHt.toLocaleString(\"fr-FR\"),\n                (emptyContainersHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE N#:\",\n                \"DEMUR-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE DATE:\",\n                \"19/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE HT (DZD) (This currency field must be entered manually.):\",\n                demurrageHt.toLocaleString(\"fr-FR\"),\n                (demurrageHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC:',\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA:',\n                shippingFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"-------------------------------------------------------\"\n            ],\n            // Other Miscellaneous Expenses\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE N#:\",\n                \"MISC-EXP-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE:\",\n                \"20/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TTC (DZD):\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\"),\n                (miscExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TVA (DZD):\",\n                miscExpensesTva.toLocaleString(\"fr-FR\"),\n                (miscExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):\",\n                miscExpensesHt.toLocaleString(\"fr-FR\"),\n                (miscExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------\"\n            ],\n            // Transit Services Expenses\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE N#:\",\n                \"TRANS-SERV-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE DATE:\",\n                \"21/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TTC (DZD):\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\"),\n                (transitExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TVA (DZD):\",\n                transitExpensesTva.toLocaleString(\"fr-FR\"),\n                (transitExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):\",\n                transitExpensesHt.toLocaleString(\"fr-FR\"),\n                (transitExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\"\n            ],\n            // Calculs finaux\n            [\n                \"CALCULS FINAUX:\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostHt / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES HT + OTHER MISCELLANEOUS EXPENSES HT + SHIPPING COMPANY FEES Overall Totals HT + PORT FEES Overall Totals HT + CUSTOMS DUTIES Overall Totals HT + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostTtc / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES TTC + OTHER MISCELLANEOUS EXPENSES TTC + SHIPPING COMPANY FEES Overall Totals TTC + PORT FEES Overall Totals TTC + CUSTOMS DUTIES Overall Totals TTC + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed cost coefficient:\",\n                landedCostCoefficient.toFixed(5),\n                landedCostCoefficient.toFixed(5)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 550,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 549,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleViewShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Voir les d\\xe9tails\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Eye, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleEditShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Edit, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 824,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleDeleteShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:border-red-300\",\n                                                                        title: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Trash2, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 835,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                        className: \"h-8 px-2\",\n                                                                        title: \"Exporter en Excel\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Export\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 563,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 562,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"vi0zUBlEbKpvy4OAJiMYSrdG9SE=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});