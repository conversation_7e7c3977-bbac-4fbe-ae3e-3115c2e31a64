/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/reports";
exports.ids = ["pages/reports"];
exports.modules = {

/***/ "__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bar: () => (/* reexport safe */ _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__.Bar),\n/* harmony export */   BarChart: () => (/* reexport safe */ _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__.BarChart),\n/* harmony export */   CartesianGrid: () => (/* reexport safe */ _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__.CartesianGrid),\n/* harmony export */   Cell: () => (/* reexport safe */ _component_Cell__WEBPACK_IMPORTED_MODULE_3__.Cell),\n/* harmony export */   PieChart: () => (/* reexport safe */ _chart_PieChart__WEBPACK_IMPORTED_MODULE_4__.PieChart),\n/* harmony export */   ResponsiveContainer: () => (/* reexport safe */ _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer),\n/* harmony export */   Tooltip: () => (/* reexport safe */ _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__.Tooltip),\n/* harmony export */   XAxis: () => (/* reexport safe */ _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__.XAxis),\n/* harmony export */   YAxis: () => (/* reexport safe */ _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__.YAxis)\n/* harmony export */ });\n/* harmony import */ var _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cartesian/Bar */ \"./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chart/BarChart */ \"./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian/CartesianGrid */ \"./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _component_Cell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./component/Cell */ \"./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _chart_PieChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chart/PieChart */ \"./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./component/ResponsiveContainer */ \"./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./component/Tooltip */ \"./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./cartesian/XAxis */ \"./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./cartesian/YAxis */ \"./node_modules/recharts/es6/cartesian/YAxis.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__, _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _chart_PieChart__WEBPACK_IMPORTED_MODULE_4__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__]);\n([_cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__, _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _chart_PieChart__WEBPACK_IMPORTED_MODULE_4__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_5__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_6__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_7__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXIsQmFyQ2hhcnQsQ2FydGVzaWFuR3JpZCxDZWxsLFBpZUNoYXJ0LFJlc3BvbnNpdmVDb250YWluZXIsVG9vbHRpcCxYQXhpcyxZQXhpcyE9IS4vbm9kZV9tb2R1bGVzL3JlY2hhcnRzL2VzNi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3FDO0FBQ007QUFDYztBQUNsQjtBQUNJO0FBQzBCO0FBQ3hCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbXBvcnQtbG9naXN0aWNzLWNhbGN1bGF0b3IvLi9ub2RlX21vZHVsZXMvcmVjaGFydHMvZXM2L2luZGV4LmpzPzcxZWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBCYXIgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vQmFyXCJcbmV4cG9ydCB7IEJhckNoYXJ0IH0gZnJvbSBcIi4vY2hhcnQvQmFyQ2hhcnRcIlxuZXhwb3J0IHsgQ2FydGVzaWFuR3JpZCB9IGZyb20gXCIuL2NhcnRlc2lhbi9DYXJ0ZXNpYW5HcmlkXCJcbmV4cG9ydCB7IENlbGwgfSBmcm9tIFwiLi9jb21wb25lbnQvQ2VsbFwiXG5leHBvcnQgeyBQaWVDaGFydCB9IGZyb20gXCIuL2NoYXJ0L1BpZUNoYXJ0XCJcbmV4cG9ydCB7IFJlc3BvbnNpdmVDb250YWluZXIgfSBmcm9tIFwiLi9jb21wb25lbnQvUmVzcG9uc2l2ZUNvbnRhaW5lclwiXG5leHBvcnQgeyBUb29sdGlwIH0gZnJvbSBcIi4vY29tcG9uZW50L1Rvb2x0aXBcIlxuZXhwb3J0IHsgWEF4aXMgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vWEF4aXNcIlxuZXhwb3J0IHsgWUF4aXMgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vWUF4aXNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calculator: () => (/* reexport safe */ _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Ship: () => (/* reexport safe */ _icons_ship_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calculator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calculator.js */ \"./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_ship_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/ship.js */ \"./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsY3VsYXRvcixGaWxlVGV4dCxMb2dPdXQsTWVudSxQYWNrYWdlLFNldHRpbmdzLFNoaXAsVXNlcixYIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDQTtBQUNIO0FBQ0o7QUFDTDtBQUNNO0FBQ0U7QUFDUjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/YWRmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxjdWxhdG9yIH0gZnJvbSBcIi4vaWNvbnMvY2FsY3VsYXRvci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpcCB9IGZyb20gXCIuL2ljb25zL3NoaXAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   DollarSign: () => (/* reexport safe */ _icons_dollar_sign_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Edit: () => (/* reexport safe */ _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   PieChart: () => (/* reexport safe */ _icons_pie_chart_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_dollar_sign_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/dollar-sign.js */ \"./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/download.js */ \"./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/pen-square.js */ \"./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_pie_chart_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/pie-chart.js */ \"./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/trending-up.js */ \"./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsZW5kYXIsRG9sbGFyU2lnbixEb3dubG9hZCxFZGl0LEV5ZSxGaWxlVGV4dCxQYWNrYWdlLFBpZUNoYXJ0LFRyYXNoMixUcmVuZGluZ1VwIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNKO0FBQ0s7QUFDTDtBQUNGO0FBQ1I7QUFDVztBQUNIO0FBQ0c7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL2ltcG9ydC1sb2dpc3RpY3MtY2FsY3VsYXRvci8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzZhMGMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhckNoYXJ0MyB9IGZyb20gXCIuL2ljb25zL2Jhci1jaGFydC0zLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsZW5kYXIgfSBmcm9tIFwiLi9pY29ucy9jYWxlbmRhci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvbGxhclNpZ24gfSBmcm9tIFwiLi9pY29ucy9kb2xsYXItc2lnbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvd25sb2FkIH0gZnJvbSBcIi4vaWNvbnMvZG93bmxvYWQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFZGl0IH0gZnJvbSBcIi4vaWNvbnMvcGVuLXNxdWFyZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV5ZSB9IGZyb20gXCIuL2ljb25zL2V5ZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQaWVDaGFydCB9IGZyb20gXCIuL2ljb25zL3BpZS1jaGFydC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoMiB9IGZyb20gXCIuL2ljb25zL3RyYXNoLTIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmVuZGluZ1VwIH0gZnJvbSBcIi4vaWNvbnMvdHJlbmRpbmctdXAuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\reports.tsx */ \"./pages/reports.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/reports\",\n        pathname: \"/reports\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_reports_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calculator,FileText,LogOut,Menu,Package,Settings,Ship,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst getNavigation = (userRole)=>{\n    const baseNavigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.BarChart3\n        },\n        {\n            name: \"Shipment Reception\",\n            href: \"/shipment-reception\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Ship\n        },\n        {\n            name: \"Cost Calculator\",\n            href: \"/calculator\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator\n        },\n        {\n            name: \"Order Management\",\n            href: \"/orders\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Package\n        },\n        {\n            name: \"Reports\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.FileText\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings\n        }\n    ];\n    // Add admin-only navigation for super admin\n    if (userRole === \"super_admin\") {\n        baseNavigation.splice(-1, 0, {\n            name: \"User Management\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User\n        });\n    }\n    return baseNavigation;\n};\nfunction Layout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [sidebarOpen, setSidebarOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const navigation = getNavigation(session?.user?.role);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-0 z-50 lg:hidden\", sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-16 items-center justify-between px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Import Calculator\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 space-y-1 px-2 py-4\",\n                                children: navigation.map((item)=>{\n                                    const isActive = router.pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center flex-shrink-0 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calculator, {\n                                    className: \"h-8 w-8 text-primary mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Import Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"mt-8 flex-1 space-y-1 px-2\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md\", isActive ? \"bg-primary text-primary-foreground\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 flex-shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Menu, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: session.user?.name || session.user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        session.user?.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800\",\n                                                            children: session.user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                    className: \"text-gray-500 hover:text-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calculator_FileText_LogOut_Menu_Package_Settings_Ship_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Layout.tsx\n");

/***/ }),

/***/ "./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/button.tsx\n");

/***/ }),

/***/ "./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/card.tsx\n");

/***/ }),

/***/ "./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/input.tsx\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1wb3J0LWxvZ2lzdGljcy1jYWxjdWxhdG9yLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4ade80\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\_app.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Reports)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,Edit,Eye,FileText,Package,PieChart,Trash2,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! xlsx */ \"xlsx\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(xlsx__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__, react_hot_toast__WEBPACK_IMPORTED_MODULE_9__, _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__]);\n([_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__, _components_ui_button__WEBPACK_IMPORTED_MODULE_6__, _components_ui_input__WEBPACK_IMPORTED_MODULE_7__, react_hot_toast__WEBPACK_IMPORTED_MODULE_9__, _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    // État pour gérer la liste des expéditions\n    const [shipments, setShipments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockData.recentShipments);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Mettre à jour la liste quand les données mockées changent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShipments(mockData.recentShipments);\n    }, []);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = `${reportType}_Report_${new Date().toISOString().split(\"T\")[0]}.xlsx`;\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_8__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(`Rapport \"${fileName}\" généré et téléchargé avec succès!`);\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const handleViewShipment = (shipment)=>{\n        // Créer une popup ou modal avec les détails de l'expédition\n        const details = `\nDÉTAILS DE L'EXPÉDITION\n\nN° Commande: ${shipment.orderNumber}\nFournisseur: ${shipment.supplier}\nDate: ${shipment.date}\nStatut: ${shipment.status}\n\nINFORMATIONS FINANCIÈRES:\nFOB Amount: ${formatUSD(shipment.fobAmount)}\nLanded Cost: ${formatCurrency(shipment.landedCost)}\nCoefficient: ${shipment.coefficient.toFixed(3)}x\nMarge: ${((shipment.coefficient - 1) * 100).toFixed(1)}%\n\nRÉPARTITION ESTIMÉE DES COÛTS:\n• FOB Amount: ${formatCurrency(shipment.landedCost * 0.65)}\n• Droits de douane: ${formatCurrency(shipment.landedCost * 0.15)}\n• Frais portuaires: ${formatCurrency(shipment.landedCost * 0.08)}\n• Frais de transport: ${formatCurrency(shipment.landedCost * 0.07)}\n• Autres coûts: ${formatCurrency(shipment.landedCost * 0.05)}\n    `;\n        alert(details);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"D\\xe9tails de l'exp\\xe9dition affich\\xe9s\");\n    };\n    const handleEditShipment = (shipment)=>{\n        // Rediriger vers la page du calculateur avec les données pré-remplies\n        const editUrl = `/calculator?edit=${shipment.orderNumber}&supplier=${encodeURIComponent(shipment.supplier)}&fob=${shipment.fobAmount}&date=${shipment.date}`;\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(`Redirection vers l'édition de ${shipment.orderNumber}`);\n        // Dans une vraie application, on utiliserait router.push()\n        // router.push(editUrl)\n        // Pour la démo, on simule l'ouverture\n        console.log(\"Edit URL:\", editUrl);\n        alert(`Redirection vers l'édition de l'expédition ${shipment.orderNumber}\\n\\nURL: ${editUrl}`);\n    };\n    const handleDeleteShipment = (shipment)=>{\n        const confirmDelete = confirm(`Êtes-vous sûr de vouloir supprimer l'expédition ${shipment.orderNumber} ?\\n\\n` + `Fournisseur: ${shipment.supplier}\\n` + `FOB Amount: ${formatUSD(shipment.fobAmount)}\\n` + `Date: ${shipment.date}\\n\\n` + `Cette action est irréversible.`);\n        if (confirmDelete) {\n            // Supprimer réellement l'expédition de la liste\n            setShipments((prevShipments)=>prevShipments.filter((s)=>s.id !== shipment.id));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(`Expédition ${shipment.orderNumber} supprimée avec succès`);\n            // Dans une vraie application, on ferait aussi un appel API pour supprimer\n            // await deleteShipment(shipment.id)\n            console.log(\"Shipment deleted:\", shipment.orderNumber);\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].info(\"Suppression annul\\xe9e\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                `${dateRange.from} à ${dateRange.to}`\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    `${item.value}%`,\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                `${dateRange.from} à ${dateRange.to}`\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    `${item.value}%`,\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    `${((s.coefficient - 1) * 100).toFixed(1)}%`\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                `${dateRange.from} à ${dateRange.to}`\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? `${((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1)}%` : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = shipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_new();\n        // Configuration de base\n        const currency = \"USD\" // Dans une vraie app, ceci viendrait des données\n        ;\n        const exchangeRate = 134.50000 // Taux de change vers DZD\n        ;\n        const invoiceNumber = \"INV-2024-001\" // Simulé\n        ;\n        // Calculs de base\n        const fobAmount = shipment.fobAmount;\n        const freight = fobAmount * 0.15 // 15% du FOB\n        ;\n        const totalCif = fobAmount + freight;\n        // Conversions automatiques en DZD\n        const fobAmountDzd = fobAmount * exchangeRate;\n        const freightDzd = freight * exchangeRate;\n        const totalCifDzd = totalCif * exchangeRate;\n        // Valeurs simulées pour les frais (dans une vraie app, ces valeurs viendraient des champs de saisie)\n        // CUSTOMS DUTIES\n        const customsDuties1Ttc = 850000;\n        const customsDuties1Tva = 136000;\n        const customsDuties1Ht = customsDuties1Ttc - customsDuties1Tva;\n        const customsDuties2Ttc = 650000;\n        const customsDuties2Tva = 104000;\n        const customsDuties2Ht = customsDuties2Ttc - customsDuties2Tva;\n        const customsDutiesTotalTtc = customsDuties1Ttc + customsDuties2Ttc;\n        const customsDutiesTotalTva = customsDuties1Tva + customsDuties2Tva;\n        const customsDutiesTotalHt = customsDuties1Ht + customsDuties2Ht;\n        // PORT FEES\n        const importDeliveryTtc = 482344;\n        const importDeliveryTva = 77175;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406;\n        const customsInspectionTva = 46305;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalTva = importDeliveryTva + customsInspectionTva;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES\n        const shippingAgencyTtc = 675281;\n        const shippingAgencyTva = 108045;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875;\n        const emptyContainersTva = 61740;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // HT seulement\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalTva = shippingAgencyTva + emptyContainersTva;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES\n        const miscExpensesTtc = 482344;\n        const miscExpensesTva = 77175;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES\n        const transitExpensesTtc = 385875;\n        const transitExpensesTva = 61740;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs finaux\n        const landedCostTtc = totalCifDzd + customsDutiesTotalTtc + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + customsDutiesTotalHt + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const landedCostCoefficient = landedCostHt / fobAmountDzd;\n        const shipmentData = [\n            // En-tête\n            [\n                \"\",\n                \"\",\n                \"Valeur DZD\",\n                `Équivalent ${currency}`\n            ],\n            [\n                `RAPPORT D'EXPÉDITION - ${orderNumber}`\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"---------------------------------------------\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INVOICE #:\",\n                invoiceNumber\n            ],\n            [\n                \"Exchange Rate Used : DZD/\" + currency + \":\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"\"\n            ],\n            // Goods Price in Currency\n            [\n                \"Goods Price in Currency (\" + currency + \"):\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT:\",\n                fobAmount.toLocaleString(\"fr-FR\"),\n                fobAmount.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT:\",\n                freight.toLocaleString(\"fr-FR\"),\n                freight.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF (FOB + Freight):\",\n                totalCif.toLocaleString(\"fr-FR\"),\n                totalCif.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            // Conversion to DZD\n            [\n                \"Conversion to DZD:\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT DZD (Automatic conversion):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\"),\n                (fobAmountDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT DZD (Automatic conversion):\",\n                freightDzd.toLocaleString(\"fr-FR\"),\n                (freightDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):\",\n                totalCifDzd.toLocaleString(\"fr-FR\"),\n                (totalCifDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"_____________________________________________________________________________________\"\n            ],\n            // Customs Duties\n            [\n                \"Customs Duties:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (ALGERIA CUSTOMS):\"\n            ],\n            [\n                \"\",\n                \"D3 N#:\",\n                \"D3-2024-001\"\n            ],\n            [\n                \"\",\n                \"D3 Date:\",\n                \"15/01/2024\"\n            ],\n            [\n                \"\",\n                \"QUITTANCE 1:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD Total TTC:\",\n                customsDuties1Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD TVA:\",\n                customsDuties1Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties1Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD HT (TTC - TVA):\",\n                customsDuties1Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"QUITTANCE 2:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD Total TTC:\",\n                customsDuties2Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD TVA:\",\n                customsDuties2Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties2Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD HT (TTC - TVA):\",\n                customsDuties2Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD Total TTC:',\n                customsDutiesTotalTtc.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD TVA:',\n                customsDutiesTotalTva.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD HT (TTC - TVA):',\n                customsDutiesTotalHt.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------------\"\n            ],\n            // Port Fees\n            [\n                \"PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (IMPORT DELIVERY):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE N#:\",\n                \"IMP-DEL-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY Total TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\"),\n                (importDeliveryTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\"),\n                (importDeliveryTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY HT (TTC - TVA):\",\n                importDeliveryHt.toLocaleString(\"fr-FR\"),\n                (importDeliveryHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (CUSTOMS INSPECTION):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE N#:\",\n                \"CUST-INS-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION Total TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\"),\n                (customsInspectionTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\"),\n                (customsInspectionTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION HT (TTC - TVA):\",\n                customsInspectionHt.toLocaleString(\"fr-FR\"),\n                (customsInspectionHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD Total TTC:',\n                portFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD TVA:',\n                portFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                portFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (portFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"---------------------------------------------------------------------------------------------------------------\"\n            ],\n            // Shipping Company Fees\n            [\n                \"SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE N#:\",\n                \"SHIP-AGE-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE DATE:\",\n                \"17/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES Total TTC (DZD):\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES TVA (DZD):\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\"),\n                (shippingAgencyHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (EMPTY CONTAINERS):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE N#:\",\n                \"EMPTY-CONT-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE DATE:\",\n                \"18/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN Total TTC (DZD):\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\"),\n                (emptyContainersTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN TVA (DZD):\",\n                emptyContainersTva.toLocaleString(\"fr-FR\"),\n                (emptyContainersTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA):\",\n                emptyContainersHt.toLocaleString(\"fr-FR\"),\n                (emptyContainersHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE N#:\",\n                \"DEMUR-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE DATE:\",\n                \"19/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE HT (DZD) (This currency field must be entered manually.):\",\n                demurrageHt.toLocaleString(\"fr-FR\"),\n                (demurrageHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC:',\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA:',\n                shippingFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"-------------------------------------------------------\"\n            ],\n            // Other Miscellaneous Expenses\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE N#:\",\n                \"MISC-EXP-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE:\",\n                \"20/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TTC (DZD):\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\"),\n                (miscExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TVA (DZD):\",\n                miscExpensesTva.toLocaleString(\"fr-FR\"),\n                (miscExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):\",\n                miscExpensesHt.toLocaleString(\"fr-FR\"),\n                (miscExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------\"\n            ],\n            // Transit Services Expenses\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE N#:\",\n                \"TRANS-SERV-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE DATE:\",\n                \"21/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TTC (DZD):\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\"),\n                (transitExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TVA (DZD):\",\n                transitExpensesTva.toLocaleString(\"fr-FR\"),\n                (transitExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):\",\n                transitExpensesHt.toLocaleString(\"fr-FR\"),\n                (transitExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\"\n            ],\n            // Calculs finaux\n            [\n                \"CALCULS FINAUX:\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostHt / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES HT + OTHER MISCELLANEOUS EXPENSES HT + SHIPPING COMPANY FEES Overall Totals HT + PORT FEES Overall Totals HT + CUSTOMS DUTIES Overall Totals HT + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostTtc / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES TTC + OTHER MISCELLANEOUS EXPENSES TTC + SHIPPING COMPANY FEES Overall Totals TTC + PORT FEES Overall Totals TTC + CUSTOMS DUTIES Overall Totals TTC + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed cost coefficient:\",\n                landedCostCoefficient.toFixed(5),\n                landedCostCoefficient.toFixed(5)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(wb, ws, `Expédition ${orderNumber}`);\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 553,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 552,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                `${value}%`,\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, `cell-${index}`, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 723,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: shipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleViewShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Voir les d\\xe9tails\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Eye, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleEditShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        title: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Edit, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleDeleteShipment(shipment),\n                                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:border-red-300\",\n                                                                        title: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Trash2, {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleExportReport(`Shipment_${shipment.orderNumber}`),\n                                                                        className: \"h-8 px-2\",\n                                                                        title: \"Exporter en Excel\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_Edit_Eye_FileText_Package_PieChart_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Export\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 842,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 566,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 565,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "eventemitter3":
/*!********************************!*\
  !*** external "eventemitter3" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("eventemitter3");

/***/ }),

/***/ "lodash/every":
/*!*******************************!*\
  !*** external "lodash/every" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/every");

/***/ }),

/***/ "lodash/find":
/*!******************************!*\
  !*** external "lodash/find" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/find");

/***/ }),

/***/ "lodash/flatMap":
/*!*********************************!*\
  !*** external "lodash/flatMap" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/flatMap");

/***/ }),

/***/ "lodash/get":
/*!*****************************!*\
  !*** external "lodash/get" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/get");

/***/ }),

/***/ "lodash/isBoolean":
/*!***********************************!*\
  !*** external "lodash/isBoolean" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isBoolean");

/***/ }),

/***/ "lodash/isEqual":
/*!*********************************!*\
  !*** external "lodash/isEqual" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isEqual");

/***/ }),

/***/ "lodash/isFunction":
/*!************************************!*\
  !*** external "lodash/isFunction" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isFunction");

/***/ }),

/***/ "lodash/isNaN":
/*!*******************************!*\
  !*** external "lodash/isNaN" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNaN");

/***/ }),

/***/ "lodash/isNil":
/*!*******************************!*\
  !*** external "lodash/isNil" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNil");

/***/ }),

/***/ "lodash/isNumber":
/*!**********************************!*\
  !*** external "lodash/isNumber" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNumber");

/***/ }),

/***/ "lodash/isObject":
/*!**********************************!*\
  !*** external "lodash/isObject" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isObject");

/***/ }),

/***/ "lodash/isPlainObject":
/*!***************************************!*\
  !*** external "lodash/isPlainObject" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isPlainObject");

/***/ }),

/***/ "lodash/isString":
/*!**********************************!*\
  !*** external "lodash/isString" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isString");

/***/ }),

/***/ "lodash/last":
/*!******************************!*\
  !*** external "lodash/last" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/last");

/***/ }),

/***/ "lodash/mapValues":
/*!***********************************!*\
  !*** external "lodash/mapValues" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/mapValues");

/***/ }),

/***/ "lodash/max":
/*!*****************************!*\
  !*** external "lodash/max" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/max");

/***/ }),

/***/ "lodash/maxBy":
/*!*******************************!*\
  !*** external "lodash/maxBy" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/maxBy");

/***/ }),

/***/ "lodash/memoize":
/*!*********************************!*\
  !*** external "lodash/memoize" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/memoize");

/***/ }),

/***/ "lodash/min":
/*!*****************************!*\
  !*** external "lodash/min" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/min");

/***/ }),

/***/ "lodash/minBy":
/*!*******************************!*\
  !*** external "lodash/minBy" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/minBy");

/***/ }),

/***/ "lodash/range":
/*!*******************************!*\
  !*** external "lodash/range" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/range");

/***/ }),

/***/ "lodash/some":
/*!******************************!*\
  !*** external "lodash/some" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/some");

/***/ }),

/***/ "lodash/sortBy":
/*!********************************!*\
  !*** external "lodash/sortBy" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/sortBy");

/***/ }),

/***/ "lodash/throttle":
/*!**********************************!*\
  !*** external "lodash/throttle" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/throttle");

/***/ }),

/***/ "lodash/uniqBy":
/*!********************************!*\
  !*** external "lodash/uniqBy" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/uniqBy");

/***/ }),

/***/ "lodash/upperFirst":
/*!************************************!*\
  !*** external "lodash/upperFirst" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/upperFirst");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-smooth":
/*!*******************************!*\
  !*** external "react-smooth" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-smooth");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "recharts-scale":
/*!*********************************!*\
  !*** external "recharts-scale" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("recharts-scale");

/***/ }),

/***/ "victory-vendor/d3-scale":
/*!******************************************!*\
  !*** external "victory-vendor/d3-scale" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("victory-vendor/d3-scale");

/***/ }),

/***/ "victory-vendor/d3-shape":
/*!******************************************!*\
  !*** external "victory-vendor/d3-shape" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("victory-vendor/d3-shape");

/***/ }),

/***/ "xlsx":
/*!***********************!*\
  !*** external "xlsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("xlsx");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "tiny-invariant":
/*!*********************************!*\
  !*** external "tiny-invariant" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tiny-invariant");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/recharts"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();