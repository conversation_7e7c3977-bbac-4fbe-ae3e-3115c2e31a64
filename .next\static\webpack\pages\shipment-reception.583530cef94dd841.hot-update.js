"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/shipment-reception",{

/***/ "./pages/shipment-reception.tsx":
/*!**************************************!*\
  !*** ./pages/shipment-reception.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShipmentReception; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Save,Ship!=!lucide-react */ \"__barrel_optimize__?names=FileText,Save,Ship!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    // Arrival Notice\n    voyageNumber: \"\",\n    billOfLading: \"\",\n    vesselName: \"\",\n    shipowner: \"\",\n    actualTimeOfArrival: \"\",\n    // General Information\n    exchangeRate: 0,\n    currency: \"USD\",\n    supplierName: \"\",\n    shipmentType: \"SEA\",\n    orderNumber: \"\",\n    dateOfOrderNumber: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    fromLocation: \"\",\n    toLocation: \"\",\n    operationType: \"OPEX\",\n    descriptionOfGoods: \"SPARE_PARTS_VEHICLES\",\n    bankName: \"\",\n    lcNumber: \"\",\n    validationLC: \"\",\n    paymentTerm: \"\",\n    priceTerm: \"\",\n    quantityPcs: 0,\n    numberContainers20: 0,\n    numberContainers40: 0,\n    numberPackages: 0,\n    // Goods Price in Currency\n    fobAmount: 0,\n    freight: 0,\n    totalAmountCif: 0,\n    // Conversion to DZD (calculated automatically)\n    fobAmountDzd: 0,\n    freightDzd: 0,\n    totalAmountCifDzd: 0,\n    // Customs Duties\n    costAllocationNameCustoms: \"ALGERIA CUSTOMS\",\n    d3Number: \"\",\n    d3Date: \"\",\n    // Quittance 1\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties1Ht: 0,\n    // Quittance 2\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    customsDuties2Ht: 0,\n    // Overall Totals Customs Duties\n    customsDutiesOverallTtc: 0,\n    customsDutiesOverallTva: 0,\n    customsDutiesOverallHt: 0,\n    // Port Fees - Import Delivery\n    costAllocationNameImportDelivery: \"IMPORT DELIVERY\",\n    importDeliveryInvoiceNumber: \"\",\n    importDeliveryInvoiceDate: \"\",\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    importDeliveryHt: 0,\n    // Port Fees - Customs Inspection\n    costAllocationNameCustomsInspection: \"CUSTOMS INSPECTION\",\n    customsInspectionInvoiceNumber: \"\",\n    customsInspectionInvoiceDate: \"\",\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    customsInspectionHt: 0,\n    // Overall Totals Port Fees\n    portFeesOverallTtc: 0,\n    portFeesOverallTva: 0,\n    portFeesOverallHt: 0,\n    // Shipping Company Fees - Shipping Agency Services\n    costAllocationNameShippingAgency: \"SHIPPING AGENCY SERVICES\",\n    shippingAgencyInvoiceNumber: \"\",\n    shippingAgencyInvoiceDate: \"\",\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    shippingAgencyHt: 0,\n    // Shipping Company Fees - Empty Containers\n    costAllocationNameEmptyContainers: \"EMPTY CONTAINERS\",\n    emptyContainersInvoiceNumber: \"\",\n    emptyContainersInvoiceDate: \"\",\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    emptyContainersHt: 0,\n    // Shipping Company Fees - Demurrage\n    costAllocationNameDemurrage: \"DEMURRAGE IF PRESENT\",\n    demurrageInvoiceNumber: \"\",\n    demurrageInvoiceDate: \"\",\n    demurrageHt: 0,\n    // Overall Totals Shipping Company Fees\n    shippingFeesOverallTtc: 0,\n    shippingFeesOverallTva: 0,\n    shippingFeesOverallHt: 0,\n    // Other Miscellaneous Expenses\n    costAllocationNameMiscExpenses: \"OTHER MISCELLANEOUS EXPENSES\",\n    miscExpensesInvoiceNumber: \"\",\n    miscExpensesInvoiceDate: \"\",\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    miscExpensesHt: 0,\n    // Transit Services Expenses\n    costAllocationNameTransitServices: \"TRANSIT SERVICES EXPENSES\",\n    transitServicesInvoiceNumber: \"\",\n    transitServicesInvoiceDate: \"\",\n    transitServicesTtc: 0,\n    transitServicesTva: 0,\n    transitServicesHt: 0\n};\nfunction ShipmentReception() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [currentExchangeRate, setCurrentExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoadingExchangeRate, setIsLoadingExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Récupérer le taux de change depuis les Settings de l'admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchExchangeRate = async ()=>{\n            try {\n                setIsLoadingExchangeRate(true);\n                // TODO: Remplacer par l'API réelle des Settings\n                // const response = await fetch('/api/settings/exchange-rate')\n                // const data = await response.json()\n                // Pour l'instant, simulation avec localStorage ou valeur par défaut\n                const savedRate = localStorage.getItem(\"admin-exchange-rate\");\n                const rate = savedRate ? parseFloat(savedRate) : 134.50000;\n                setCurrentExchangeRate(rate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: rate\n                    }));\n            } catch (error) {\n                console.error(\"Error fetching exchange rate:\", error);\n                // Valeur de fallback\n                const fallbackRate = 134.50000;\n                setCurrentExchangeRate(fallbackRate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: fallbackRate\n                    }));\n            } finally{\n                setIsLoadingExchangeRate(false);\n            }\n        };\n        if (status === \"authenticated\") {\n            fetchExchangeRate();\n        }\n    }, [\n        status\n    ]);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>{\n            const updated = {\n                ...prev,\n                [field]: value\n            };\n            // Calculs automatiques pour les conversions DZD\n            if (field === \"fobAmount\" || field === \"exchangeRate\") {\n                updated.fobAmountDzd = updated.fobAmount * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            if (field === \"freight\" || field === \"exchangeRate\") {\n                updated.freightDzd = updated.freight * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            // Calculs automatiques pour les champs HT\n            if (field === \"customsDuties1Ttc\" || field === \"customsDuties1Tva\") {\n                updated.customsDuties1Ht = updated.customsDuties1Ttc - updated.customsDuties1Tva;\n            }\n            if (field === \"customsDuties2Ttc\" || field === \"customsDuties2Tva\") {\n                updated.customsDuties2Ht = updated.customsDuties2Ttc - updated.customsDuties2Tva;\n            }\n            // Calculs des totaux Customs Duties\n            updated.customsDutiesOverallTtc = updated.customsDuties1Ttc + updated.customsDuties2Ttc;\n            updated.customsDutiesOverallTva = updated.customsDuties1Tva + updated.customsDuties2Tva;\n            updated.customsDutiesOverallHt = updated.customsDuties1Ht + updated.customsDuties2Ht;\n            // Calculs automatiques Port Fees\n            if (field === \"importDeliveryTtc\" || field === \"importDeliveryTva\") {\n                updated.importDeliveryHt = updated.importDeliveryTtc - updated.importDeliveryTva;\n            }\n            if (field === \"customsInspectionTtc\" || field === \"customsInspectionTva\") {\n                updated.customsInspectionHt = updated.customsInspectionTtc - updated.customsInspectionTva;\n            }\n            // Calculs des totaux Port Fees\n            updated.portFeesOverallTtc = updated.importDeliveryTtc + updated.customsInspectionTtc;\n            updated.portFeesOverallTva = updated.importDeliveryTva + updated.customsInspectionTva;\n            updated.portFeesOverallHt = updated.importDeliveryHt + updated.customsInspectionHt;\n            // Calculs automatiques Shipping Fees\n            if (field === \"shippingAgencyTtc\" || field === \"shippingAgencyTva\") {\n                updated.shippingAgencyHt = updated.shippingAgencyTtc - updated.shippingAgencyTva;\n            }\n            if (field === \"emptyContainersTtc\" || field === \"emptyContainersTva\") {\n                updated.emptyContainersHt = updated.emptyContainersTtc - updated.emptyContainersTva;\n            }\n            // Calculs des totaux Shipping Fees\n            updated.shippingFeesOverallTtc = updated.shippingAgencyTtc + updated.emptyContainersTtc + updated.demurrageHt;\n            updated.shippingFeesOverallTva = updated.shippingAgencyTva + updated.emptyContainersTva;\n            updated.shippingFeesOverallHt = updated.shippingAgencyHt + updated.emptyContainersHt + updated.demurrageHt;\n            // Calculs automatiques Misc Expenses\n            if (field === \"miscExpensesTtc\" || field === \"miscExpensesTva\") {\n                updated.miscExpensesHt = updated.miscExpensesTtc - updated.miscExpensesTva;\n            }\n            // Calculs automatiques Transit Services\n            if (field === \"transitServicesTtc\" || field === \"transitServicesTva\") {\n                updated.transitServicesHt = updated.transitServicesTtc - updated.transitServicesTva;\n            }\n            return updated;\n        });\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                lineNumber: 391,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 390,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Shipment Reception\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Record merchandise reception from arrival notice to container release\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Reception\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-1 gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Ship, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Arrival Notice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Vessel arrival and shipping information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: 'Voyage Number - \"CALL AT PORT\" (ESCALE) N\\xb0'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.voyageNumber,\n                                                            onChange: (e)=>handleInputChange(\"voyageNumber\", e.target.value),\n                                                            placeholder: \"Enter voyage number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Bill of Lading (B/L) (Connaissement) N\\xb0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.billOfLading,\n                                                            onChange: (e)=>handleInputChange(\"billOfLading\", e.target.value),\n                                                            placeholder: \"Enter B/L number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Vessel Name (Navire)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.vesselName,\n                                                            onChange: (e)=>handleInputChange(\"vesselName\", e.target.value),\n                                                            placeholder: \"Enter vessel name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Shipowner (Armateur)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.shipowner,\n                                                            onChange: (e)=>handleInputChange(\"shipowner\", e.target.value),\n                                                            placeholder: \"Enter shipowner name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Actual Time of Arrival (Date Accostage)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            type: \"datetime-local\",\n                                                            value: form.actualTimeOfArrival,\n                                                            onChange: (e)=>handleInputChange(\"actualTimeOfArrival\", e.target.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"General Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Basic shipment and order details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: [\n                                                                    \"Exchange Rate Used : DZD/\",\n                                                                    form.currency,\n                                                                    \" *\",\n                                                                    isLoadingExchangeRate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-blue-600 ml-2\",\n                                                                        children: \"(Loading...)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"0.00000\",\n                                                                className: \"bg-blue-50\",\n                                                                disabled: isLoadingExchangeRate\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"SUPPLIER NAME *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Shipment Type *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.shipmentType,\n                                                                onChange: (e)=>handleInputChange(\"shipmentType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SEA\",\n                                                                        children: \"SEA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"AIR\",\n                                                                        children: \"AIR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EXPRESS\",\n                                                                        children: \"Express\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"ORDER NUMBER * (odoo)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DATE OF ORDER NUMBER *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.dateOfOrderNumber,\n                                                                onChange: (e)=>handleInputChange(\"dateOfOrderNumber\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"INVOICE #\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.invoiceDate,\n                                                                onChange: (e)=>handleInputChange(\"invoiceDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"FROM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.fromLocation,\n                                                                onChange: (e)=>handleInputChange(\"fromLocation\", e.target.value),\n                                                                placeholder: \"Origin location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"TO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.toLocation,\n                                                                onChange: (e)=>handleInputChange(\"toLocation\", e.target.value),\n                                                                placeholder: \"Destination location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"OPERATION TYPE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.operationType,\n                                                                onChange: (e)=>handleInputChange(\"operationType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OPEX\",\n                                                                        children: \"Operational Expenses (OpEx)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"INVESTMENT\",\n                                                                        children: \"Investment (or Equipment)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"REINVESTMENT\",\n                                                                        children: \"Reinvestment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"RAW_MATERIALS\",\n                                                                        children: \"Raw Materials and Semi-finished Products\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS\",\n                                                                        children: \"Spare Parts (for maintenance or specific resale)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"GOODS_RESALE\",\n                                                                        children: \"Goods for Resale as Is\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 637,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SERVICES\",\n                                                                        children: \"Services (intangible)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"TEMPORARY_IMPORT\",\n                                                                        children: \"Temporary Importation (or Temporary Admission)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DESCRIPTION OF THE GOODS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.descriptionOfGoods,\n                                                                onChange: (e)=>handleInputChange(\"descriptionOfGoods\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS_VEHICLES\",\n                                                                        children: \"SPARE PARTS FOR VEHICLES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LUBRICANT\",\n                                                                        children: \"LUBRICANT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ACCESSORY\",\n                                                                        children: \"ACCESSORY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 653,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"OTHER\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Banking & Payment Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Bank Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.bankName,\n                                                                        onChange: (e)=>handleInputChange(\"bankName\", e.target.value),\n                                                                        placeholder: \"Enter bank name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"L/C N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.lcNumber,\n                                                                        onChange: (e)=>handleInputChange(\"lcNumber\", e.target.value),\n                                                                        placeholder: \"Enter L/C number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.validationLC,\n                                                                        onChange: (e)=>handleInputChange(\"validationLC\", e.target.value),\n                                                                        placeholder: \"Enter validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PAYMENT TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.paymentTerm,\n                                                                        onChange: (e)=>handleInputChange(\"paymentTerm\", e.target.value),\n                                                                        placeholder: \"Enter payment term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PRICE TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.priceTerm,\n                                                                        onChange: (e)=>handleInputChange(\"priceTerm\", e.target.value),\n                                                                        placeholder: \"Enter price term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Quantities & Container Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"QUANTITY (PCS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.quantityPcs,\n                                                                        onChange: (e)=>handleInputChange(\"quantityPcs\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 20 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers20,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers20\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 40 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers40,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers40\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number PACKAGES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberPackages,\n                                                                        onChange: (e)=>handleInputChange(\"numberPackages\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: [\n                                                    \"Goods Price in Currency (\",\n                                                    form.currency,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"FOB, Freight and CIF amounts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: [\n                                                            \"Goods Price in \",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount,\n                                                                        onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight,\n                                                                        onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF (FOB + Freight)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount + form.freight,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Conversion to DZD (Automatic conversion)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 840,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 843,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: (form.fobAmount + form.freight) * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-50 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Exchange Rate Used: \",\n                                                            form.exchangeRate.toFixed(5),\n                                                            \" DZD/\",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Customs Duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Algeria customs fees and duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Customs Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"COST ALLOCATION NAME (ALGERIA CUSTOMS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 885,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.costAllocationNameCustoms,\n                                                                        onChange: (e)=>handleInputChange(\"costAllocationNameCustoms\", e.target.value),\n                                                                        placeholder: \"ALGERIA CUSTOMS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 888,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.d3Number,\n                                                                        onChange: (e)=>handleInputChange(\"d3Number\", e.target.value),\n                                                                        placeholder: \"Enter D3 number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 898,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 905,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.d3Date,\n                                                                        onChange: (e)=>handleInputChange(\"d3Date\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 908,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 934,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 933,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 946,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-1 gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Customs Duties2 DZD HT (manually entered)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.customsDuties2Ht,\n                                                                    onChange: (e)=>handleInputChange(\"customsDuties2Ht\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"bg-orange-50 border-orange-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 962,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-yellow-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 986,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 999,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1002,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD HT'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1012,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ht,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1015,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Port Fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Port and customs inspection fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (IMPORT DELIVERY)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1041,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.importDeliveryInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.importDeliveryInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1054,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1063,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1075,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1078,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1074,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1087,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1090,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (CUSTOMS INSPECTION)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.customsInspectionInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.customsInspectionInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1129,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1128,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1141,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTva,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1140,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc - form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1156,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-blue-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF PORT FEES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1173,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc + form.customsInspectionTtc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1172,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1186,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva + form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1189,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA)'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva + (form.customsInspectionTtc - form.customsInspectionTva),\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1198,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n        lineNumber: 399,\n        columnNumber: 5\n    }, this);\n}\n_s(ShipmentReception, \"VXikbBX57EuFTz5bPw9OPhzTG9I=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ShipmentReception;\nvar _c;\n$RefreshReg$(_c, \"ShipmentReception\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/shipment-reception.tsx\n"));

/***/ })

});