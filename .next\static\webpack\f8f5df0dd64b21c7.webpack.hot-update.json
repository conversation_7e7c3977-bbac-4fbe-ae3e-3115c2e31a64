{"c": ["webpack"], "r": ["pages/orders", "pages/settings", "pages/admin/users"], "m": ["./lib/xlsx-utils.ts", "./node_modules/lucide-react/dist/esm/icons/alert-circle.js", "./node_modules/lucide-react/dist/esm/icons/check-circle.js", "./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js", "./node_modules/lucide-react/dist/esm/icons/upload.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cadam2%5Cpages%5Corders.tsx&page=%2Forders!", "./pages/orders.tsx", "__barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileSpreadsheet,Package,Trash2,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/eye-off.js", "./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cadam2%5Cpages%5Csettings.tsx&page=%2Fsettings!", "./pages/settings.tsx", "__barrel_optimize__?names=DollarSign,Eye,EyeOff,RefreshCw,Save,<PERSON><PERSON><PERSON>,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/clock.js", "./node_modules/lucide-react/dist/esm/icons/user-check.js", "./node_modules/lucide-react/dist/esm/icons/users.js", "./node_modules/lucide-react/dist/esm/icons/x-circle.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cadam2%5Cpages%5Cadmin%5Cusers.tsx&page=%2Fadmin%2Fusers!", "./pages/admin/users.tsx", "__barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Trash2,<PERSON>r<PERSON><PERSON><PERSON>,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}