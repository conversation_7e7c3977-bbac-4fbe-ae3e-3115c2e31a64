"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Calculs détaillés basés sur les formules de l'application\n        const exchangeRate = 134.50000 // Taux de change USD vers DZD\n        ;\n        const fobAmountDzd = shipment.fobAmount * exchangeRate;\n        const freightDzd = shipment.fobAmount * 0.15 * exchangeRate // Estimation 15% du FOB\n        ;\n        const totalCifDzd = fobAmountDzd + freightDzd;\n        // Valeurs manuelles simulées (dans une vraie application, ces valeurs viendraient des champs de saisie)\n        // PORT FEES - Valeurs TTC et TVA saisies manuellement\n        const importDeliveryTtc = 482344 // Valeur TTC saisie manuellement\n        ;\n        const importDeliveryTva = 77175 // TVA saisie manuellement (pas calculée automatiquement)\n        ;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406 // Valeur TTC saisie manuellement\n        ;\n        const customsInspectionTva = 46305 // TVA saisie manuellement\n        ;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES - Valeurs TTC et TVA saisies manuellement\n        const shippingAgencyTtc = 675281 // Valeur TTC saisie manuellement\n        ;\n        const shippingAgencyTva = 108045 // TVA saisie manuellement\n        ;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875 // Valeur TTC saisie manuellement\n        ;\n        const emptyContainersTva = 61740 // TVA saisie manuellement\n        ;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // Valeur HT saisie manuellement (pas de TVA sur surestaries)\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES - Valeurs TTC et TVA saisies manuellement\n        const miscExpensesTtc = 482344 // Valeur TTC saisie manuellement\n        ;\n        const miscExpensesTva = 77175 // TVA saisie manuellement\n        ;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES - Valeurs TTC et TVA saisies manuellement\n        const transitExpensesTtc = 385875 // Valeur TTC saisie manuellement\n        ;\n        const transitExpensesTva = 61740 // TVA saisie manuellement\n        ;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs des totaux selon les formules demandées\n        const landedCostTtc = totalCifDzd + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const shipmentData = [\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INFORMATIONS G\\xc9N\\xc9RALES\"\n            ],\n            [\n                \"N\\xb0 Commande:\",\n                shipment.orderNumber\n            ],\n            [\n                \"Fournisseur:\",\n                shipment.supplier\n            ],\n            [\n                \"Date:\",\n                shipment.date\n            ],\n            [\n                \"Statut:\",\n                shipment.status\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"D\\xc9TAIL FINANCIER\"\n            ],\n            [\n                \"Montant FOB (USD):\",\n                shipment.fobAmount.toLocaleString(\"en-US\")\n            ],\n            [\n                \"Taux de change (USD/DZD):\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"Montant FOB (DZD):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Fret (DZD):\",\n                freightDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight):\",\n                totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CO\\xdbTS D\\xc9TAILL\\xc9S\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Livraison Import:\"\n            ],\n            [\n                \"  TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                importDeliveryHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Inspection Douani\\xe8re:\"\n            ],\n            [\n                \"  TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                customsInspectionHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'PORT FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                portFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Agence Maritime:\"\n            ],\n            [\n                \"  TTC:\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Conteneurs Vides:\"\n            ],\n            [\n                \"  TTC:\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                emptyContainersTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                emptyContainersHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Surestaries:\"\n            ],\n            [\n                \"  HT:\",\n                demurrageHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'SHIPPING COMPANY FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"  TTC:\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                miscExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                miscExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"  TTC:\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                transitExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                transitExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CALCULS FINAUX\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees TTC + Shipping Fees TTC + Misc Expenses TTC + Transit Expenses TTC\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesTtc.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees HT + Shipping Fees HT + Misc Expenses HT + Transit Expenses HT\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesHt.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Coefficient TTC:\",\n                (landedCostTtc / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Coefficient HT:\",\n                (landedCostHt / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Marge vs FOB (TTC):\",\n                \"\".concat(((landedCostTtc / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ],\n            [\n                \"Marge vs FOB (HT):\",\n                \"\".concat(((landedCostHt / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 399,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Export\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"V9rHqNTl7h2x7X2rKtHE3y76I5M=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9yZXBvcnRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ0s7QUFDTDtBQUNRO0FBQ2lEO0FBQ2pEO0FBQ0Y7QUFVeEI7QUFDa0g7QUFDM0c7QUFDTztBQUVuQyxNQUFNK0IsV0FBVztJQUNmQyxrQkFBa0I7UUFDaEI7WUFBRUMsT0FBTztZQUFPQyxXQUFXO1lBQUlDLE9BQU87UUFBUTtRQUM5QztZQUFFRixPQUFPO1lBQU9DLFdBQVc7WUFBR0MsT0FBTztRQUFPO1FBQzVDO1lBQUVGLE9BQU87WUFBT0MsV0FBVztZQUFJQyxPQUFPO1FBQVE7UUFDOUM7WUFBRUYsT0FBTztZQUFPQyxXQUFXO1lBQUlDLE9BQU87UUFBUTtRQUM5QztZQUFFRixPQUFPO1lBQU9DLFdBQVc7WUFBSUMsT0FBTztRQUFRO1FBQzlDO1lBQUVGLE9BQU87WUFBT0MsV0FBVztZQUFJQyxPQUFPO1FBQVE7S0FDL0M7SUFDREMsZUFBZTtRQUNiO1lBQUVDLE1BQU07WUFBY0YsT0FBTztZQUFJRyxRQUFRO1FBQVE7UUFDakQ7WUFBRUQsTUFBTTtZQUFrQkYsT0FBTztZQUFJRyxRQUFRO1FBQVE7UUFDckQ7WUFBRUQsTUFBTTtZQUFhRixPQUFPO1lBQUdHLFFBQVE7UUFBTztRQUM5QztZQUFFRCxNQUFNO1lBQWlCRixPQUFPO1lBQUdHLFFBQVE7UUFBTztRQUNsRDtZQUFFRCxNQUFNO1lBQWVGLE9BQU87WUFBR0csUUFBUTtRQUFPO0tBQ2pEO0lBQ0RDLGlCQUFpQjtRQUNmO1lBQ0VDLElBQUk7WUFDSkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsV0FBVztZQUNYQyxZQUFZO1lBQ1pDLGFBQWE7WUFDYkMsUUFBUTtRQUNWO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLFlBQVk7WUFDWkMsYUFBYTtZQUNiQyxRQUFRO1FBQ1Y7UUFDQTtZQUNFUCxJQUFJO1lBQ0pDLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLFFBQVE7UUFDVjtLQUNEO0FBQ0g7QUFFQSxNQUFNQyxTQUFTO0lBQUM7SUFBVztJQUFXO0lBQVc7SUFBVztDQUFVO0FBRXZELFNBQVNDOztJQUN0QixNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRUosTUFBTSxFQUFFLEdBQUc3QywyREFBVUE7SUFDNUMsTUFBTWtELFNBQVNqRCxzREFBU0E7SUFDeEIsTUFBTSxDQUFDa0QsV0FBV0MsYUFBYSxHQUFHckQsK0NBQVFBLENBQUM7UUFDekNzRCxNQUFNO1FBQ05DLElBQUk7SUFDTjtJQUVBeEQsc0RBQWUsQ0FBQztRQUNkLElBQUkrQyxXQUFXLG1CQUFtQjtZQUNoQ0ssT0FBT00sSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNYO1FBQVFLO0tBQU87SUFFbkIsTUFBTU8saUJBQWlCLENBQUNyQjtRQUN0QixPQUFPLElBQUlzQixLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNwQ0MsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLHVCQUF1QjtZQUN2QkMsdUJBQXVCO1FBQ3pCLEdBQUdDLE1BQU0sQ0FBQzVCO0lBQ1o7SUFFQSxNQUFNNkIsWUFBWSxDQUFDN0I7UUFDakIsT0FBTyxJQUFJc0IsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyx1QkFBdUI7WUFDdkJDLHVCQUF1QjtRQUN6QixHQUFHQyxNQUFNLENBQUM1QjtJQUNaO0lBRUEsTUFBTThCLHFCQUFxQixDQUFDQztRQUMxQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxHQUF3QixPQUFyQkQsWUFBVyxZQUFpRCxPQUF2QyxJQUFJRSxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFDO1lBQ2hGLElBQUlDO1lBRUosSUFBSUwsZUFBZSxpQkFBaUI7Z0JBQ2xDSyxXQUFXQztZQUNiLE9BQU8sSUFBSU4sZUFBZSxpQkFBaUI7Z0JBQ3pDSyxXQUFXRTtZQUNiLE9BQU8sSUFBSVAsZUFBZSxtQkFBbUI7Z0JBQzNDSyxXQUFXRztZQUNiLE9BQU8sSUFBSVIsV0FBV1MsVUFBVSxDQUFDLGNBQWM7Z0JBQzdDLE1BQU1yQyxjQUFjNEIsV0FBV1UsT0FBTyxDQUFDLGFBQWE7Z0JBQ3BETCxXQUFXTSx1QkFBdUJ2QztZQUNwQyxPQUFPO2dCQUNMaUMsV0FBV0M7WUFDYjtZQUVBLGlDQUFpQztZQUNqQzlDLDJDQUFjLENBQUM2QyxVQUFVSjtZQUN6QnhDLCtEQUFhLENBQUMsWUFBcUIsT0FBVHdDLFVBQVM7UUFDckMsRUFBRSxPQUFPYSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDckQsNkRBQVcsQ0FBQztRQUNkO0lBQ0Y7SUFFQSxNQUFNNkMsOEJBQThCO1FBQ2xDLE1BQU1VLEtBQUt4RCx1Q0FBVSxDQUFDMEQsUUFBUTtRQUU5QixnQkFBZ0I7UUFDaEIsTUFBTUMsY0FBYztZQUNsQjtnQkFBQzthQUFrRDtZQUNuRDtnQkFBQztnQkFBYSxHQUFzQm5DLE9BQXBCQSxVQUFVRSxJQUFJLEVBQUMsVUFBa0IsT0FBYkYsVUFBVUcsRUFBRTthQUFHO1lBQ25EO2dCQUFDO2dCQUF1QixJQUFJZSxPQUFPa0Isa0JBQWtCLENBQUM7YUFBUztZQUMvRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBa0I7WUFDbkI7Z0JBQUM7Z0JBQTBCQzthQUFlO1lBQzFDO2dCQUFDO2dCQUF3QkMsV0FBV0MsY0FBYyxDQUFDO2FBQVM7WUFDNUQ7Z0JBQUM7Z0JBQXNCQyxlQUFlQyxPQUFPLENBQUM7YUFBRztZQUNqRDtnQkFBQztnQkFBeUJDLEtBQUtDLEtBQUssQ0FBQ04saUJBQWlCO2FBQUc7WUFDekQ7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXdCO2VBQ3RCM0QsU0FBU0ssYUFBYSxDQUFDNkQsR0FBRyxDQUFDQyxDQUFBQSxPQUFRO29CQUFDQSxLQUFLN0QsSUFBSTtvQkFBRyxHQUFhLE9BQVg2RCxLQUFLL0QsS0FBSyxFQUFDO29CQUFJK0QsS0FBSzVELE1BQU0sQ0FBQ3NELGNBQWMsQ0FBQyxXQUFXO2lCQUFPO1NBQ2xIO1FBRUQsTUFBTU8sWUFBWXRFLHVDQUFVLENBQUN1RSxZQUFZLENBQUNaO1FBQzFDM0QsdUNBQVUsQ0FBQ3dFLGlCQUFpQixDQUFDaEIsSUFBSWMsV0FBVztRQUU1Qyx5QkFBeUI7UUFDekIsTUFBTUcsZ0JBQWdCO1lBQ3BCO2dCQUFDO2dCQUFlO2dCQUFlO2dCQUFRO2dCQUFxQjtnQkFBdUI7Z0JBQWU7YUFBUztlQUN4R3ZFLFNBQVNRLGVBQWUsQ0FBQzBELEdBQUcsQ0FBQ00sQ0FBQUEsSUFBSztvQkFDbkNBLEVBQUU5RCxXQUFXO29CQUNiOEQsRUFBRTdELFFBQVE7b0JBQ1Y2RCxFQUFFNUQsSUFBSTtvQkFDTjRELEVBQUUzRCxTQUFTO29CQUNYMkQsRUFBRTFELFVBQVU7b0JBQ1owRCxFQUFFekQsV0FBVyxDQUFDZ0QsT0FBTyxDQUFDO29CQUN0QlMsRUFBRXhELE1BQU07aUJBQ1Q7U0FDRjtRQUVELE1BQU15RCxjQUFjM0UsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ0U7UUFDNUN6RSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJbUIsYUFBYTtRQUU5QyxxQkFBcUI7UUFDckIsTUFBTUMsY0FBYztZQUNsQjtnQkFBQztnQkFBUTtnQkFBeUI7YUFBZTtlQUM5QzFFLFNBQVNDLGdCQUFnQixDQUFDaUUsR0FBRyxDQUFDUyxDQUFBQSxJQUFLO29CQUFDQSxFQUFFekUsS0FBSztvQkFBRXlFLEVBQUV4RSxTQUFTO29CQUFFd0UsRUFBRXZFLEtBQUs7aUJBQUM7U0FDdEU7UUFFRCxNQUFNd0UsWUFBWTlFLHVDQUFVLENBQUN1RSxZQUFZLENBQUNLO1FBQzFDNUUsdUNBQVUsQ0FBQ3dFLGlCQUFpQixDQUFDaEIsSUFBSXNCLFdBQVc7UUFFNUMsT0FBT3RCO0lBQ1Q7SUFFQSxNQUFNVCw2QkFBNkI7UUFDakMsTUFBTVMsS0FBS3hELHVDQUFVLENBQUMwRCxRQUFRO1FBRTlCLE1BQU1xQixXQUFXO1lBQ2Y7Z0JBQUM7YUFBb0Q7WUFDckQ7Z0JBQUM7Z0JBQWEsR0FBc0J2RCxPQUFwQkEsVUFBVUUsSUFBSSxFQUFDLFVBQWtCLE9BQWJGLFVBQVVHLEVBQUU7YUFBRztZQUNuRDtnQkFBQztnQkFBdUIsSUFBSWUsT0FBT2tCLGtCQUFrQixDQUFDO2FBQVM7WUFDL0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQWtDO1lBQ25DO2dCQUFDO2dCQUFhO2dCQUFlO2dCQUFpQjthQUFjO2VBQ3pEMUQsU0FBU0ssYUFBYSxDQUFDNkQsR0FBRyxDQUFDQyxDQUFBQSxPQUFRO29CQUNwQ0EsS0FBSzdELElBQUk7b0JBQ1IsR0FBYSxPQUFYNkQsS0FBSy9ELEtBQUssRUFBQztvQkFDZCtELEtBQUs1RCxNQUFNLENBQUNzRCxjQUFjLENBQUM7b0JBQzNCaUIsd0JBQXdCWCxLQUFLN0QsSUFBSTtpQkFDbEM7WUFDRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBeUI7WUFDMUI7Z0JBQUM7Z0JBQWU7Z0JBQWE7Z0JBQXVCO2dCQUFlO2FBQWU7ZUFDL0VOLFNBQVNRLGVBQWUsQ0FBQzBELEdBQUcsQ0FBQ00sQ0FBQUEsSUFBSztvQkFDbkNBLEVBQUU5RCxXQUFXO29CQUNiOEQsRUFBRTNELFNBQVMsQ0FBQ2dELGNBQWMsQ0FBQztvQkFDM0JXLEVBQUUxRCxVQUFVLENBQUMrQyxjQUFjLENBQUM7b0JBQzVCVyxFQUFFekQsV0FBVyxDQUFDZ0QsT0FBTyxDQUFDO29CQUNyQixHQUF5QyxPQUF2QyxDQUFDLENBQUNTLEVBQUV6RCxXQUFXLEdBQUcsS0FBSyxHQUFFLEVBQUdnRCxPQUFPLENBQUMsSUFBRztpQkFDM0M7U0FDRjtRQUVELE1BQU1nQixLQUFLakYsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ1E7UUFDbkMvRSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJeUIsSUFBSTtRQUVyQyxPQUFPekI7SUFDVDtJQUVBLE1BQU1SLCtCQUErQjtRQUNuQyxNQUFNUSxLQUFLeEQsdUNBQVUsQ0FBQzBELFFBQVE7UUFFOUIsTUFBTWtCLGNBQWM7WUFDbEI7Z0JBQUM7YUFBaUQ7WUFDbEQ7Z0JBQUM7Z0JBQWEsR0FBc0JwRCxPQUFwQkEsVUFBVUUsSUFBSSxFQUFDLFVBQWtCLE9BQWJGLFVBQVVHLEVBQUU7YUFBRztZQUNuRDtnQkFBQztnQkFBdUIsSUFBSWUsT0FBT2tCLGtCQUFrQixDQUFDO2FBQVM7WUFDL0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXdCO1lBQ3pCO2dCQUFDO2dCQUFRO2dCQUFlO2dCQUFnQjtnQkFBa0I7YUFBWTtlQUNuRTFELFNBQVNDLGdCQUFnQixDQUFDaUUsR0FBRyxDQUFDLENBQUNTLEdBQUdLLFFBQVU7b0JBQzdDTCxFQUFFekUsS0FBSztvQkFDUHlFLEVBQUV4RSxTQUFTO29CQUNYd0UsRUFBRXZFLEtBQUssQ0FBQ3lELGNBQWMsQ0FBQztvQkFDdkJHLEtBQUtDLEtBQUssQ0FBQ1UsRUFBRXZFLEtBQUssR0FBR3VFLEVBQUV4RSxTQUFTLEVBQUUwRCxjQUFjLENBQUM7b0JBQ2pEbUIsUUFBUSxJQUFJLEdBQXdILE9BQXJILENBQUMsQ0FBRUwsRUFBRXZFLEtBQUssR0FBR0osU0FBU0MsZ0JBQWdCLENBQUMrRSxRQUFNLEVBQUUsQ0FBQzVFLEtBQUssSUFBSUosU0FBU0MsZ0JBQWdCLENBQUMrRSxRQUFNLEVBQUUsQ0FBQzVFLEtBQUssR0FBSSxHQUFFLEVBQUcyRCxPQUFPLENBQUMsSUFBRyxPQUFLO2lCQUMxSTtZQUNEO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFTO1lBQ1Y7Z0JBQUM7Z0JBQXNCSjthQUFlO1lBQ3RDO2dCQUFDO2dCQUF1QkMsV0FBV0MsY0FBYyxDQUFDO2FBQVM7WUFDM0Q7Z0JBQUM7Z0JBQXNCRyxLQUFLQyxLQUFLLENBQUNMLGFBQWEsR0FBR0MsY0FBYyxDQUFDO2FBQVM7WUFDMUU7Z0JBQUM7Z0JBQXNCQyxlQUFlQyxPQUFPLENBQUM7YUFBRztTQUNsRDtRQUVELE1BQU1nQixLQUFLakYsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQ0s7UUFDbkM1RSx1Q0FBVSxDQUFDd0UsaUJBQWlCLENBQUNoQixJQUFJeUIsSUFBSTtRQUVyQyxPQUFPekI7SUFDVDtJQUVBLE1BQU1MLHlCQUF5QixDQUFDdkM7UUFDOUIsTUFBTXVFLFdBQVdqRixTQUFTUSxlQUFlLENBQUMwRSxJQUFJLENBQUNWLENBQUFBLElBQUtBLEVBQUU5RCxXQUFXLEtBQUtBO1FBQ3RFLElBQUksQ0FBQ3VFLFVBQVU7WUFDYixNQUFNLElBQUlFLE1BQU07UUFDbEI7UUFFQSxNQUFNN0IsS0FBS3hELHVDQUFVLENBQUMwRCxRQUFRO1FBRTlCLDREQUE0RDtRQUM1RCxNQUFNNEIsZUFBZSxVQUFVLDhCQUE4Qjs7UUFDN0QsTUFBTUMsZUFBZUosU0FBU3BFLFNBQVMsR0FBR3VFO1FBQzFDLE1BQU1FLGFBQWFMLFNBQVNwRSxTQUFTLEdBQUcsT0FBT3VFLGFBQWEsd0JBQXdCOztRQUNwRixNQUFNRyxjQUFjRixlQUFlQztRQUVuQyx3R0FBd0c7UUFDeEcsc0RBQXNEO1FBQ3RELE1BQU1FLG9CQUFvQixPQUFPLGlDQUFpQzs7UUFDbEUsTUFBTUMsb0JBQW9CLE1BQU0seURBQXlEOztRQUN6RixNQUFNQyxtQkFBbUJGLG9CQUFvQkM7UUFFN0MsTUFBTUUsdUJBQXVCLE9BQU8saUNBQWlDOztRQUNyRSxNQUFNQyx1QkFBdUIsTUFBTSwwQkFBMEI7O1FBQzdELE1BQU1DLHNCQUFzQkYsdUJBQXVCQztRQUVuRCxNQUFNRSxtQkFBbUJOLG9CQUFvQkc7UUFDN0MsTUFBTUksa0JBQWtCTCxtQkFBbUJHO1FBRTNDLGtFQUFrRTtRQUNsRSxNQUFNRyxvQkFBb0IsT0FBTyxpQ0FBaUM7O1FBQ2xFLE1BQU1DLG9CQUFvQixPQUFPLDBCQUEwQjs7UUFDM0QsTUFBTUMsbUJBQW1CRixvQkFBb0JDO1FBRTdDLE1BQU1FLHFCQUFxQixPQUFPLGlDQUFpQzs7UUFDbkUsTUFBTUMscUJBQXFCLE1BQU0sMEJBQTBCOztRQUMzRCxNQUFNQyxvQkFBb0JGLHFCQUFxQkM7UUFFL0MsTUFBTUUsY0FBYyxPQUFPLDZEQUE2RDs7UUFFeEYsTUFBTUMsdUJBQXVCUCxvQkFBb0JHLHFCQUFxQkc7UUFDdEUsTUFBTUUsc0JBQXNCTixtQkFBbUJHLG9CQUFvQkM7UUFFbkUseUVBQXlFO1FBQ3pFLE1BQU1HLGtCQUFrQixPQUFPLGlDQUFpQzs7UUFDaEUsTUFBTUMsa0JBQWtCLE1BQU0sMEJBQTBCOztRQUN4RCxNQUFNQyxpQkFBaUJGLGtCQUFrQkM7UUFFekMsc0VBQXNFO1FBQ3RFLE1BQU1FLHFCQUFxQixPQUFPLGlDQUFpQzs7UUFDbkUsTUFBTUMscUJBQXFCLE1BQU0sMEJBQTBCOztRQUMzRCxNQUFNQyxvQkFBb0JGLHFCQUFxQkM7UUFFL0Msa0RBQWtEO1FBQ2xELE1BQU1FLGdCQUFnQnhCLGNBQWNPLG1CQUFtQlMsdUJBQXVCRSxrQkFBa0JHO1FBQ2hHLE1BQU1JLGVBQWV6QixjQUFjUSxrQkFBa0JTLHNCQUFzQkcsaUJBQWlCRztRQUU1RixNQUFNRyxlQUFlO1lBQ25CO2dCQUFFLDZCQUFxQyxPQUFadkc7YUFBYztZQUN6QztnQkFBQztnQkFBdUIsSUFBSThCLE9BQU9rQixrQkFBa0IsQ0FBQzthQUFTO1lBQy9EO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUF5QjtZQUMxQjtnQkFBQztnQkFBZ0J1QixTQUFTdkUsV0FBVzthQUFDO1lBQ3RDO2dCQUFDO2dCQUFnQnVFLFNBQVN0RSxRQUFRO2FBQUM7WUFDbkM7Z0JBQUM7Z0JBQVNzRSxTQUFTckUsSUFBSTthQUFDO1lBQ3hCO2dCQUFDO2dCQUFXcUUsU0FBU2pFLE1BQU07YUFBQztZQUM1QjtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBbUI7WUFDcEI7Z0JBQUM7Z0JBQXNCaUUsU0FBU3BFLFNBQVMsQ0FBQ2dELGNBQWMsQ0FBQzthQUFTO1lBQ2xFO2dCQUFDO2dCQUE2QnVCLGFBQWFyQixPQUFPLENBQUM7YUFBRztZQUN0RDtnQkFBQztnQkFBc0JzQixhQUFheEIsY0FBYyxDQUFDO2FBQVM7WUFDNUQ7Z0JBQUM7Z0JBQWV5QixXQUFXekIsY0FBYyxDQUFDO2FBQVM7WUFDbkQ7Z0JBQUM7Z0JBQXlDMEIsWUFBWTFCLGNBQWMsQ0FBQzthQUFTO1lBQzlFO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUFrQjtZQUNuQjtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBZ0M7WUFDakM7Z0JBQUM7Z0JBQVUyQixrQkFBa0IzQixjQUFjLENBQUMsV0FBVzthQUFPO1lBQzlEO2dCQUFDO2dCQUFVNEIsa0JBQWtCNUIsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUM5RDtnQkFBQztnQkFBUzZCLGlCQUFpQjdCLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDNUQ7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQW9DO1lBQ3JDO2dCQUFDO2dCQUFVOEIscUJBQXFCOUIsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNqRTtnQkFBQztnQkFBVStCLHFCQUFxQi9CLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDakU7Z0JBQUM7Z0JBQVNnQyxvQkFBb0JoQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQy9EO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUE4QjtZQUMvQjtnQkFBQztnQkFBVWlDLGlCQUFpQmpDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDN0Q7Z0JBQUM7Z0JBQVNrQyxnQkFBZ0JsQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQzNEO2dCQUFDO2FBQUc7WUFDSjtnQkFBQzthQUEyQztZQUM1QztnQkFBQztnQkFBVW1DLGtCQUFrQm5DLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDOUQ7Z0JBQUM7Z0JBQVVvQyxrQkFBa0JwQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQzlEO2dCQUFDO2dCQUFTcUMsaUJBQWlCckMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUM1RDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBNEM7WUFDN0M7Z0JBQUM7Z0JBQVVzQyxtQkFBbUJ0QyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQy9EO2dCQUFDO2dCQUFVdUMsbUJBQW1CdkMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUMvRDtnQkFBQztnQkFBU3dDLGtCQUFrQnhDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDN0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQXVDO1lBQ3hDO2dCQUFDO2dCQUFTeUMsWUFBWXpDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDdkQ7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQTBDO1lBQzNDO2dCQUFDO2dCQUFVMEMscUJBQXFCMUMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNqRTtnQkFBQztnQkFBUzJDLG9CQUFvQjNDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDL0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQWdDO1lBQ2pDO2dCQUFDO2dCQUFVNEMsZ0JBQWdCNUMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUM1RDtnQkFBQztnQkFBVTZDLGdCQUFnQjdDLGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDNUQ7Z0JBQUM7Z0JBQVM4QyxlQUFlOUMsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUMxRDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7YUFBNkI7WUFDOUI7Z0JBQUM7Z0JBQVUrQyxtQkFBbUIvQyxjQUFjLENBQUMsV0FBVzthQUFPO1lBQy9EO2dCQUFDO2dCQUFVZ0QsbUJBQW1CaEQsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUMvRDtnQkFBQztnQkFBU2lELGtCQUFrQmpELGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDN0Q7Z0JBQUM7YUFBRztZQUNKO2dCQUFDO2FBQWlCO1lBQ2xCO2dCQUFDO2FBQUc7WUFDSjtnQkFBQztnQkFBb0JrRCxjQUFjbEQsY0FBYyxDQUFDLFdBQVc7YUFBTztZQUNwRTtnQkFBQzthQUF5RjtZQUMxRjtnQkFBQyxTQUFTMEIsWUFBWTFCLGNBQWMsQ0FBQyxXQUFXLFFBQVFpQyxpQkFBaUJqQyxjQUFjLENBQUMsV0FBVyxRQUFRMEMscUJBQXFCMUMsY0FBYyxDQUFDLFdBQVcsUUFBUTRDLGdCQUFnQjVDLGNBQWMsQ0FBQyxXQUFXLFFBQVErQyxtQkFBbUIvQyxjQUFjLENBQUM7YUFBUztZQUMvUDtnQkFBQzthQUFHO1lBQ0o7Z0JBQUM7Z0JBQW1CbUQsYUFBYW5ELGNBQWMsQ0FBQyxXQUFXO2FBQU87WUFDbEU7Z0JBQUM7YUFBcUY7WUFDdEY7Z0JBQUMsU0FBUzBCLFlBQVkxQixjQUFjLENBQUMsV0FBVyxRQUFRa0MsZ0JBQWdCbEMsY0FBYyxDQUFDLFdBQVcsUUFBUTJDLG9CQUFvQjNDLGNBQWMsQ0FBQyxXQUFXLFFBQVE4QyxlQUFlOUMsY0FBYyxDQUFDLFdBQVcsUUFBUWlELGtCQUFrQmpELGNBQWMsQ0FBQzthQUFTO1lBQzNQO2dCQUFDO2FBQUc7WUFDSjtnQkFBQztnQkFBcUJrRCxDQUFBQSxnQkFBZ0IxQixZQUFXLEVBQUd0QixPQUFPLENBQUM7YUFBRztZQUMvRDtnQkFBQztnQkFBb0JpRCxDQUFBQSxlQUFlM0IsWUFBVyxFQUFHdEIsT0FBTyxDQUFDO2FBQUc7WUFDN0Q7Z0JBQUM7Z0JBQXdCLEdBQTBELE9BQXhELENBQUMsQ0FBQyxnQkFBaUJzQixlQUFnQixLQUFLLEdBQUUsRUFBR3RCLE9BQU8sQ0FBQyxJQUFHO2FBQUc7WUFDdEY7Z0JBQUM7Z0JBQXVCLEdBQXlELE9BQXZELENBQUMsQ0FBQyxlQUFnQnNCLGVBQWdCLEtBQUssR0FBRSxFQUFHdEIsT0FBTyxDQUFDLElBQUc7YUFBRztTQUNyRjtRQUVELE1BQU1nQixLQUFLakYsdUNBQVUsQ0FBQ3VFLFlBQVksQ0FBQzRDO1FBQ25DbkgsdUNBQVUsQ0FBQ3dFLGlCQUFpQixDQUFDaEIsSUFBSXlCLElBQUksaUJBQTBCLE9BQVpyRTtRQUVuRCxPQUFPNEM7SUFDVDtJQUVBLE1BQU13QiwwQkFBMEIsQ0FBQ29DO1FBQy9CLE1BQU1DLGVBQTBDO1lBQzlDLGNBQWM7WUFDZCxrQkFBa0I7WUFDbEIsYUFBYTtZQUNiLGlCQUFpQjtZQUNqQixlQUFlO1FBQ2pCO1FBQ0EsT0FBT0EsWUFBWSxDQUFDRCxVQUFVLElBQUk7SUFDcEM7SUFFQSxJQUFJbEcsV0FBVyxXQUFXO1FBQ3hCLHFCQUNFLDhEQUFDM0MsaUVBQU1BO3NCQUNMLDRFQUFDK0k7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O0lBSXZCO0lBRUEsTUFBTTFELGlCQUFpQjNELFNBQVNDLGdCQUFnQixDQUFDcUgsTUFBTSxDQUFDLENBQUNDLEtBQUtwRCxPQUFTb0QsTUFBTXBELEtBQUtoRSxTQUFTLEVBQUU7SUFDN0YsTUFBTXlELGFBQWE1RCxTQUFTQyxnQkFBZ0IsQ0FBQ3FILE1BQU0sQ0FBQyxDQUFDQyxLQUFLcEQsT0FBU29ELE1BQU1wRCxLQUFLL0QsS0FBSyxFQUFFO0lBQ3JGLE1BQU0wRCxpQkFBaUI7SUFFdkIscUJBQ0UsOERBQUN6RixpRUFBTUE7a0JBQ0wsNEVBQUMrSTtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNJO29DQUFHSCxXQUFVOzhDQUFtQzs7Ozs7OzhDQUNqRCw4REFBQ0k7b0NBQUVKLFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7c0NBRS9CLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDekksdURBQUtBO3dDQUNKOEksTUFBSzt3Q0FDTHRILE9BQU9rQixVQUFVRSxJQUFJO3dDQUNyQm1HLFVBQVUsQ0FBQ0MsSUFBTXJHLGFBQWFzRyxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVyRyxNQUFNb0csRUFBRUUsTUFBTSxDQUFDMUgsS0FBSztnREFBQzt3Q0FDdkVpSCxXQUFVOzs7Ozs7a0RBRVosOERBQUN6SSx1REFBS0E7d0NBQ0o4SSxNQUFLO3dDQUNMdEgsT0FBT2tCLFVBQVVHLEVBQUU7d0NBQ25Ca0csVUFBVSxDQUFDQyxJQUFNckcsYUFBYXNHLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXBHLElBQUltRyxFQUFFRSxNQUFNLENBQUMxSCxLQUFLO2dEQUFDO3dDQUNyRWlILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9sQiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDL0kscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBO29DQUFDNEksV0FBVTs7c0RBQ3BCLDhEQUFDM0ksMERBQVNBOzRDQUFDMkksV0FBVTtzREFBc0I7Ozs7OztzREFDM0MsOERBQUNuSSxxSkFBT0E7NENBQUNtSSxXQUFVOzs7Ozs7Ozs7Ozs7OENBRXJCLDhEQUFDOUksNERBQVdBOztzREFDViw4REFBQzZJOzRDQUFJQyxXQUFVO3NEQUFzQjFEOzs7Ozs7c0RBQ3JDLDhEQUFDOEQ7NENBQUVKLFdBQVU7c0RBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWpELDhEQUFDL0kscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBO29DQUFDNEksV0FBVTs7c0RBQ3BCLDhEQUFDM0ksMERBQVNBOzRDQUFDMkksV0FBVTtzREFBc0I7Ozs7OztzREFDM0MsOERBQUNwSSx3SkFBVUE7NENBQUNvSSxXQUFVOzs7Ozs7Ozs7Ozs7OENBRXhCLDhEQUFDOUksNERBQVdBOztzREFDViw4REFBQzZJOzRDQUFJQyxXQUFVO3NEQUFzQnpGLGVBQWVnQzs7Ozs7O3NEQUNwRCw4REFBQzZEOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1qRCw4REFBQy9JLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTtvQ0FBQzRJLFdBQVU7O3NEQUNwQiw4REFBQzNJLDBEQUFTQTs0Q0FBQzJJLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzNDLDhEQUFDckksd0pBQVVBOzRDQUFDcUksV0FBVTs7Ozs7Ozs7Ozs7OzhDQUV4Qiw4REFBQzlJLDREQUFXQTs7c0RBQ1YsOERBQUM2STs0Q0FBSUMsV0FBVTs7Z0RBQXNCdkQsZUFBZUMsT0FBTyxDQUFDO2dEQUFHOzs7Ozs7O3NEQUMvRCw4REFBQzBEOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1qRCw4REFBQy9JLHFEQUFJQTs7OENBQ0gsOERBQUNHLDJEQUFVQTtvQ0FBQzRJLFdBQVU7O3NEQUNwQiw4REFBQzNJLDBEQUFTQTs0Q0FBQzJJLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzNDLDhEQUFDdEksc0pBQVFBOzRDQUFDc0ksV0FBVTs7Ozs7Ozs7Ozs7OzhDQUV0Qiw4REFBQzlJLDREQUFXQTs7c0RBQ1YsOERBQUM2STs0Q0FBSUMsV0FBVTtzREFBc0JyRCxLQUFLQyxLQUFLLENBQUNOLGlCQUFpQjs7Ozs7O3NEQUNqRSw4REFBQzhEOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVFuRCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDL0kscURBQUlBOzs4Q0FDSCw4REFBQ0csMkRBQVVBOztzREFDVCw4REFBQ0MsMERBQVNBOzRDQUFDMkksV0FBVTs7OERBQ25CLDhEQUFDbEksdUpBQVNBO29EQUFDa0ksV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7OztzREFHeEMsOERBQUM3SSxnRUFBZUE7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNELDREQUFXQTs4Q0FDViw0RUFBQ29CLGtLQUFtQkE7d0NBQUNvSSxPQUFNO3dDQUFPQyxRQUFRO2tEQUN4Qyw0RUFBQzNJLHVKQUFRQTs0Q0FBQzhCLE1BQU1uQixTQUFTQyxnQkFBZ0I7OzhEQUN2Qyw4REFBQ1IsNEpBQWFBO29EQUFDd0ksaUJBQWdCOzs7Ozs7OERBQy9CLDhEQUFDMUksb0pBQUtBO29EQUFDMkksU0FBUTs7Ozs7OzhEQUNmLDhEQUFDMUksb0pBQUtBOzs7Ozs4REFDTiw4REFBQ0Usc0pBQU9BO29EQUNOeUksV0FBVyxDQUFDL0gsT0FBT0UsT0FBUzs0REFDMUJBLFNBQVMsY0FBY0YsUUFBUXdCLGVBQWV4Qjs0REFDOUNFLFNBQVMsY0FBYyxjQUFjO3lEQUN0Qzs7Ozs7OzhEQUVILDhEQUFDaEIsa0pBQUdBO29EQUFDNEksU0FBUTtvREFBWUUsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPdEMsOERBQUM5SixxREFBSUE7OzhDQUNILDhEQUFDRywyREFBVUE7O3NEQUNULDhEQUFDQywwREFBU0E7NENBQUMySSxXQUFVOzs4REFDbkIsOERBQUNqSSxzSkFBUUE7b0RBQUNpSSxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7O3NEQUd2Qyw4REFBQzdJLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUluQiw4REFBQ0QsNERBQVdBOztzREFDViw4REFBQ29CLGtLQUFtQkE7NENBQUNvSSxPQUFNOzRDQUFPQyxRQUFRO3NEQUN4Qyw0RUFBQ3BJLHVKQUFnQkE7O2tFQUNmLDhEQUFDRixzSkFBT0E7d0RBQ055SSxXQUFXLENBQUMvSCxPQUFPRSxPQUFTO2dFQUN6QixHQUFRLE9BQU5GLE9BQU07Z0VBQ1RFOzZEQUNEOzs7Ozs7a0VBRUgsOERBQUNWLHVKQUFnQkE7d0RBQUN1QixNQUFNbkIsU0FBU0ssYUFBYTtrRUFDM0NMLFNBQVNLLGFBQWEsQ0FBQzZELEdBQUcsQ0FBQyxDQUFDbUUsT0FBT3JELHNCQUNsQyw4REFBQ25GLG1KQUFJQTtnRUFBdUJ1SSxNQUFNbkgsTUFBTSxDQUFDK0QsUUFBUS9ELE9BQU9xSCxNQUFNLENBQUM7K0RBQXBELFFBQWMsT0FBTnREOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSzNCLDhEQUFDb0M7NENBQUlDLFdBQVU7c0RBQ1pySCxTQUFTSyxhQUFhLENBQUM2RCxHQUFHLENBQUMsQ0FBQ0MsTUFBTWEsc0JBQ2pDLDhEQUFDb0M7b0RBQW9CQyxXQUFVOztzRUFDN0IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQ0NDLFdBQVU7b0VBQ1Z0RixPQUFPO3dFQUFFd0csaUJBQWlCdEgsTUFBTSxDQUFDK0QsUUFBUS9ELE9BQU9xSCxNQUFNLENBQUM7b0VBQUM7Ozs7Ozs4RUFFMUQsOERBQUNFOzhFQUFNckUsS0FBSzdELElBQUk7Ozs7Ozs7Ozs7OztzRUFFbEIsOERBQUNrSTs0REFBS25CLFdBQVU7O2dFQUFlbEQsS0FBSy9ELEtBQUs7Z0VBQUM7Ozs7Ozs7O21EQVJsQytELEtBQUs3RCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQWlCN0IsOERBQUNoQyxxREFBSUE7O3NDQUNILDhEQUFDRywyREFBVUE7OzhDQUNULDhEQUFDQywwREFBU0E7OENBQUM7Ozs7Ozs4Q0FDWCw4REFBQ0YsZ0VBQWVBOzhDQUFDOzs7Ozs7Ozs7Ozs7c0NBSW5CLDhEQUFDRCw0REFBV0E7c0NBQ1YsNEVBQUM2STtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMxSSx5REFBTUE7d0NBQ0w4SixTQUFRO3dDQUNScEIsV0FBVTt3Q0FDVnFCLFNBQVMsSUFBTXJHLG1CQUFtQjs7MERBRWxDLDhEQUFDeEQsc0pBQVFBO2dEQUFDd0ksV0FBVTs7Ozs7OzBEQUNwQiw4REFBQ21COzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBO2dEQUFLbkIsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHMUMsOERBQUMxSSx5REFBTUE7d0NBQ0w4SixTQUFRO3dDQUNScEIsV0FBVTt3Q0FDVnFCLFNBQVMsSUFBTXJHLG1CQUFtQjs7MERBRWxDLDhEQUFDbEQsdUpBQVNBO2dEQUFDa0ksV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ21COzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBO2dEQUFLbkIsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHMUMsOERBQUMxSSx5REFBTUE7d0NBQ0w4SixTQUFRO3dDQUNScEIsV0FBVTt3Q0FDVnFCLFNBQVMsSUFBTXJHLG1CQUFtQjs7MERBRWxDLDhEQUFDdEQsc0pBQVFBO2dEQUFDc0ksV0FBVTs7Ozs7OzBEQUNwQiw4REFBQ21COzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBO2dEQUFLbkIsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9oRCw4REFBQy9JLHFEQUFJQTs7c0NBQ0gsOERBQUNHLDJEQUFVQTs7OENBQ1QsOERBQUNDLDBEQUFTQTs4Q0FBQzs7Ozs7OzhDQUNYLDhEQUFDRixnRUFBZUE7OENBQUM7Ozs7Ozs7Ozs7OztzQ0FJbkIsOERBQUNELDREQUFXQTtzQ0FDViw0RUFBQzZJO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDc0I7b0NBQU10QixXQUFVOztzREFDZiw4REFBQ3VCO3NEQUNDLDRFQUFDQztnREFBR3hCLFdBQVU7O2tFQUNaLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUFzQjs7Ozs7O2tFQUNwQyw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBc0I7Ozs7OztrRUFDcEMsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQXNCOzs7Ozs7a0VBQ3BDLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUF1Qjs7Ozs7O2tFQUNyQyw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBdUI7Ozs7OztrRUFDckMsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQXVCOzs7Ozs7a0VBQ3JDLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUFzQjs7Ozs7O2tFQUNwQyw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUd4Qyw4REFBQzBCO3NEQUNFL0ksU0FBU1EsZUFBZSxDQUFDMEQsR0FBRyxDQUFDLENBQUNlLHlCQUM3Qiw4REFBQzREO29EQUFxQnhCLFdBQVU7O3NFQUM5Qiw4REFBQzJCOzREQUFHM0IsV0FBVTtzRUFBeUJwQyxTQUFTdkUsV0FBVzs7Ozs7O3NFQUMzRCw4REFBQ3NJOzREQUFHM0IsV0FBVTtzRUFBYXBDLFNBQVN0RSxRQUFROzs7Ozs7c0VBQzVDLDhEQUFDcUk7NERBQUczQixXQUFVO3NFQUFhLElBQUk3RSxLQUFLeUMsU0FBU3JFLElBQUksRUFBRThDLGtCQUFrQjs7Ozs7O3NFQUNyRSw4REFBQ3NGOzREQUFHM0IsV0FBVTtzRUFBd0JqRixVQUFVNkMsU0FBU3BFLFNBQVM7Ozs7OztzRUFDbEUsOERBQUNtSTs0REFBRzNCLFdBQVU7c0VBQXdCekYsZUFBZXFELFNBQVNuRSxVQUFVOzs7Ozs7c0VBQ3hFLDhEQUFDa0k7NERBQUczQixXQUFVOztnRUFBd0JwQyxTQUFTbEUsV0FBVyxDQUFDZ0QsT0FBTyxDQUFDO2dFQUFHOzs7Ozs7O3NFQUN0RSw4REFBQ2lGOzREQUFHM0IsV0FBVTtzRUFDWiw0RUFBQ21CO2dFQUFLbkIsV0FBVywyRUFJaEIsT0FIQ3BDLFNBQVNqRSxNQUFNLEtBQUssY0FDaEIsZ0NBQ0E7MEVBRUhpRSxTQUFTakUsTUFBTTs7Ozs7Ozs7Ozs7c0VBR3BCLDhEQUFDZ0k7NERBQUczQixXQUFVO3NFQUNaLDRFQUFDMUkseURBQU1BO2dFQUNMc0ssTUFBSztnRUFDTFIsU0FBUTtnRUFDUkMsU0FBUyxJQUFNckcsbUJBQW1CLFlBQWlDLE9BQXJCNEMsU0FBU3ZFLFdBQVc7O2tGQUVsRSw4REFBQzVCLHNKQUFRQTt3RUFBQ3VJLFdBQVU7Ozs7OztvRUFBaUI7Ozs7Ozs7Ozs7Ozs7bURBdEJsQ3BDLFNBQVN4RSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBb0N4QztHQXZsQndCUzs7UUFDWS9DLHVEQUFVQTtRQUM3QkMsa0RBQVNBOzs7S0FGRjhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3JlcG9ydHMudHN4P2RkYmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInXG5pbXBvcnQgTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvTGF5b3V0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQge1xuICBGaWxlVGV4dCxcbiAgRG93bmxvYWQsXG4gIENhbGVuZGFyLFxuICBUcmVuZGluZ1VwLFxuICBEb2xsYXJTaWduLFxuICBQYWNrYWdlLFxuICBCYXJDaGFydDMsXG4gIFBpZUNoYXJ0XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IEJhckNoYXJ0LCBCYXIsIFhBeGlzLCBZQXhpcywgQ2FydGVzaWFuR3JpZCwgVG9vbHRpcCwgUmVzcG9uc2l2ZUNvbnRhaW5lciwgUGllQ2hhcnQgYXMgUmVjaGFydHNQaWVDaGFydCwgQ2VsbCB9IGZyb20gJ3JlY2hhcnRzJ1xuaW1wb3J0ICogYXMgWExTWCBmcm9tICd4bHN4J1xuaW1wb3J0IHRvYXN0IGZyb20gJ3JlYWN0LWhvdC10b2FzdCdcblxuY29uc3QgbW9ja0RhdGEgPSB7XG4gIG1vbnRobHlTaGlwbWVudHM6IFtcbiAgICB7IG1vbnRoOiAnSmFuJywgc2hpcG1lbnRzOiAxMiwgdmFsdWU6IDEyMDAwMDAgfSxcbiAgICB7IG1vbnRoOiAnRmViJywgc2hpcG1lbnRzOiA4LCB2YWx1ZTogODUwMDAwIH0sXG4gICAgeyBtb250aDogJ01hcicsIHNoaXBtZW50czogMTUsIHZhbHVlOiAxNjUwMDAwIH0sXG4gICAgeyBtb250aDogJ0FwcicsIHNoaXBtZW50czogMTAsIHZhbHVlOiAxMTAwMDAwIH0sXG4gICAgeyBtb250aDogJ01heScsIHNoaXBtZW50czogMTgsIHZhbHVlOiAyMTAwMDAwIH0sXG4gICAgeyBtb250aDogJ0p1bicsIHNoaXBtZW50czogMTQsIHZhbHVlOiAxODAwMDAwIH1cbiAgXSxcbiAgY29zdEJyZWFrZG93bjogW1xuICAgIHsgbmFtZTogJ0ZPQiBBbW91bnQnLCB2YWx1ZTogNjUsIGFtb3VudDogNjUwMDAwMCB9LFxuICAgIHsgbmFtZTogJ0N1c3RvbXMgRHV0aWVzJywgdmFsdWU6IDE1LCBhbW91bnQ6IDE1MDAwMDAgfSxcbiAgICB7IG5hbWU6ICdQb3J0IEZlZXMnLCB2YWx1ZTogOCwgYW1vdW50OiA4MDAwMDAgfSxcbiAgICB7IG5hbWU6ICdTaGlwcGluZyBGZWVzJywgdmFsdWU6IDcsIGFtb3VudDogNzAwMDAwIH0sXG4gICAgeyBuYW1lOiAnT3RoZXIgQ29zdHMnLCB2YWx1ZTogNSwgYW1vdW50OiA1MDAwMDAgfVxuICBdLFxuICByZWNlbnRTaGlwbWVudHM6IFtcbiAgICB7XG4gICAgICBpZDogJzEnLFxuICAgICAgb3JkZXJOdW1iZXI6ICdPUkQtMjAyNC0wMDEnLFxuICAgICAgc3VwcGxpZXI6ICdBQkMgTW90b3JzIEx0ZCcsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0xNScsXG4gICAgICBmb2JBbW91bnQ6IDEyNTAwMCxcbiAgICAgIGxhbmRlZENvc3Q6IDE2ODc1MCxcbiAgICAgIGNvZWZmaWNpZW50OiAxLjM1LFxuICAgICAgc3RhdHVzOiAnQ29tcGxldGVkJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIG9yZGVyTnVtYmVyOiAnT1JELTIwMjQtMDAyJyxcbiAgICAgIHN1cHBsaWVyOiAnWFlaIFBhcnRzIENvJyxcbiAgICAgIGRhdGU6ICcyMDI0LTAxLTEyJyxcbiAgICAgIGZvYkFtb3VudDogODkwMDAsXG4gICAgICBsYW5kZWRDb3N0OiAxMjAxNTAsXG4gICAgICBjb2VmZmljaWVudDogMS4zNSxcbiAgICAgIHN0YXR1czogJ0NvbXBsZXRlZCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMycsXG4gICAgICBvcmRlck51bWJlcjogJ09SRC0yMDI0LTAwMycsXG4gICAgICBzdXBwbGllcjogJ0dsb2JhbCBTdXBwbHkgSW5jJyxcbiAgICAgIGRhdGU6ICcyMDI0LTAxLTEwJyxcbiAgICAgIGZvYkFtb3VudDogMTU2MDAwLFxuICAgICAgbGFuZGVkQ29zdDogMjEwNjAwLFxuICAgICAgY29lZmZpY2llbnQ6IDEuMzUsXG4gICAgICBzdGF0dXM6ICdJbiBQcm9ncmVzcydcbiAgICB9XG4gIF1cbn1cblxuY29uc3QgQ09MT1JTID0gWycjM2I4MmY2JywgJyNlZjQ0NDQnLCAnI2Y1OWUwYicsICcjMTBiOTgxJywgJyM4YjVjZjYnXVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSZXBvcnRzKCkge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtkYXRlUmFuZ2UsIHNldERhdGVSYW5nZV0gPSB1c2VTdGF0ZSh7XG4gICAgZnJvbTogJzIwMjQtMDEtMDEnLFxuICAgIHRvOiAnMjAyNC0wNi0zMCdcbiAgfSlcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2F1dGgvc2lnbmluJylcbiAgICB9XG4gIH0sIFtzdGF0dXMsIHJvdXRlcl0pXG5cbiAgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoYW1vdW50OiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdmci1GUicsIHtcbiAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgY3VycmVuY3k6ICdEWkQnLFxuICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAwLFxuICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAwXG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdFVTRCA9IChhbW91bnQ6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJywge1xuICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDAsXG4gICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDBcbiAgICB9KS5mb3JtYXQoYW1vdW50KVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRXhwb3J0UmVwb3J0ID0gKHJlcG9ydFR5cGU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBmaWxlTmFtZSA9IGAke3JlcG9ydFR5cGV9X1JlcG9ydF8ke25ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfS54bHN4YFxuICAgICAgbGV0IHdvcmtib29rOiBYTFNYLldvcmtCb29rXG5cbiAgICAgIGlmIChyZXBvcnRUeXBlID09PSAnQ29tcHJlaGVuc2l2ZScpIHtcbiAgICAgICAgd29ya2Jvb2sgPSBnZW5lcmF0ZUNvbXByZWhlbnNpdmVSZXBvcnQoKVxuICAgICAgfSBlbHNlIGlmIChyZXBvcnRUeXBlID09PSAnQ29zdF9BbmFseXNpcycpIHtcbiAgICAgICAgd29ya2Jvb2sgPSBnZW5lcmF0ZUNvc3RBbmFseXNpc1JlcG9ydCgpXG4gICAgICB9IGVsc2UgaWYgKHJlcG9ydFR5cGUgPT09ICdNb250aGx5X1N1bW1hcnknKSB7XG4gICAgICAgIHdvcmtib29rID0gZ2VuZXJhdGVNb250aGx5U3VtbWFyeVJlcG9ydCgpXG4gICAgICB9IGVsc2UgaWYgKHJlcG9ydFR5cGUuc3RhcnRzV2l0aCgnU2hpcG1lbnRfJykpIHtcbiAgICAgICAgY29uc3Qgb3JkZXJOdW1iZXIgPSByZXBvcnRUeXBlLnJlcGxhY2UoJ1NoaXBtZW50XycsICcnKVxuICAgICAgICB3b3JrYm9vayA9IGdlbmVyYXRlU2hpcG1lbnRSZXBvcnQob3JkZXJOdW1iZXIpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB3b3JrYm9vayA9IGdlbmVyYXRlQ29tcHJlaGVuc2l2ZVJlcG9ydCgpXG4gICAgICB9XG5cbiAgICAgIC8vIEdlbmVyYXRlIGFuZCBkb3dubG9hZCB0aGUgZmlsZVxuICAgICAgWExTWC53cml0ZUZpbGUod29ya2Jvb2ssIGZpbGVOYW1lKVxuICAgICAgdG9hc3Quc3VjY2VzcyhgUmFwcG9ydCBcIiR7ZmlsZU5hbWV9XCIgZ8OpbsOpcsOpIGV0IHTDqWzDqWNoYXJnw6kgYXZlYyBzdWNjw6hzIWApXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgcmVwb3J0OicsIGVycm9yKVxuICAgICAgdG9hc3QuZXJyb3IoJ0VycmV1ciBsb3JzIGRlIGxhIGfDqW7DqXJhdGlvbiBkdSByYXBwb3J0JylcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZW5lcmF0ZUNvbXByZWhlbnNpdmVSZXBvcnQgPSAoKTogWExTWC5Xb3JrQm9vayA9PiB7XG4gICAgY29uc3Qgd2IgPSBYTFNYLnV0aWxzLmJvb2tfbmV3KClcblxuICAgIC8vIFN1bW1hcnkgc2hlZXRcbiAgICBjb25zdCBzdW1tYXJ5RGF0YSA9IFtcbiAgICAgIFsnUkFQUE9SVCBDT01QTEVUIC0gSU1QT1JUICYgTE9HSVNUSUNTIENBTENVTEFUT1InXSxcbiAgICAgIFsnUMOpcmlvZGU6JywgYCR7ZGF0ZVJhbmdlLmZyb219IMOgICR7ZGF0ZVJhbmdlLnRvfWBdLFxuICAgICAgWydEYXRlIGRlIGfDqW7DqXJhdGlvbjonLCBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydSw4lTVU3DiSBFWMOJQ1VUSUYnXSxcbiAgICAgIFsnVG90YWwgZGVzIGV4cMOpZGl0aW9uczonLCB0b3RhbFNoaXBtZW50c10sXG4gICAgICBbJ1ZhbGV1ciB0b3RhbGUgKERaRCk6JywgdG90YWxWYWx1ZS50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJ0NvZWZmaWNpZW50IG1veWVuOicsIGF2Z0NvZWZmaWNpZW50LnRvRml4ZWQoMyldLFxuICAgICAgWydFeHDDqWRpdGlvbnMgcGFyIG1vaXM6JywgTWF0aC5yb3VuZCh0b3RhbFNoaXBtZW50cyAvIDYpXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1LDiVBBUlRJVElPTiBERVMgQ0/Dm1RTJ10sXG4gICAgICAuLi5tb2NrRGF0YS5jb3N0QnJlYWtkb3duLm1hcChpdGVtID0+IFtpdGVtLm5hbWUsIGAke2l0ZW0udmFsdWV9JWAsIGl0ZW0uYW1vdW50LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSlcbiAgICBdXG5cbiAgICBjb25zdCBzdW1tYXJ5V3MgPSBYTFNYLnV0aWxzLmFvYV90b19zaGVldChzdW1tYXJ5RGF0YSlcbiAgICBYTFNYLnV0aWxzLmJvb2tfYXBwZW5kX3NoZWV0KHdiLCBzdW1tYXJ5V3MsICdSw6lzdW3DqScpXG5cbiAgICAvLyBTaGlwbWVudHMgZGV0YWlsIHNoZWV0XG4gICAgY29uc3Qgc2hpcG1lbnRzRGF0YSA9IFtcbiAgICAgIFsnTsKwIENvbW1hbmRlJywgJ0ZvdXJuaXNzZXVyJywgJ0RhdGUnLCAnTW9udGFudCBGT0IgKFVTRCknLCAnQ2/Du3QgRMOpYmFycXXDqSAoRFpEKScsICdDb2VmZmljaWVudCcsICdTdGF0dXQnXSxcbiAgICAgIC4uLm1vY2tEYXRhLnJlY2VudFNoaXBtZW50cy5tYXAocyA9PiBbXG4gICAgICAgIHMub3JkZXJOdW1iZXIsXG4gICAgICAgIHMuc3VwcGxpZXIsXG4gICAgICAgIHMuZGF0ZSxcbiAgICAgICAgcy5mb2JBbW91bnQsXG4gICAgICAgIHMubGFuZGVkQ29zdCxcbiAgICAgICAgcy5jb2VmZmljaWVudC50b0ZpeGVkKDMpLFxuICAgICAgICBzLnN0YXR1c1xuICAgICAgXSlcbiAgICBdXG5cbiAgICBjb25zdCBzaGlwbWVudHNXcyA9IFhMU1gudXRpbHMuYW9hX3RvX3NoZWV0KHNoaXBtZW50c0RhdGEpXG4gICAgWExTWC51dGlscy5ib29rX2FwcGVuZF9zaGVldCh3Yiwgc2hpcG1lbnRzV3MsICdEw6l0YWlsIEV4cMOpZGl0aW9ucycpXG5cbiAgICAvLyBNb250aGx5IGRhdGEgc2hlZXRcbiAgICBjb25zdCBtb250aGx5RGF0YSA9IFtcbiAgICAgIFsnTW9pcycsICdOb21icmUgZFxcJ2V4cMOpZGl0aW9ucycsICdWYWxldXIgKERaRCknXSxcbiAgICAgIC4uLm1vY2tEYXRhLm1vbnRobHlTaGlwbWVudHMubWFwKG0gPT4gW20ubW9udGgsIG0uc2hpcG1lbnRzLCBtLnZhbHVlXSlcbiAgICBdXG5cbiAgICBjb25zdCBtb250aGx5V3MgPSBYTFNYLnV0aWxzLmFvYV90b19zaGVldChtb250aGx5RGF0YSlcbiAgICBYTFNYLnV0aWxzLmJvb2tfYXBwZW5kX3NoZWV0KHdiLCBtb250aGx5V3MsICdEb25uw6llcyBNZW5zdWVsbGVzJylcblxuICAgIHJldHVybiB3YlxuICB9XG5cbiAgY29uc3QgZ2VuZXJhdGVDb3N0QW5hbHlzaXNSZXBvcnQgPSAoKTogWExTWC5Xb3JrQm9vayA9PiB7XG4gICAgY29uc3Qgd2IgPSBYTFNYLnV0aWxzLmJvb2tfbmV3KClcblxuICAgIGNvbnN0IGNvc3REYXRhID0gW1xuICAgICAgWydBTkFMWVNFIERFUyBDT8ObVFMgLSBJTVBPUlQgJiBMT0dJU1RJQ1MgQ0FMQ1VMQVRPUiddLFxuICAgICAgWydQw6lyaW9kZTonLCBgJHtkYXRlUmFuZ2UuZnJvbX0gw6AgJHtkYXRlUmFuZ2UudG99YF0sXG4gICAgICBbJ0RhdGUgZGUgZ8OpbsOpcmF0aW9uOicsIG5ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1LDiVBBUlRJVElPTiBEw4lUQUlMTMOJRSBERVMgQ0/Dm1RTJ10sXG4gICAgICBbJ0NvbXBvc2FudCcsICdQb3VyY2VudGFnZScsICdNb250YW50IChEWkQpJywgJ0Rlc2NyaXB0aW9uJ10sXG4gICAgICAuLi5tb2NrRGF0YS5jb3N0QnJlYWtkb3duLm1hcChpdGVtID0+IFtcbiAgICAgICAgaXRlbS5uYW1lLFxuICAgICAgICBgJHtpdGVtLnZhbHVlfSVgLFxuICAgICAgICBpdGVtLmFtb3VudC50b0xvY2FsZVN0cmluZygnZnItRlInKSxcbiAgICAgICAgZ2V0Q29tcG9uZW50RGVzY3JpcHRpb24oaXRlbS5uYW1lKVxuICAgICAgXSksXG4gICAgICBbJyddLFxuICAgICAgWydBTkFMWVNFIFBBUiBFWFDDiURJVElPTiddLFxuICAgICAgWydOwrAgQ29tbWFuZGUnLCAnRk9CIChVU0QpJywgJ0Nvw7t0IETDqWJhcnF1w6kgKERaRCknLCAnQ29lZmZpY2llbnQnLCAnTWFyZ2UgdnMgRk9CJ10sXG4gICAgICAuLi5tb2NrRGF0YS5yZWNlbnRTaGlwbWVudHMubWFwKHMgPT4gW1xuICAgICAgICBzLm9yZGVyTnVtYmVyLFxuICAgICAgICBzLmZvYkFtb3VudC50b0xvY2FsZVN0cmluZygnZW4tVVMnKSxcbiAgICAgICAgcy5sYW5kZWRDb3N0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpLFxuICAgICAgICBzLmNvZWZmaWNpZW50LnRvRml4ZWQoMyksXG4gICAgICAgIGAkeygocy5jb2VmZmljaWVudCAtIDEpICogMTAwKS50b0ZpeGVkKDEpfSVgXG4gICAgICBdKVxuICAgIF1cblxuICAgIGNvbnN0IHdzID0gWExTWC51dGlscy5hb2FfdG9fc2hlZXQoY29zdERhdGEpXG4gICAgWExTWC51dGlscy5ib29rX2FwcGVuZF9zaGVldCh3Yiwgd3MsICdBbmFseXNlIENvw7t0cycpXG5cbiAgICByZXR1cm4gd2JcbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlTW9udGhseVN1bW1hcnlSZXBvcnQgPSAoKTogWExTWC5Xb3JrQm9vayA9PiB7XG4gICAgY29uc3Qgd2IgPSBYTFNYLnV0aWxzLmJvb2tfbmV3KClcblxuICAgIGNvbnN0IG1vbnRobHlEYXRhID0gW1xuICAgICAgWydSw4lTVU3DiSBNRU5TVUVMIC0gSU1QT1JUICYgTE9HSVNUSUNTIENBTENVTEFUT1InXSxcbiAgICAgIFsnUMOpcmlvZGU6JywgYCR7ZGF0ZVJhbmdlLmZyb219IMOgICR7ZGF0ZVJhbmdlLnRvfWBdLFxuICAgICAgWydEYXRlIGRlIGfDqW7DqXJhdGlvbjonLCBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydQRVJGT1JNQU5DRSBNRU5TVUVMTEUnXSxcbiAgICAgIFsnTW9pcycsICdFeHDDqWRpdGlvbnMnLCAnVmFsZXVyIChEWkQpJywgJ1ZhbGV1ciBNb3llbm5lJywgJ8OJdm9sdXRpb24nXSxcbiAgICAgIC4uLm1vY2tEYXRhLm1vbnRobHlTaGlwbWVudHMubWFwKChtLCBpbmRleCkgPT4gW1xuICAgICAgICBtLm1vbnRoLFxuICAgICAgICBtLnNoaXBtZW50cyxcbiAgICAgICAgbS52YWx1ZS50b0xvY2FsZVN0cmluZygnZnItRlInKSxcbiAgICAgICAgTWF0aC5yb3VuZChtLnZhbHVlIC8gbS5zaGlwbWVudHMpLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpLFxuICAgICAgICBpbmRleCA+IDAgPyBgJHsoKChtLnZhbHVlIC0gbW9ja0RhdGEubW9udGhseVNoaXBtZW50c1tpbmRleC0xXS52YWx1ZSkgLyBtb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzW2luZGV4LTFdLnZhbHVlKSAqIDEwMCkudG9GaXhlZCgxKX0lYCA6ICdOL0EnXG4gICAgICBdKSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1RPVEFVWCddLFxuICAgICAgWydUb3RhbCBleHDDqWRpdGlvbnM6JywgdG90YWxTaGlwbWVudHNdLFxuICAgICAgWydUb3RhbCB2YWxldXIgKERaRCk6JywgdG90YWxWYWx1ZS50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJ01veWVubmUgbWVuc3VlbGxlOicsIE1hdGgucm91bmQodG90YWxWYWx1ZSAvIDYpLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnQ29lZmZpY2llbnQgbW95ZW46JywgYXZnQ29lZmZpY2llbnQudG9GaXhlZCgzKV1cbiAgICBdXG5cbiAgICBjb25zdCB3cyA9IFhMU1gudXRpbHMuYW9hX3RvX3NoZWV0KG1vbnRobHlEYXRhKVxuICAgIFhMU1gudXRpbHMuYm9va19hcHBlbmRfc2hlZXQod2IsIHdzLCAnUsOpc3Vtw6kgTWVuc3VlbCcpXG5cbiAgICByZXR1cm4gd2JcbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlU2hpcG1lbnRSZXBvcnQgPSAob3JkZXJOdW1iZXI6IHN0cmluZyk6IFhMU1guV29ya0Jvb2sgPT4ge1xuICAgIGNvbnN0IHNoaXBtZW50ID0gbW9ja0RhdGEucmVjZW50U2hpcG1lbnRzLmZpbmQocyA9PiBzLm9yZGVyTnVtYmVyID09PSBvcmRlck51bWJlcilcbiAgICBpZiAoIXNoaXBtZW50KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cMOpZGl0aW9uIG5vbiB0cm91dsOpZScpXG4gICAgfVxuXG4gICAgY29uc3Qgd2IgPSBYTFNYLnV0aWxzLmJvb2tfbmV3KClcblxuICAgIC8vIENhbGN1bHMgZMOpdGFpbGzDqXMgYmFzw6lzIHN1ciBsZXMgZm9ybXVsZXMgZGUgbCdhcHBsaWNhdGlvblxuICAgIGNvbnN0IGV4Y2hhbmdlUmF0ZSA9IDEzNC41MDAwMCAvLyBUYXV4IGRlIGNoYW5nZSBVU0QgdmVycyBEWkRcbiAgICBjb25zdCBmb2JBbW91bnREemQgPSBzaGlwbWVudC5mb2JBbW91bnQgKiBleGNoYW5nZVJhdGVcbiAgICBjb25zdCBmcmVpZ2h0RHpkID0gc2hpcG1lbnQuZm9iQW1vdW50ICogMC4xNSAqIGV4Y2hhbmdlUmF0ZSAvLyBFc3RpbWF0aW9uIDE1JSBkdSBGT0JcbiAgICBjb25zdCB0b3RhbENpZkR6ZCA9IGZvYkFtb3VudER6ZCArIGZyZWlnaHREemRcblxuICAgIC8vIFZhbGV1cnMgbWFudWVsbGVzIHNpbXVsw6llcyAoZGFucyB1bmUgdnJhaWUgYXBwbGljYXRpb24sIGNlcyB2YWxldXJzIHZpZW5kcmFpZW50IGRlcyBjaGFtcHMgZGUgc2Fpc2llKVxuICAgIC8vIFBPUlQgRkVFUyAtIFZhbGV1cnMgVFRDIGV0IFRWQSBzYWlzaWVzIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IGltcG9ydERlbGl2ZXJ5VHRjID0gNDgyMzQ0IC8vIFZhbGV1ciBUVEMgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IGltcG9ydERlbGl2ZXJ5VHZhID0gNzcxNzUgLy8gVFZBIHNhaXNpZSBtYW51ZWxsZW1lbnQgKHBhcyBjYWxjdWzDqWUgYXV0b21hdGlxdWVtZW50KVxuICAgIGNvbnN0IGltcG9ydERlbGl2ZXJ5SHQgPSBpbXBvcnREZWxpdmVyeVR0YyAtIGltcG9ydERlbGl2ZXJ5VHZhXG5cbiAgICBjb25zdCBjdXN0b21zSW5zcGVjdGlvblR0YyA9IDI4OTQwNiAvLyBWYWxldXIgVFRDIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBjdXN0b21zSW5zcGVjdGlvblR2YSA9IDQ2MzA1IC8vIFRWQSBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgY3VzdG9tc0luc3BlY3Rpb25IdCA9IGN1c3RvbXNJbnNwZWN0aW9uVHRjIC0gY3VzdG9tc0luc3BlY3Rpb25UdmFcblxuICAgIGNvbnN0IHBvcnRGZWVzVG90YWxUdGMgPSBpbXBvcnREZWxpdmVyeVR0YyArIGN1c3RvbXNJbnNwZWN0aW9uVHRjXG4gICAgY29uc3QgcG9ydEZlZXNUb3RhbEh0ID0gaW1wb3J0RGVsaXZlcnlIdCArIGN1c3RvbXNJbnNwZWN0aW9uSHRcblxuICAgIC8vIFNISVBQSU5HIENPTVBBTlkgRkVFUyAtIFZhbGV1cnMgVFRDIGV0IFRWQSBzYWlzaWVzIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IHNoaXBwaW5nQWdlbmN5VHRjID0gNjc1MjgxIC8vIFZhbGV1ciBUVEMgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IHNoaXBwaW5nQWdlbmN5VHZhID0gMTA4MDQ1IC8vIFRWQSBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3Qgc2hpcHBpbmdBZ2VuY3lIdCA9IHNoaXBwaW5nQWdlbmN5VHRjIC0gc2hpcHBpbmdBZ2VuY3lUdmFcblxuICAgIGNvbnN0IGVtcHR5Q29udGFpbmVyc1R0YyA9IDM4NTg3NSAvLyBWYWxldXIgVFRDIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBlbXB0eUNvbnRhaW5lcnNUdmEgPSA2MTc0MCAvLyBUVkEgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IGVtcHR5Q29udGFpbmVyc0h0ID0gZW1wdHlDb250YWluZXJzVHRjIC0gZW1wdHlDb250YWluZXJzVHZhXG5cbiAgICBjb25zdCBkZW11cnJhZ2VIdCA9IDE5MjkzOCAvLyBWYWxldXIgSFQgc2Fpc2llIG1hbnVlbGxlbWVudCAocGFzIGRlIFRWQSBzdXIgc3VyZXN0YXJpZXMpXG5cbiAgICBjb25zdCBzaGlwcGluZ0ZlZXNUb3RhbFR0YyA9IHNoaXBwaW5nQWdlbmN5VHRjICsgZW1wdHlDb250YWluZXJzVHRjICsgZGVtdXJyYWdlSHRcbiAgICBjb25zdCBzaGlwcGluZ0ZlZXNUb3RhbEh0ID0gc2hpcHBpbmdBZ2VuY3lIdCArIGVtcHR5Q29udGFpbmVyc0h0ICsgZGVtdXJyYWdlSHRcblxuICAgIC8vIE9USEVSIE1JU0NFTExBTkVPVVMgRVhQRU5TRVMgLSBWYWxldXJzIFRUQyBldCBUVkEgc2Fpc2llcyBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBtaXNjRXhwZW5zZXNUdGMgPSA0ODIzNDQgLy8gVmFsZXVyIFRUQyBzYWlzaWUgbWFudWVsbGVtZW50XG4gICAgY29uc3QgbWlzY0V4cGVuc2VzVHZhID0gNzcxNzUgLy8gVFZBIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCBtaXNjRXhwZW5zZXNIdCA9IG1pc2NFeHBlbnNlc1R0YyAtIG1pc2NFeHBlbnNlc1R2YVxuXG4gICAgLy8gVFJBTlNJVCBTRVJWSUNFUyBFWFBFTlNFUyAtIFZhbGV1cnMgVFRDIGV0IFRWQSBzYWlzaWVzIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IHRyYW5zaXRFeHBlbnNlc1R0YyA9IDM4NTg3NSAvLyBWYWxldXIgVFRDIHNhaXNpZSBtYW51ZWxsZW1lbnRcbiAgICBjb25zdCB0cmFuc2l0RXhwZW5zZXNUdmEgPSA2MTc0MCAvLyBUVkEgc2Fpc2llIG1hbnVlbGxlbWVudFxuICAgIGNvbnN0IHRyYW5zaXRFeHBlbnNlc0h0ID0gdHJhbnNpdEV4cGVuc2VzVHRjIC0gdHJhbnNpdEV4cGVuc2VzVHZhXG5cbiAgICAvLyBDYWxjdWxzIGRlcyB0b3RhdXggc2Vsb24gbGVzIGZvcm11bGVzIGRlbWFuZMOpZXNcbiAgICBjb25zdCBsYW5kZWRDb3N0VHRjID0gdG90YWxDaWZEemQgKyBwb3J0RmVlc1RvdGFsVHRjICsgc2hpcHBpbmdGZWVzVG90YWxUdGMgKyBtaXNjRXhwZW5zZXNUdGMgKyB0cmFuc2l0RXhwZW5zZXNUdGNcbiAgICBjb25zdCBsYW5kZWRDb3N0SHQgPSB0b3RhbENpZkR6ZCArIHBvcnRGZWVzVG90YWxIdCArIHNoaXBwaW5nRmVlc1RvdGFsSHQgKyBtaXNjRXhwZW5zZXNIdCArIHRyYW5zaXRFeHBlbnNlc0h0XG5cbiAgICBjb25zdCBzaGlwbWVudERhdGEgPSBbXG4gICAgICBbYFJBUFBPUlQgRCdFWFDDiURJVElPTiAtICR7b3JkZXJOdW1iZXJ9YF0sXG4gICAgICBbJ0RhdGUgZGUgZ8OpbsOpcmF0aW9uOicsIG5ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ0lORk9STUFUSU9OUyBHw4lOw4lSQUxFUyddLFxuICAgICAgWydOwrAgQ29tbWFuZGU6Jywgc2hpcG1lbnQub3JkZXJOdW1iZXJdLFxuICAgICAgWydGb3Vybmlzc2V1cjonLCBzaGlwbWVudC5zdXBwbGllcl0sXG4gICAgICBbJ0RhdGU6Jywgc2hpcG1lbnQuZGF0ZV0sXG4gICAgICBbJ1N0YXR1dDonLCBzaGlwbWVudC5zdGF0dXNdLFxuICAgICAgWycnXSxcbiAgICAgIFsnRMOJVEFJTCBGSU5BTkNJRVInXSxcbiAgICAgIFsnTW9udGFudCBGT0IgKFVTRCk6Jywgc2hpcG1lbnQuZm9iQW1vdW50LnRvTG9jYWxlU3RyaW5nKCdlbi1VUycpXSxcbiAgICAgIFsnVGF1eCBkZSBjaGFuZ2UgKFVTRC9EWkQpOicsIGV4Y2hhbmdlUmF0ZS50b0ZpeGVkKDUpXSxcbiAgICAgIFsnTW9udGFudCBGT0IgKERaRCk6JywgZm9iQW1vdW50RHpkLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnRnJldCAoRFpEKTonLCBmcmVpZ2h0RHpkLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnVE9UQUwgQU1PVU5UIENJRiBEWkQgKEZPQiArIEZyZWlnaHQpOicsIHRvdGFsQ2lmRHpkLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ0NPw5tUUyBEw4lUQUlMTMOJUyddLFxuICAgICAgWycnXSxcbiAgICAgIFsnUE9SVCBGRUVTIC0gTGl2cmFpc29uIEltcG9ydDonXSxcbiAgICAgIFsnICBUVEM6JywgaW1wb3J0RGVsaXZlcnlUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIFRWQTonLCBpbXBvcnREZWxpdmVyeVR2YS50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgSFQ6JywgaW1wb3J0RGVsaXZlcnlIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyddLFxuICAgICAgWydQT1JUIEZFRVMgLSBJbnNwZWN0aW9uIERvdWFuacOocmU6J10sXG4gICAgICBbJyAgVFRDOicsIGN1c3RvbXNJbnNwZWN0aW9uVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBUVkE6JywgY3VzdG9tc0luc3BlY3Rpb25UdmEudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUOicsIGN1c3RvbXNJbnNwZWN0aW9uSHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycnXSxcbiAgICAgIFsnUE9SVCBGRUVTIFwiT3ZlcmFsbCBUb3RhbHNcIjonXSxcbiAgICAgIFsnICBUVEM6JywgcG9ydEZlZXNUb3RhbFR0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgSFQ6JywgcG9ydEZlZXNUb3RhbEh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyAtIEFnZW5jZSBNYXJpdGltZTonXSxcbiAgICAgIFsnICBUVEM6Jywgc2hpcHBpbmdBZ2VuY3lUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIFRWQTonLCBzaGlwcGluZ0FnZW5jeVR2YS50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgSFQ6Jywgc2hpcHBpbmdBZ2VuY3lIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyddLFxuICAgICAgWydTSElQUElORyBDT01QQU5ZIEZFRVMgLSBDb250ZW5ldXJzIFZpZGVzOiddLFxuICAgICAgWycgIFRUQzonLCBlbXB0eUNvbnRhaW5lcnNUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIFRWQTonLCBlbXB0eUNvbnRhaW5lcnNUdmEudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgIEhUOicsIGVtcHR5Q29udGFpbmVyc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyAtIFN1cmVzdGFyaWVzOiddLFxuICAgICAgWycgIEhUOicsIGRlbXVycmFnZUh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ1NISVBQSU5HIENPTVBBTlkgRkVFUyBcIk92ZXJhbGwgVG90YWxzXCI6J10sXG4gICAgICBbJyAgVFRDOicsIHNoaXBwaW5nRmVlc1RvdGFsVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBIVDonLCBzaGlwcGluZ0ZlZXNUb3RhbEh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnJ10sXG4gICAgICBbJ09USEVSIE1JU0NFTExBTkVPVVMgRVhQRU5TRVM6J10sXG4gICAgICBbJyAgVFRDOicsIG1pc2NFeHBlbnNlc1R0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgVFZBOicsIG1pc2NFeHBlbnNlc1R2YS50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyAgSFQ6JywgbWlzY0V4cGVuc2VzSHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycnXSxcbiAgICAgIFsnVFJBTlNJVCBTRVJWSUNFUyBFWFBFTlNFUzonXSxcbiAgICAgIFsnICBUVEM6JywgdHJhbnNpdEV4cGVuc2VzVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBUVkE6JywgdHJhbnNpdEV4cGVuc2VzVHZhLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICBIVDonLCB0cmFuc2l0RXhwZW5zZXNIdC50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgRFpEJ10sXG4gICAgICBbJyddLFxuICAgICAgWydDQUxDVUxTIEZJTkFVWCddLFxuICAgICAgWycnXSxcbiAgICAgIFsnTGFuZGVkIENvc3QgVFRDOicsIGxhbmRlZENvc3RUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnIERaRCddLFxuICAgICAgWycgID0gQ0lGICsgUG9ydCBGZWVzIFRUQyArIFNoaXBwaW5nIEZlZXMgVFRDICsgTWlzYyBFeHBlbnNlcyBUVEMgKyBUcmFuc2l0IEV4cGVuc2VzIFRUQyddLFxuICAgICAgWycgID0gJyArIHRvdGFsQ2lmRHpkLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyBwb3J0RmVlc1RvdGFsVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyBzaGlwcGluZ0ZlZXNUb3RhbFR0Yy50b0xvY2FsZVN0cmluZygnZnItRlInKSArICcgKyAnICsgbWlzY0V4cGVuc2VzVHRjLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyB0cmFuc2l0RXhwZW5zZXNUdGMudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJyldLFxuICAgICAgWycnXSxcbiAgICAgIFsnTGFuZGVkIENvc3QgSFQ6JywgbGFuZGVkQ29zdEh0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyBEWkQnXSxcbiAgICAgIFsnICA9IENJRiArIFBvcnQgRmVlcyBIVCArIFNoaXBwaW5nIEZlZXMgSFQgKyBNaXNjIEV4cGVuc2VzIEhUICsgVHJhbnNpdCBFeHBlbnNlcyBIVCddLFxuICAgICAgWycgID0gJyArIHRvdGFsQ2lmRHpkLnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyBwb3J0RmVlc1RvdGFsSHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIHNoaXBwaW5nRmVlc1RvdGFsSHQudG9Mb2NhbGVTdHJpbmcoJ2ZyLUZSJykgKyAnICsgJyArIG1pc2NFeHBlbnNlc0h0LnRvTG9jYWxlU3RyaW5nKCdmci1GUicpICsgJyArICcgKyB0cmFuc2l0RXhwZW5zZXNIdC50b0xvY2FsZVN0cmluZygnZnItRlInKV0sXG4gICAgICBbJyddLFxuICAgICAgWydDb2VmZmljaWVudCBUVEM6JywgKGxhbmRlZENvc3RUdGMgLyBmb2JBbW91bnREemQpLnRvRml4ZWQoMyldLFxuICAgICAgWydDb2VmZmljaWVudCBIVDonLCAobGFuZGVkQ29zdEh0IC8gZm9iQW1vdW50RHpkKS50b0ZpeGVkKDMpXSxcbiAgICAgIFsnTWFyZ2UgdnMgRk9CIChUVEMpOicsIGAkeygoKGxhbmRlZENvc3RUdGMgLyBmb2JBbW91bnREemQpIC0gMSkgKiAxMDApLnRvRml4ZWQoMSl9JWBdLFxuICAgICAgWydNYXJnZSB2cyBGT0IgKEhUKTonLCBgJHsoKChsYW5kZWRDb3N0SHQgLyBmb2JBbW91bnREemQpIC0gMSkgKiAxMDApLnRvRml4ZWQoMSl9JWBdXG4gICAgXVxuXG4gICAgY29uc3Qgd3MgPSBYTFNYLnV0aWxzLmFvYV90b19zaGVldChzaGlwbWVudERhdGEpXG4gICAgWExTWC51dGlscy5ib29rX2FwcGVuZF9zaGVldCh3Yiwgd3MsIGBFeHDDqWRpdGlvbiAke29yZGVyTnVtYmVyfWApXG5cbiAgICByZXR1cm4gd2JcbiAgfVxuXG4gIGNvbnN0IGdldENvbXBvbmVudERlc2NyaXB0aW9uID0gKGNvbXBvbmVudDogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICBjb25zdCBkZXNjcmlwdGlvbnM6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgICAnRk9CIEFtb3VudCc6ICdWYWxldXIgRnJlZSBPbiBCb2FyZCAtIFByaXggbWFyY2hhbmRpc2UgYXUgcG9ydCBkZSBkw6lwYXJ0JyxcbiAgICAgICdDdXN0b21zIER1dGllcyc6ICdEcm9pdHMgZGUgZG91YW5lIGV0IHRheGVzIGRcXCdpbXBvcnRhdGlvbicsXG4gICAgICAnUG9ydCBGZWVzJzogJ0ZyYWlzIHBvcnR1YWlyZXMgZXQgZGUgbWFudXRlbnRpb24nLFxuICAgICAgJ1NoaXBwaW5nIEZlZXMnOiAnRnJhaXMgZGUgdHJhbnNwb3J0IG1hcml0aW1lIGV0IHRlcnJlc3RyZScsXG4gICAgICAnT3RoZXIgQ29zdHMnOiAnRnJhaXMgZGl2ZXJzIChhc3N1cmFuY2UsIHRyYW5zaXQsIGV0Yy4pJ1xuICAgIH1cbiAgICByZXR1cm4gZGVzY3JpcHRpb25zW2NvbXBvbmVudF0gfHwgJ0Rlc2NyaXB0aW9uIG5vbiBkaXNwb25pYmxlJ1xuICB9XG5cbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxMYXlvdXQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeVwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTGF5b3V0PlxuICAgIClcbiAgfVxuXG4gIGNvbnN0IHRvdGFsU2hpcG1lbnRzID0gbW9ja0RhdGEubW9udGhseVNoaXBtZW50cy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgaXRlbS5zaGlwbWVudHMsIDApXG4gIGNvbnN0IHRvdGFsVmFsdWUgPSBtb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnZhbHVlLCAwKVxuICBjb25zdCBhdmdDb2VmZmljaWVudCA9IDEuMzVcblxuICByZXR1cm4gKFxuICAgIDxMYXlvdXQ+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5SZXBvcnRzICYgQW5hbHl0aWNzPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Db21wcmVoZW5zaXZlIGluc2lnaHRzIGludG8geW91ciBpbXBvcnQgY29zdHM8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtkYXRlUmFuZ2UuZnJvbX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERhdGVSYW5nZShwcmV2ID0+ICh7IC4uLnByZXYsIGZyb206IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtkYXRlUmFuZ2UudG99XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXREYXRlUmFuZ2UocHJldiA9PiAoeyAuLi5wcmV2LCB0bzogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNDBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBLUEkgQ2FyZHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlRvdGFsIFNoaXBtZW50czwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3RvdGFsU2hpcG1lbnRzfTwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICsxMiUgZnJvbSBsYXN0IHBlcmlvZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlRvdGFsIFZhbHVlPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57Zm9ybWF0Q3VycmVuY3kodG90YWxWYWx1ZSl9PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgKzglIGZyb20gbGFzdCBwZXJpb2RcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItMlwiPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5BdmcgQ29lZmZpY2llbnQ8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPnthdmdDb2VmZmljaWVudC50b0ZpeGVkKDIpfXg8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAtMiUgZnJvbSBsYXN0IHBlcmlvZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkF2ZyBNb250aGx5PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e01hdGgucm91bmQodG90YWxTaGlwbWVudHMgLyA2KX08L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBzaGlwbWVudHMgcGVyIG1vbnRoXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ2hhcnRzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICB7LyogTW9udGhseSBTaGlwbWVudHMgQ2hhcnQgKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBNb250aGx5IFNoaXBtZW50c1xuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBTaGlwbWVudCB2b2x1bWUgYW5kIHZhbHVlIHRyZW5kc1xuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PXszMDB9PlxuICAgICAgICAgICAgICAgIDxCYXJDaGFydCBkYXRhPXttb2NrRGF0YS5tb250aGx5U2hpcG1lbnRzfT5cbiAgICAgICAgICAgICAgICAgIDxDYXJ0ZXNpYW5HcmlkIHN0cm9rZURhc2hhcnJheT1cIjMgM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8WEF4aXMgZGF0YUtleT1cIm1vbnRoXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxZQXhpcyAvPlxuICAgICAgICAgICAgICAgICAgPFRvb2x0aXBcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0dGVyPXsodmFsdWUsIG5hbWUpID0+IFtcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lID09PSAnc2hpcG1lbnRzJyA/IHZhbHVlIDogZm9ybWF0Q3VycmVuY3kodmFsdWUgYXMgbnVtYmVyKSxcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lID09PSAnc2hpcG1lbnRzJyA/ICdTaGlwbWVudHMnIDogJ1ZhbHVlIChEWkQpJ1xuICAgICAgICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxCYXIgZGF0YUtleT1cInNoaXBtZW50c1wiIGZpbGw9XCIjM2I4MmY2XCIgLz5cbiAgICAgICAgICAgICAgICA8L0JhckNoYXJ0PlxuICAgICAgICAgICAgICA8L1Jlc3BvbnNpdmVDb250YWluZXI+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBDb3N0IEJyZWFrZG93biBDaGFydCAqL31cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFBpZUNoYXJ0IGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQ29zdCBCcmVha2Rvd25cbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgRGlzdHJpYnV0aW9uIG9mIGltcG9ydCBjb3N0c1xuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PXszMDB9PlxuICAgICAgICAgICAgICAgIDxSZWNoYXJ0c1BpZUNoYXJ0PlxuICAgICAgICAgICAgICAgICAgPFRvb2x0aXBcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0dGVyPXsodmFsdWUsIG5hbWUpID0+IFtcbiAgICAgICAgICAgICAgICAgICAgICBgJHt2YWx1ZX0lYCxcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lXG4gICAgICAgICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPFJlY2hhcnRzUGllQ2hhcnQgZGF0YT17bW9ja0RhdGEuY29zdEJyZWFrZG93bn0+XG4gICAgICAgICAgICAgICAgICAgIHttb2NrRGF0YS5jb3N0QnJlYWtkb3duLm1hcCgoZW50cnksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPENlbGwga2V5PXtgY2VsbC0ke2luZGV4fWB9IGZpbGw9e0NPTE9SU1tpbmRleCAlIENPTE9SUy5sZW5ndGhdfSAvPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvUmVjaGFydHNQaWVDaGFydD5cbiAgICAgICAgICAgICAgICA8L1JlY2hhcnRzUGllQ2hhcnQ+XG4gICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUNvbnRhaW5lcj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIHttb2NrRGF0YS5jb3N0QnJlYWtkb3duLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLm5hbWV9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBtci0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogQ09MT1JTW2luZGV4ICUgQ09MT1JTLmxlbmd0aF0gfX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aXRlbS52YWx1ZX0lPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZXBvcnQgR2VuZXJhdGlvbiAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPkdlbmVyYXRlIFJlcG9ydHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIEV4cG9ydCBkZXRhaWxlZCByZXBvcnRzIGZvciBhbmFseXNpcyBhbmQgcmVjb3JkIGtlZXBpbmdcbiAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTIwIGZsZXgtY29sXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFeHBvcnRSZXBvcnQoJ0NvbXByZWhlbnNpdmUnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTYgdy02IG1iLTJcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPkNvbXByZWhlbnNpdmUgUmVwb3J0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkFsbCBzaGlwbWVudHMgJiBjb3N0czwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTIwIGZsZXgtY29sXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFeHBvcnRSZXBvcnQoJ0Nvc3RfQW5hbHlzaXMnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC02IHctNiBtYi0yXCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5Db3N0IEFuYWx5c2lzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkRldGFpbGVkIGNvc3QgYnJlYWtkb3duPC9zcGFuPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMjAgZmxleC1jb2xcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUV4cG9ydFJlcG9ydCgnTW9udGhseV9TdW1tYXJ5Jyl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaC02IHctNiBtYi0yXCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5Nb250aGx5IFN1bW1hcnk8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UGVyaW9kLWJhc2VkIGFuYWx5c2lzPC9zcGFuPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogUmVjZW50IFNoaXBtZW50cyBUYWJsZSAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPlJlY2VudCBTaGlwbWVudHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIExhdGVzdCBpbXBvcnQgY2FsY3VsYXRpb25zIGFuZCB0aGVpciByZXN1bHRzXG4gICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgPHRoZWFkPlxuICAgICAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00XCI+T3JkZXIgTnVtYmVyPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTRcIj5TdXBwbGllcjwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00XCI+RGF0ZTwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHB5LTMgcHgtNFwiPkZPQiBBbW91bnQ8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodCBweS0zIHB4LTRcIj5MYW5kZWQgQ29zdDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHB5LTMgcHgtNFwiPkNvZWZmaWNpZW50PC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTRcIj5TdGF0dXM8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPkFjdGlvbnM8L3RoPlxuICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgIDx0Ym9keT5cbiAgICAgICAgICAgICAgICAgIHttb2NrRGF0YS5yZWNlbnRTaGlwbWVudHMubWFwKChzaGlwbWVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtzaGlwbWVudC5pZH0gY2xhc3NOYW1lPVwiYm9yZGVyLWIgaG92ZXI6YmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgZm9udC1tZWRpdW1cIj57c2hpcG1lbnQub3JkZXJOdW1iZXJ9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+e3NoaXBtZW50LnN1cHBsaWVyfTwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNFwiPntuZXcgRGF0ZShzaGlwbWVudC5kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC1yaWdodFwiPntmb3JtYXRVU0Qoc2hpcG1lbnQuZm9iQW1vdW50KX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC1yaWdodFwiPntmb3JtYXRDdXJyZW5jeShzaGlwbWVudC5sYW5kZWRDb3N0KX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC1yaWdodFwiPntzaGlwbWVudC5jb2VmZmljaWVudC50b0ZpeGVkKDIpfXg8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNoaXBtZW50LnN0YXR1cyA9PT0gJ0NvbXBsZXRlZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzaGlwbWVudC5zdGF0dXN9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRXhwb3J0UmVwb3J0KGBTaGlwbWVudF8ke3NoaXBtZW50Lm9yZGVyTnVtYmVyfWApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgRXhwb3J0XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVNlc3Npb24iLCJ1c2VSb3V0ZXIiLCJMYXlvdXQiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiSW5wdXQiLCJGaWxlVGV4dCIsIkRvd25sb2FkIiwiQ2FsZW5kYXIiLCJUcmVuZGluZ1VwIiwiRG9sbGFyU2lnbiIsIlBhY2thZ2UiLCJCYXJDaGFydDMiLCJQaWVDaGFydCIsIkJhckNoYXJ0IiwiQmFyIiwiWEF4aXMiLCJZQXhpcyIsIkNhcnRlc2lhbkdyaWQiLCJUb29sdGlwIiwiUmVzcG9uc2l2ZUNvbnRhaW5lciIsIlJlY2hhcnRzUGllQ2hhcnQiLCJDZWxsIiwiWExTWCIsInRvYXN0IiwibW9ja0RhdGEiLCJtb250aGx5U2hpcG1lbnRzIiwibW9udGgiLCJzaGlwbWVudHMiLCJ2YWx1ZSIsImNvc3RCcmVha2Rvd24iLCJuYW1lIiwiYW1vdW50IiwicmVjZW50U2hpcG1lbnRzIiwiaWQiLCJvcmRlck51bWJlciIsInN1cHBsaWVyIiwiZGF0ZSIsImZvYkFtb3VudCIsImxhbmRlZENvc3QiLCJjb2VmZmljaWVudCIsInN0YXR1cyIsIkNPTE9SUyIsIlJlcG9ydHMiLCJkYXRhIiwic2Vzc2lvbiIsInJvdXRlciIsImRhdGVSYW5nZSIsInNldERhdGVSYW5nZSIsImZyb20iLCJ0byIsInVzZUVmZmVjdCIsInB1c2giLCJmb3JtYXRDdXJyZW5jeSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiZm9ybWF0IiwiZm9ybWF0VVNEIiwiaGFuZGxlRXhwb3J0UmVwb3J0IiwicmVwb3J0VHlwZSIsImZpbGVOYW1lIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJ3b3JrYm9vayIsImdlbmVyYXRlQ29tcHJlaGVuc2l2ZVJlcG9ydCIsImdlbmVyYXRlQ29zdEFuYWx5c2lzUmVwb3J0IiwiZ2VuZXJhdGVNb250aGx5U3VtbWFyeVJlcG9ydCIsInN0YXJ0c1dpdGgiLCJyZXBsYWNlIiwiZ2VuZXJhdGVTaGlwbWVudFJlcG9ydCIsIndyaXRlRmlsZSIsInN1Y2Nlc3MiLCJlcnJvciIsImNvbnNvbGUiLCJ3YiIsInV0aWxzIiwiYm9va19uZXciLCJzdW1tYXJ5RGF0YSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInRvdGFsU2hpcG1lbnRzIiwidG90YWxWYWx1ZSIsInRvTG9jYWxlU3RyaW5nIiwiYXZnQ29lZmZpY2llbnQiLCJ0b0ZpeGVkIiwiTWF0aCIsInJvdW5kIiwibWFwIiwiaXRlbSIsInN1bW1hcnlXcyIsImFvYV90b19zaGVldCIsImJvb2tfYXBwZW5kX3NoZWV0Iiwic2hpcG1lbnRzRGF0YSIsInMiLCJzaGlwbWVudHNXcyIsIm1vbnRobHlEYXRhIiwibSIsIm1vbnRobHlXcyIsImNvc3REYXRhIiwiZ2V0Q29tcG9uZW50RGVzY3JpcHRpb24iLCJ3cyIsImluZGV4Iiwic2hpcG1lbnQiLCJmaW5kIiwiRXJyb3IiLCJleGNoYW5nZVJhdGUiLCJmb2JBbW91bnREemQiLCJmcmVpZ2h0RHpkIiwidG90YWxDaWZEemQiLCJpbXBvcnREZWxpdmVyeVR0YyIsImltcG9ydERlbGl2ZXJ5VHZhIiwiaW1wb3J0RGVsaXZlcnlIdCIsImN1c3RvbXNJbnNwZWN0aW9uVHRjIiwiY3VzdG9tc0luc3BlY3Rpb25UdmEiLCJjdXN0b21zSW5zcGVjdGlvbkh0IiwicG9ydEZlZXNUb3RhbFR0YyIsInBvcnRGZWVzVG90YWxIdCIsInNoaXBwaW5nQWdlbmN5VHRjIiwic2hpcHBpbmdBZ2VuY3lUdmEiLCJzaGlwcGluZ0FnZW5jeUh0IiwiZW1wdHlDb250YWluZXJzVHRjIiwiZW1wdHlDb250YWluZXJzVHZhIiwiZW1wdHlDb250YWluZXJzSHQiLCJkZW11cnJhZ2VIdCIsInNoaXBwaW5nRmVlc1RvdGFsVHRjIiwic2hpcHBpbmdGZWVzVG90YWxIdCIsIm1pc2NFeHBlbnNlc1R0YyIsIm1pc2NFeHBlbnNlc1R2YSIsIm1pc2NFeHBlbnNlc0h0IiwidHJhbnNpdEV4cGVuc2VzVHRjIiwidHJhbnNpdEV4cGVuc2VzVHZhIiwidHJhbnNpdEV4cGVuc2VzSHQiLCJsYW5kZWRDb3N0VHRjIiwibGFuZGVkQ29zdEh0Iiwic2hpcG1lbnREYXRhIiwiY29tcG9uZW50IiwiZGVzY3JpcHRpb25zIiwiZGl2IiwiY2xhc3NOYW1lIiwicmVkdWNlIiwic3VtIiwiaDEiLCJwIiwidHlwZSIsIm9uQ2hhbmdlIiwiZSIsInByZXYiLCJ0YXJnZXQiLCJ3aWR0aCIsImhlaWdodCIsInN0cm9rZURhc2hhcnJheSIsImRhdGFLZXkiLCJmb3JtYXR0ZXIiLCJmaWxsIiwiZW50cnkiLCJsZW5ndGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJzcGFuIiwidmFyaWFudCIsIm9uQ2xpY2siLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwidGQiLCJzaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});