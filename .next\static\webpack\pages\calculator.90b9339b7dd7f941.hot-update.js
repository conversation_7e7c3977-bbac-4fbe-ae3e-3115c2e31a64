"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = async ()=>{\n        try {\n            // Charger depuis la base de données via l'API\n            const response = await fetch(\"/api/shipment-receptions\");\n            if (!response.ok) {\n                throw new Error(\"Erreur lors du chargement des r\\xe9ceptions\");\n            }\n            const savedReceptions = await response.json();\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplier_name,\n                    orderNumber: reception.order_number,\n                    invoiceNumber: reception.invoice_number,\n                    exchangeRate: reception.exchange_rate,\n                    currency: reception.currency,\n                    fobAmount: reception.fob_amount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customs_duties_1_ttc,\n                    customsDuties1Tva: reception.customs_duties_1_tva,\n                    customsDuties1Ht: reception.customs_duties_1_ttc - reception.customs_duties_1_tva,\n                    customsDuties2Ht: reception.customs_duties_2_ht,\n                    importDeliveryTtc: reception.import_delivery_ttc,\n                    importDeliveryTva: reception.import_delivery_tva,\n                    importDeliveryHt: reception.import_delivery_ttc - reception.import_delivery_tva,\n                    customsInspectionTtc: reception.customs_inspection_ttc,\n                    customsInspectionTva: reception.customs_inspection_tva,\n                    customsInspectionHt: reception.customs_inspection_ttc - reception.customs_inspection_tva,\n                    shippingAgencyTtc: reception.shipping_agency_ttc,\n                    shippingAgencyTva: reception.shipping_agency_tva,\n                    shippingAgencyHt: reception.shipping_agency_ttc - reception.shipping_agency_tva,\n                    emptyContainersTtc: reception.empty_containers_ttc,\n                    emptyContainersTva: reception.empty_containers_tva,\n                    emptyContainersHt: reception.empty_containers_ttc - reception.empty_containers_tva,\n                    demurrageHt: reception.demurrage_ht,\n                    miscExpensesTtc: reception.misc_expenses_ttc,\n                    miscExpensesTva: reception.misc_expenses_tva,\n                    miscExpensesHt: reception.misc_expenses_ttc - reception.misc_expenses_tva,\n                    transitServicesTtc: reception.transit_services_ttc,\n                    transitServicesTva: reception.transit_services_tva,\n                    transitServicesHt: reception.transit_services_ttc - reception.transit_services_tva,\n                    createdAt: reception.created_at\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n            setError(\"Erreur lors du chargement des r\\xe9ceptions depuis la base de donn\\xe9es\");\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans la base de données via l'API\n            const response = await fetch(\"/api/shipment-receptions?invoiceNumber=\".concat(encodeURIComponent(invoiceNumber.trim())));\n            if (!response.ok) {\n                if (response.status === 404) {\n                    setError(\"Aucune r\\xe9ception trouv\\xe9e pour la facture: \".concat(invoiceNumber));\n                } else {\n                    throw new Error(\"Erreur lors de la recherche\");\n                }\n                setIsLoading(false);\n                return;\n            }\n            const reception = await response.json();\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplier_name,\n                orderNumber: reception.order_number,\n                invoiceNumber: reception.invoice_number,\n                exchangeRate: reception.exchange_rate,\n                currency: reception.currency,\n                fobAmount: reception.fob_amount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customs_duties_1_ttc,\n                customsDuties1Tva: reception.customs_duties_1_tva,\n                customsDuties1Ht: reception.customs_duties_1_ttc - reception.customs_duties_1_tva,\n                customsDuties2Ht: reception.customs_duties_2_ht,\n                importDeliveryTtc: reception.import_delivery_ttc,\n                importDeliveryTva: reception.import_delivery_tva,\n                importDeliveryHt: reception.import_delivery_ttc - reception.import_delivery_tva,\n                customsInspectionTtc: reception.customs_inspection_ttc,\n                customsInspectionTva: reception.customs_inspection_tva,\n                customsInspectionHt: reception.customs_inspection_ttc - reception.customs_inspection_tva,\n                shippingAgencyTtc: reception.shipping_agency_ttc,\n                shippingAgencyTva: reception.shipping_agency_tva,\n                shippingAgencyHt: reception.shipping_agency_ttc - reception.shipping_agency_tva,\n                emptyContainersTtc: reception.empty_containers_ttc,\n                emptyContainersTva: reception.empty_containers_tva,\n                emptyContainersHt: reception.empty_containers_ttc - reception.empty_containers_tva,\n                demurrageHt: reception.demurrage_ht,\n                miscExpensesTtc: reception.misc_expenses_ttc,\n                miscExpensesTva: reception.misc_expenses_tva,\n                miscExpensesHt: reception.misc_expenses_ttc - reception.misc_expenses_tva,\n                transitServicesTtc: reception.transit_services_ttc,\n                transitServicesTva: reception.transit_services_tva,\n                transitServicesHt: reception.transit_services_ttc - reception.transit_services_tva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception dans la base de donn\\xe9es\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(\"Co\\xfbts sauvegard\\xe9s avec succ\\xe8s pour la facture: \".concat(generatedCosts.invoiceNumber));\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(\"/shipment-reception?edit=\".concat(reception.id));\n    };\n    const deleteReception = async (reception)=>{\n        if (confirm('\\xcates-vous s\\xfbr de vouloir supprimer la r\\xe9ception \"'.concat(reception.supplierName, \" - \").concat(reception.orderNumber, '\" de la base de donn\\xe9es ?'))) {\n            try {\n                // Supprimer via l'API\n                const response = await fetch(\"/api/shipment-receptions?id=\".concat(reception.id), {\n                    method: \"DELETE\"\n                });\n                if (!response.ok) {\n                    throw new Error(\"Erreur lors de la suppression\");\n                }\n                // Recharger la liste\n                await loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if ((receptionData === null || receptionData === void 0 ? void 0 : receptionData.id) === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(\"R\\xe9ception supprim\\xe9e avec succ\\xe8s de la base de donn\\xe9es\");\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception: \" + error.message);\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const fobDzd = reception.fobAmount * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const landedCostHt = cifDzd + totalAllFees;\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Créer les données pour Excel (format CSV compatible)\n            const excelData = [\n                // En-têtes\n                [\n                    \"SHIPMENT RECEPTION DETAILED REPORT\"\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GENERAL INFORMATION\"\n                ],\n                [\n                    \"Supplier Name\",\n                    reception.supplierName\n                ],\n                [\n                    \"Order Number\",\n                    reception.orderNumber\n                ],\n                [\n                    \"Invoice Number\",\n                    reception.invoiceNumber\n                ],\n                [\n                    \"Currency\",\n                    reception.currency\n                ],\n                [\n                    \"Exchange Rate\",\n                    reception.exchangeRate.toFixed(5)\n                ],\n                [\n                    \"Created At\",\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GOODS PRICE\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (\" + reception.currency + \")\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"FOB Amount\",\n                    reception.fobAmount.toFixed(2),\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"Freight\",\n                    reception.freight.toFixed(2),\n                    (reception.freight * reception.exchangeRate).toFixed(2)\n                ],\n                [\n                    \"CIF Amount\",\n                    (reception.fobAmount + reception.freight).toFixed(2),\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"CUSTOMS DUTIES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Customs Duties 1\",\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2)\n                ],\n                [\n                    \"Customs Duties 2\",\n                    \"\",\n                    \"\",\n                    reception.customsDuties2Ht.toFixed(2)\n                ],\n                [\n                    \"TOTAL CUSTOMS DUTIES\",\n                    \"\",\n                    \"\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"PORT FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Import Delivery\",\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2)\n                ],\n                [\n                    \"Customs Inspection\",\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL PORT FEES\",\n                    \"\",\n                    \"\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"SHIPPING COMPANY FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Shipping Agency\",\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2)\n                ],\n                [\n                    \"Empty Containers\",\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2)\n                ],\n                [\n                    \"Demurrage\",\n                    \"\",\n                    \"\",\n                    reception.demurrageHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL SHIPPING FEES\",\n                    \"\",\n                    \"\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"MISCELLANEOUS EXPENSES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Misc Expenses\",\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"TRANSIT SERVICES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Transit Services\",\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"COST BREAKDOWN SUMMARY\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"CIF Amount\",\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"Customs Duties HT\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"Port Fees HT\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"Shipping Fees HT\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"Misc Expenses HT\",\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"Transit Services HT\",\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL ALL FEES HT\",\n                    totalAllFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"FINAL RESULTS\"\n                ],\n                [\n                    \"Description\",\n                    \"Value\"\n                ],\n                [\n                    \"FOB Amount (DZD)\",\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"LANDED COST HT (DZD)\",\n                    landedCostHt.toFixed(2)\n                ],\n                [\n                    \"Coefficient of Landed Cost\",\n                    landedCostCoefficient.toFixed(4)\n                ],\n                [\n                    \"Formula\",\n                    \"Landed Cost (DZD) / FOB (DZD)\"\n                ],\n                [\n                    \"Calculation\",\n                    landedCostHt.toFixed(2) + \" / \" + fobDzd.toFixed(2) + \" = \" + landedCostCoefficient.toFixed(4)\n                ]\n            ];\n            // Convertir en CSV pour Excel\n            const csvContent = excelData.map((row)=>row.map((cell)=>typeof cell === \"string\" && (cell.includes(\",\") || cell.includes('\"')) ? '\"'.concat(cell.replace(/\"/g, '\"\"'), '\"') : cell).join(\",\")).join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"reception_\".concat(reception.supplierName, \"_\").concat(reception.orderNumber, \"_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi pour la r\\xe9ception: \".concat(reception.supplierName, \" - \").concat(reception.orderNumber));\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? '\"'.concat(cell, '\"') : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"shipment_receptions_export_\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi ! \".concat(allReceptions.length, \" r\\xe9ceptions export\\xe9es.\"));\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 590,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 589,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 765,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 599,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 598,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"CITHuXboUsUYvZZyHh6Xlw/KVr8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});