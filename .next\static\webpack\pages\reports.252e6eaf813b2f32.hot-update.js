"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Configuration de base\n        const currency = \"USD\" // Dans une vraie app, ceci viendrait des données\n        ;\n        const exchangeRate = 134.50000 // Taux de change vers DZD\n        ;\n        const invoiceNumber = \"INV-2024-001\" // Simulé\n        ;\n        // Calculs de base\n        const fobAmount = shipment.fobAmount;\n        const freight = fobAmount * 0.15 // 15% du FOB\n        ;\n        const totalCif = fobAmount + freight;\n        // Conversions automatiques en DZD\n        const fobAmountDzd = fobAmount * exchangeRate;\n        const freightDzd = freight * exchangeRate;\n        const totalCifDzd = totalCif * exchangeRate;\n        // Valeurs simulées pour les frais (dans une vraie app, ces valeurs viendraient des champs de saisie)\n        // CUSTOMS DUTIES\n        const customsDuties1Ttc = 850000;\n        const customsDuties1Tva = 136000;\n        const customsDuties1Ht = customsDuties1Ttc - customsDuties1Tva;\n        const customsDuties2Ttc = 650000;\n        const customsDuties2Tva = 104000;\n        const customsDuties2Ht = customsDuties2Ttc - customsDuties2Tva;\n        const customsDutiesTotalTtc = customsDuties1Ttc + customsDuties2Ttc;\n        const customsDutiesTotalTva = customsDuties1Tva + customsDuties2Tva;\n        const customsDutiesTotalHt = customsDuties1Ht + customsDuties2Ht;\n        // PORT FEES\n        const importDeliveryTtc = 482344;\n        const importDeliveryTva = 77175;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = 289406;\n        const customsInspectionTva = 46305;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalTva = importDeliveryTva + customsInspectionTva;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // SHIPPING COMPANY FEES\n        const shippingAgencyTtc = 675281;\n        const shippingAgencyTva = 108045;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = 385875;\n        const emptyContainersTva = 61740;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = 192938 // HT seulement\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalTva = shippingAgencyTva + emptyContainersTva;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // OTHER MISCELLANEOUS EXPENSES\n        const miscExpensesTtc = 482344;\n        const miscExpensesTva = 77175;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        // TRANSIT SERVICES EXPENSES\n        const transitExpensesTtc = 385875;\n        const transitExpensesTva = 61740;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs finaux\n        const landedCostTtc = totalCifDzd + customsDutiesTotalTtc + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + customsDutiesTotalHt + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const landedCostCoefficient = landedCostHt / fobAmountDzd;\n        const shipmentData = [\n            // En-tête\n            [\n                \"\",\n                \"\",\n                \"Valeur DZD\",\n                \"\\xc9quivalent \".concat(currency)\n            ],\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"---------------------------------------------\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INVOICE #:\",\n                invoiceNumber\n            ],\n            [\n                \"Exchange Rate Used : DZD/\" + currency + \":\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"\"\n            ],\n            // Goods Price in Currency\n            [\n                \"Goods Price in Currency (\" + currency + \"):\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT:\",\n                fobAmount.toLocaleString(\"fr-FR\"),\n                fobAmount.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT:\",\n                freight.toLocaleString(\"fr-FR\"),\n                freight.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF (FOB + Freight):\",\n                totalCif.toLocaleString(\"fr-FR\"),\n                totalCif.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            // Conversion to DZD\n            [\n                \"Conversion to DZD:\"\n            ],\n            [\n                \"\",\n                \"FOB AMOUNT DZD (Automatic conversion):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\"),\n                (fobAmountDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"FREIGHT DZD (Automatic conversion):\",\n                freightDzd.toLocaleString(\"fr-FR\"),\n                (freightDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):\",\n                totalCifDzd.toLocaleString(\"fr-FR\"),\n                (totalCifDzd / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"_____________________________________________________________________________________\"\n            ],\n            // Customs Duties\n            [\n                \"Customs Duties:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (ALGERIA CUSTOMS):\"\n            ],\n            [\n                \"\",\n                \"D3 N#:\",\n                \"D3-2024-001\"\n            ],\n            [\n                \"\",\n                \"D3 Date:\",\n                \"15/01/2024\"\n            ],\n            [\n                \"\",\n                \"QUITTANCE 1:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD Total TTC:\",\n                customsDuties1Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD TVA:\",\n                customsDuties1Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties1Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties1 DZD HT (TTC - TVA):\",\n                customsDuties1Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties1Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"QUITTANCE 2:\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD Total TTC:\",\n                customsDuties2Ttc.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ttc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD TVA:\",\n                customsDuties2Tva.toLocaleString(\"fr-FR\"),\n                (customsDuties2Tva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"Customs Duties2 DZD HT (TTC - TVA):\",\n                customsDuties2Ht.toLocaleString(\"fr-FR\"),\n                (customsDuties2Ht / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD Total TTC:',\n                customsDutiesTotalTtc.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD TVA:',\n                customsDutiesTotalTva.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'Customs Duties \"Overall Totals\" DZD HT (TTC - TVA):',\n                customsDutiesTotalHt.toLocaleString(\"fr-FR\"),\n                (customsDutiesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------------\"\n            ],\n            // Port Fees\n            [\n                \"PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (IMPORT DELIVERY):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE N#:\",\n                \"IMP-DEL-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY Total TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\"),\n                (importDeliveryTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\"),\n                (importDeliveryTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"IMPORT DELIVERY HT (TTC - TVA):\",\n                importDeliveryHt.toLocaleString(\"fr-FR\"),\n                (importDeliveryHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (CUSTOMS INSPECTION):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE N#:\",\n                \"CUST-INS-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION INVOICE DATE:\",\n                \"16/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION Total TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\"),\n                (customsInspectionTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\"),\n                (customsInspectionTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"CUSTOMS INSPECTION HT (TTC - TVA):\",\n                customsInspectionHt.toLocaleString(\"fr-FR\"),\n                (customsInspectionHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF PORT FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD Total TTC:',\n                portFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD TVA:',\n                portFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (portFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                portFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (portFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"---------------------------------------------------------------------------------------------------------------\"\n            ],\n            // Shipping Company Fees\n            [\n                \"SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE N#:\",\n                \"SHIP-AGE-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES INVOICE DATE:\",\n                \"17/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES Total TTC (DZD):\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES TVA (DZD):\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\"),\n                (shippingAgencyTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\"),\n                (shippingAgencyHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (EMPTY CONTAINERS):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE N#:\",\n                \"EMPTY-CONT-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN INVOICE DATE:\",\n                \"18/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN Total TTC (DZD):\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\"),\n                (emptyContainersTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN TVA (DZD):\",\n                emptyContainersTva.toLocaleString(\"fr-FR\"),\n                (emptyContainersTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA):\",\n                emptyContainersHt.toLocaleString(\"fr-FR\"),\n                (emptyContainersHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE N#:\",\n                \"DEMUR-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE INVOICE DATE:\",\n                \"19/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"DEMURRAGE HT (DZD) (This currency field must be entered manually.):\",\n                demurrageHt.toLocaleString(\"fr-FR\"),\n                (demurrageHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\",\n                \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC:',\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA:',\n                shippingFeesTotalTva.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA):',\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\"),\n                (shippingFeesTotalHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"-------------------------------------------------------\"\n            ],\n            // Other Miscellaneous Expenses\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE N#:\",\n                \"MISC-EXP-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE:\",\n                \"20/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TTC (DZD):\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\"),\n                (miscExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES TVA (DZD):\",\n                miscExpensesTva.toLocaleString(\"fr-FR\"),\n                (miscExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):\",\n                miscExpensesHt.toLocaleString(\"fr-FR\"),\n                (miscExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"------------------------------------------------------------------------------\"\n            ],\n            // Transit Services Expenses\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"\",\n                \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE N#:\",\n                \"TRANS-SERV-001\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES INVOICE DATE:\",\n                \"21/01/2024\"\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TTC (DZD):\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\"),\n                (transitExpensesTtc / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES TVA (DZD):\",\n                transitExpensesTva.toLocaleString(\"fr-FR\"),\n                (transitExpensesTva / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\",\n                \"\",\n                \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):\",\n                transitExpensesHt.toLocaleString(\"fr-FR\"),\n                (transitExpensesHt / exchangeRate).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"\"\n            ],\n            // Calculs finaux\n            [\n                \"CALCULS FINAUX:\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostHt / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES HT + OTHER MISCELLANEOUS EXPENSES HT + SHIPPING COMPANY FEES Overall Totals HT + PORT FEES Overall Totals HT + CUSTOMS DUTIES Overall Totals HT + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\",\n                (landedCostTtc / exchangeRate).toLocaleString(\"fr-FR\") + \" \" + currency\n            ],\n            [\n                \"= TRANSIT SERVICES EXPENSES TTC + OTHER MISCELLANEOUS EXPENSES TTC + SHIPPING COMPANY FEES Overall Totals TTC + PORT FEES Overall Totals TTC + CUSTOMS DUTIES Overall Totals TTC + TOTAL AMOUNT CIF DZD\"\n            ],\n            [\n                \"= \" + transitExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + customsDutiesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed cost coefficient:\",\n                landedCostCoefficient.toFixed(5),\n                landedCostCoefficient.toFixed(5)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 475,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 474,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Export\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 488,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 487,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"V9rHqNTl7h2x7X2rKtHE3y76I5M=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});