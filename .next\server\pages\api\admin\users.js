"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/users";
exports.ids = ["pages/api/admin/users"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/credentials":
/*!**************************************************!*\
  !*** external "next-auth/providers/credentials" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/credentials");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cusers.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cusers.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_users_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\users.ts */ \"(api)/./pages/api/admin/users.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/users\",\n        pathname: \"/api/admin/users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_users_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cusers.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/admin/users.ts":
/*!**********************************!*\
  !*** ./pages/api/admin/users.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _auth_register__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../auth/register */ \"(api)/./pages/api/auth/register.ts\");\n\n\n\n// Mock active users database (should be imported from auth file)\nconst activeUsers = [];\nasync function handler(req, res) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    // Check if user is authenticated and is super admin\n    if (!session || session.user.role !== \"super_admin\") {\n        return res.status(403).json({\n            message: \"Acc\\xe8s refus\\xe9. Seul l'administrateur principal peut acc\\xe9der \\xe0 cette ressource.\"\n        });\n    }\n    if (req.method === \"GET\") {\n        // Get all pending users\n        return res.status(200).json({\n            pendingUsers: _auth_register__WEBPACK_IMPORTED_MODULE_2__.pendingUsers.map((user)=>({\n                    id: user.id,\n                    email: user.email,\n                    fullName: user.fullName,\n                    companyName: user.companyName,\n                    createdAt: user.createdAt,\n                    status: user.status\n                })),\n            activeUsers: activeUsers.map((user)=>({\n                    id: user.id,\n                    email: user.email,\n                    fullName: user.fullName,\n                    companyName: user.companyName,\n                    role: user.role,\n                    status: user.status,\n                    approvedAt: user.approvedAt\n                }))\n        });\n    }\n    if (req.method === \"POST\") {\n        const { action, userId } = req.body;\n        if (!action || !userId) {\n            return res.status(400).json({\n                message: \"Action et ID utilisateur requis\"\n            });\n        }\n        const userIndex = _auth_register__WEBPACK_IMPORTED_MODULE_2__.pendingUsers.findIndex((u)=>u.id === userId);\n        if (userIndex === -1) {\n            return res.status(404).json({\n                message: \"Utilisateur non trouv\\xe9\"\n            });\n        }\n        const user = _auth_register__WEBPACK_IMPORTED_MODULE_2__.pendingUsers[userIndex];\n        if (action === \"approve\") {\n            // Approve user\n            const approvedUser = {\n                ...user,\n                status: \"active\",\n                approvedBy: session.user.id,\n                approvedAt: new Date().toISOString()\n            };\n            // Move from pending to active\n            activeUsers.push(approvedUser);\n            _auth_register__WEBPACK_IMPORTED_MODULE_2__.pendingUsers.splice(userIndex, 1);\n            return res.status(200).json({\n                message: `Utilisateur ${user.fullName} approuvé avec succès`,\n                user: approvedUser\n            });\n        }\n        if (action === \"reject\") {\n            // Reject user\n            _auth_register__WEBPACK_IMPORTED_MODULE_2__.pendingUsers.splice(userIndex, 1);\n            return res.status(200).json({\n                message: `Demande de ${user.fullName} rejetée`\n            });\n        }\n        return res.status(400).json({\n            message: \"Action non valide\"\n        });\n    }\n    if (req.method === \"DELETE\") {\n        const { userId } = req.query;\n        if (!userId) {\n            return res.status(400).json({\n                message: \"ID utilisateur requis\"\n            });\n        }\n        // Remove from active users\n        const activeUserIndex = activeUsers.findIndex((u)=>u.id === userId);\n        if (activeUserIndex !== -1) {\n            const user = activeUsers[activeUserIndex];\n            activeUsers.splice(activeUserIndex, 1);\n            return res.status(200).json({\n                message: `Utilisateur ${user.fullName} supprimé`\n            });\n        }\n        return res.status(404).json({\n            message: \"Utilisateur non trouv\\xe9\"\n        });\n    }\n    return res.status(405).json({\n        message: \"M\\xe9thode non autoris\\xe9e\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/users.ts\n");

/***/ }),

/***/ "(api)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"next-auth/providers/credentials\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Mock user database for demo purposes\nconst mockUsers = [\n    {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        password: \"123SOLEIL@2025\",\n        name: \"Said BELDJILALI\",\n        role: \"super_admin\",\n        company: \"Administration Principale\",\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        email: \"<EMAIL>\",\n        password: \"password123\",\n        name: \"Admin User\",\n        role: \"admin\",\n        company: \"Demo Company\",\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        email: \"<EMAIL>\",\n        password: \"password123\",\n        name: \"Demo User\",\n        role: \"user\",\n        company: \"Demo Company\",\n        status: \"active\"\n    }\n];\n// Pending users awaiting approval\nconst pendingUsers = [];\nconst authOptions = {\n    providers: [\n        next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default()({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // Find user in mock database\n                const user = mockUsers.find((u)=>u.email === credentials.email && u.password === credentials.password && u.status === \"active\");\n                if (user) {\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        company: user.company\n                    };\n                }\n                return null;\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\"\n    },\n    secret: \"your-secret-key-change-this-in-production\" || 0\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api)/./pages/api/auth/register.ts":
/*!************************************!*\
  !*** ./pages/api/auth/register.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler),\n/* harmony export */   pendingUsers: () => (/* binding */ pendingUsers)\n/* harmony export */ });\n// Mock database for pending users\nlet pendingUsers = [];\nlet userIdCounter = 100;\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    const { fullName, email, password, companyName } = req.body;\n    // Validation\n    if (!fullName || !email || !password) {\n        return res.status(400).json({\n            message: \"Tous les champs obligatoires doivent \\xeatre remplis\"\n        });\n    }\n    if (password.length < 6) {\n        return res.status(400).json({\n            message: \"Le mot de passe doit contenir au moins 6 caract\\xe8res\"\n        });\n    }\n    // Check if user already exists (in pending or active users)\n    const existingUser = pendingUsers.find((u)=>u.email === email);\n    if (existingUser) {\n        return res.status(400).json({\n            message: \"Un utilisateur avec cet email existe d\\xe9j\\xe0\"\n        });\n    }\n    // Create pending user\n    const newUser = {\n        id: (userIdCounter++).toString(),\n        email,\n        fullName,\n        password,\n        companyName: companyName || \"\",\n        role: \"user\",\n        status: \"pending\",\n        createdAt: new Date().toISOString(),\n        approvedBy: null,\n        approvedAt: null\n    };\n    pendingUsers.push(newUser);\n    res.status(201).json({\n        message: \"Votre demande de cr\\xe9ation de compte a \\xe9t\\xe9 soumise. Elle sera examin\\xe9e par l'administrateur principal.\",\n        userId: newUser.id\n    });\n}\n// Export for admin access\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/register.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cusers.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();