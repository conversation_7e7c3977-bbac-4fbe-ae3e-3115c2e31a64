"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/shipment-reception",{

/***/ "./pages/shipment-reception.tsx":
/*!**************************************!*\
  !*** ./pages/shipment-reception.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShipmentReception; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Save,Ship!=!lucide-react */ \"__barrel_optimize__?names=FileText,Save,Ship!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    // Arrival Notice\n    voyageNumber: \"\",\n    billOfLading: \"\",\n    vesselName: \"\",\n    shipowner: \"\",\n    actualTimeOfArrival: \"\",\n    // General Information\n    exchangeRate: 0,\n    currency: \"USD\",\n    supplierName: \"\",\n    shipmentType: \"SEA\",\n    orderNumber: \"\",\n    dateOfOrderNumber: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    fromLocation: \"\",\n    toLocation: \"\",\n    operationType: \"OPEX\",\n    descriptionOfGoods: \"SPARE_PARTS_VEHICLES\",\n    bankName: \"\",\n    lcNumber: \"\",\n    validationLC: \"\",\n    paymentTerm: \"\",\n    priceTerm: \"\",\n    quantityPcs: 0,\n    numberContainers20: 0,\n    numberContainers40: 0,\n    numberPackages: 0,\n    // Goods Price in Currency\n    fobAmount: 0,\n    freight: 0,\n    totalAmountCif: 0,\n    // Conversion to DZD (calculated automatically)\n    fobAmountDzd: 0,\n    freightDzd: 0,\n    totalAmountCifDzd: 0,\n    // Customs Duties\n    costAllocationNameCustoms: \"ALGERIA CUSTOMS\",\n    d3Number: \"\",\n    d3Date: \"\",\n    // Quittance 1\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties1Ht: 0,\n    // Quittance 2\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    customsDuties2Ht: 0,\n    // Overall Totals Customs Duties\n    customsDutiesOverallTtc: 0,\n    customsDutiesOverallTva: 0,\n    customsDutiesOverallHt: 0,\n    // Port Fees - Import Delivery\n    costAllocationNameImportDelivery: \"IMPORT DELIVERY\",\n    importDeliveryInvoiceNumber: \"\",\n    importDeliveryInvoiceDate: \"\",\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    importDeliveryHt: 0,\n    // Port Fees - Customs Inspection\n    costAllocationNameCustomsInspection: \"CUSTOMS INSPECTION\",\n    customsInspectionInvoiceNumber: \"\",\n    customsInspectionInvoiceDate: \"\",\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    customsInspectionHt: 0,\n    // Overall Totals Port Fees\n    portFeesOverallTtc: 0,\n    portFeesOverallTva: 0,\n    portFeesOverallHt: 0,\n    // Shipping Company Fees - Shipping Agency Services\n    costAllocationNameShippingAgency: \"SHIPPING AGENCY SERVICES\",\n    shippingAgencyInvoiceNumber: \"\",\n    shippingAgencyInvoiceDate: \"\",\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    shippingAgencyHt: 0,\n    // Shipping Company Fees - Empty Containers\n    costAllocationNameEmptyContainers: \"EMPTY CONTAINERS\",\n    emptyContainersInvoiceNumber: \"\",\n    emptyContainersInvoiceDate: \"\",\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    emptyContainersHt: 0,\n    // Shipping Company Fees - Demurrage\n    costAllocationNameDemurrage: \"DEMURRAGE IF PRESENT\",\n    demurrageInvoiceNumber: \"\",\n    demurrageInvoiceDate: \"\",\n    demurrageHt: 0,\n    // Overall Totals Shipping Company Fees\n    shippingFeesOverallTtc: 0,\n    shippingFeesOverallTva: 0,\n    shippingFeesOverallHt: 0,\n    // Other Miscellaneous Expenses\n    costAllocationNameMiscExpenses: \"OTHER MISCELLANEOUS EXPENSES\",\n    miscExpensesInvoiceNumber: \"\",\n    miscExpensesInvoiceDate: \"\",\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    miscExpensesHt: 0,\n    // Transit Services Expenses\n    costAllocationNameTransitServices: \"TRANSIT SERVICES EXPENSES\",\n    transitServicesInvoiceNumber: \"\",\n    transitServicesInvoiceDate: \"\",\n    transitServicesTtc: 0,\n    transitServicesTva: 0,\n    transitServicesHt: 0\n};\nfunction ShipmentReception() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [currentExchangeRate, setCurrentExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoadingExchangeRate, setIsLoadingExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Récupérer le taux de change depuis les Settings de l'admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchExchangeRate = async ()=>{\n            try {\n                setIsLoadingExchangeRate(true);\n                // TODO: Remplacer par l'API réelle des Settings\n                // const response = await fetch('/api/settings/exchange-rate')\n                // const data = await response.json()\n                // Pour l'instant, simulation avec localStorage ou valeur par défaut\n                const savedRate = localStorage.getItem(\"admin-exchange-rate\");\n                const rate = savedRate ? parseFloat(savedRate) : 134.50000;\n                setCurrentExchangeRate(rate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: rate\n                    }));\n            } catch (error) {\n                console.error(\"Error fetching exchange rate:\", error);\n                // Valeur de fallback\n                const fallbackRate = 134.50000;\n                setCurrentExchangeRate(fallbackRate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: fallbackRate\n                    }));\n            } finally{\n                setIsLoadingExchangeRate(false);\n            }\n        };\n        if (status === \"authenticated\") {\n            fetchExchangeRate();\n        }\n    }, [\n        status\n    ]);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>{\n            const updated = {\n                ...prev,\n                [field]: value\n            };\n            // Calculs automatiques pour les conversions DZD\n            if (field === \"fobAmount\" || field === \"exchangeRate\") {\n                updated.fobAmountDzd = updated.fobAmount * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            if (field === \"freight\" || field === \"exchangeRate\") {\n                updated.freightDzd = updated.freight * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            // Calculs automatiques pour les champs HT\n            if (field === \"customsDuties1Ttc\" || field === \"customsDuties1Tva\") {\n                updated.customsDuties1Ht = updated.customsDuties1Ttc - updated.customsDuties1Tva;\n            }\n            // Calculs des totaux Customs Duties\n            updated.customsDutiesOverallTtc = updated.customsDuties1Ttc;\n            updated.customsDutiesOverallTva = updated.customsDuties1Tva;\n            updated.customsDutiesOverallHt = updated.customsDuties1Ttc - updated.customsDuties1Tva + updated.customsDuties2Ht;\n            // Calculs automatiques Port Fees\n            if (field === \"importDeliveryTtc\" || field === \"importDeliveryTva\") {\n                updated.importDeliveryHt = updated.importDeliveryTtc - updated.importDeliveryTva;\n            }\n            if (field === \"customsInspectionTtc\" || field === \"customsInspectionTva\") {\n                updated.customsInspectionHt = updated.customsInspectionTtc - updated.customsInspectionTva;\n            }\n            // Calculs des totaux Port Fees\n            updated.portFeesOverallTtc = updated.importDeliveryTtc + updated.customsInspectionTtc;\n            updated.portFeesOverallTva = updated.importDeliveryTva + updated.customsInspectionTva;\n            updated.portFeesOverallHt = updated.importDeliveryHt + updated.customsInspectionHt;\n            // Calculs automatiques Shipping Fees\n            if (field === \"shippingAgencyTtc\" || field === \"shippingAgencyTva\") {\n                updated.shippingAgencyHt = updated.shippingAgencyTtc - updated.shippingAgencyTva;\n            }\n            if (field === \"emptyContainersTtc\" || field === \"emptyContainersTva\") {\n                updated.emptyContainersHt = updated.emptyContainersTtc - updated.emptyContainersTva;\n            }\n            // Calculs des totaux Shipping Fees\n            updated.shippingFeesOverallTtc = updated.shippingAgencyTtc + updated.emptyContainersTtc + updated.demurrageHt;\n            updated.shippingFeesOverallTva = updated.shippingAgencyTva + updated.emptyContainersTva;\n            updated.shippingFeesOverallHt = updated.shippingAgencyHt + updated.emptyContainersHt + updated.demurrageHt;\n            // Calculs automatiques Misc Expenses\n            if (field === \"miscExpensesTtc\" || field === \"miscExpensesTva\") {\n                updated.miscExpensesHt = updated.miscExpensesTtc - updated.miscExpensesTva;\n            }\n            // Calculs automatiques Transit Services\n            if (field === \"transitServicesTtc\" || field === \"transitServicesTva\") {\n                updated.transitServicesHt = updated.transitServicesTtc - updated.transitServicesTva;\n            }\n            return updated;\n        });\n    };\n    const saveDraft = ()=>{\n        // Générer un nom automatique basé sur le supplier et order number\n        const draftName = form.supplierName && form.orderNumber ? \"\".concat(form.supplierName, \" - \").concat(form.orderNumber) : \"Reception Draft \".concat(new Date().toLocaleDateString(\"fr-FR\"), \" \").concat(new Date().toLocaleTimeString(\"fr-FR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        }));\n        // Sauvegarder dans localStorage pour l'instant\n        const savedDrafts = JSON.parse(localStorage.getItem(\"shipment-reception-drafts\") || \"[]\");\n        const newDraft = {\n            id: Date.now().toString(),\n            name: draftName,\n            form: {\n                ...form\n            },\n            savedAt: new Date().toISOString()\n        };\n        savedDrafts.push(newDraft);\n        localStorage.setItem(\"shipment-reception-drafts\", JSON.stringify(savedDrafts));\n        alert('Brouillon sauvegard\\xe9: \"'.concat(draftName, '\"'));\n    };\n    const saveReception = async ()=>{\n        try {\n            // Validation des champs obligatoires\n            if (!form.supplierName || !form.orderNumber || !form.invoiceNumber) {\n                alert(\"Veuillez remplir les champs obligatoires : Supplier Name, Order Number, Invoice Number\");\n                return;\n            }\n            // TODO: Remplacer par l'API réelle\n            // const response = await fetch('/api/shipment-receptions', {\n            //   method: 'POST',\n            //   headers: { 'Content-Type': 'application/json' },\n            //   body: JSON.stringify(form)\n            // })\n            // Pour l'instant, simulation avec localStorage\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const newReception = {\n                id: Date.now().toString(),\n                ...form,\n                createdAt: new Date().toISOString(),\n                status: \"completed\"\n            };\n            savedReceptions.push(newReception);\n            localStorage.setItem(\"shipment-receptions\", JSON.stringify(savedReceptions));\n            alert('R\\xe9ception sauvegard\\xe9e avec succ\\xe8s: \"'.concat(form.supplierName, \" - \").concat(form.orderNumber, '\"'));\n        // Optionnel: Rediriger vers la liste des réceptions\n        // router.push('/shipment-receptions')\n        } catch (error) {\n            console.error(\"Error saving reception:\", error);\n            alert(\"Erreur lors de la sauvegarde de la r\\xe9ception\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                lineNumber: 446,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Shipment Reception\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Record merchandise reception from arrival notice to container release\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: saveDraft,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: saveReception,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Reception\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-1 gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Ship, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Arrival Notice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Vessel arrival and shipping information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: 'Voyage Number - \"CALL AT PORT\" (ESCALE) N\\xb0'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.voyageNumber,\n                                                            onChange: (e)=>handleInputChange(\"voyageNumber\", e.target.value),\n                                                            placeholder: \"Enter voyage number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Bill of Lading (B/L) (Connaissement) N\\xb0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.billOfLading,\n                                                            onChange: (e)=>handleInputChange(\"billOfLading\", e.target.value),\n                                                            placeholder: \"Enter B/L number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Vessel Name (Navire)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.vesselName,\n                                                            onChange: (e)=>handleInputChange(\"vesselName\", e.target.value),\n                                                            placeholder: \"Enter vessel name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Shipowner (Armateur)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.shipowner,\n                                                            onChange: (e)=>handleInputChange(\"shipowner\", e.target.value),\n                                                            placeholder: \"Enter shipowner name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Actual Time of Arrival (Date Accostage)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            type: \"datetime-local\",\n                                                            value: form.actualTimeOfArrival,\n                                                            onChange: (e)=>handleInputChange(\"actualTimeOfArrival\", e.target.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"General Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Basic shipment and order details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: [\n                                                                    \"Exchange Rate Used : DZD/\",\n                                                                    form.currency,\n                                                                    \" *\",\n                                                                    isLoadingExchangeRate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-blue-600 ml-2\",\n                                                                        children: \"(Loading...)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"0.00000\",\n                                                                className: \"bg-blue-50\",\n                                                                disabled: isLoadingExchangeRate\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"SUPPLIER NAME *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Shipment Type *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.shipmentType,\n                                                                onChange: (e)=>handleInputChange(\"shipmentType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SEA\",\n                                                                        children: \"SEA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"AIR\",\n                                                                        children: \"AIR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EXPRESS\",\n                                                                        children: \"Express\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"ORDER NUMBER * (odoo)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DATE OF ORDER NUMBER *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.dateOfOrderNumber,\n                                                                onChange: (e)=>handleInputChange(\"dateOfOrderNumber\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"INVOICE #\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.invoiceDate,\n                                                                onChange: (e)=>handleInputChange(\"invoiceDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"FROM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.fromLocation,\n                                                                onChange: (e)=>handleInputChange(\"fromLocation\", e.target.value),\n                                                                placeholder: \"Origin location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"TO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.toLocation,\n                                                                onChange: (e)=>handleInputChange(\"toLocation\", e.target.value),\n                                                                placeholder: \"Destination location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"OPERATION TYPE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.operationType,\n                                                                onChange: (e)=>handleInputChange(\"operationType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OPEX\",\n                                                                        children: \"Operational Expenses (OpEx)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"INVESTMENT\",\n                                                                        children: \"Investment (or Equipment)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"REINVESTMENT\",\n                                                                        children: \"Reinvestment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"RAW_MATERIALS\",\n                                                                        children: \"Raw Materials and Semi-finished Products\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS\",\n                                                                        children: \"Spare Parts (for maintenance or specific resale)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"GOODS_RESALE\",\n                                                                        children: \"Goods for Resale as Is\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SERVICES\",\n                                                                        children: \"Services (intangible)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"TEMPORARY_IMPORT\",\n                                                                        children: \"Temporary Importation (or Temporary Admission)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DESCRIPTION OF THE GOODS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.descriptionOfGoods,\n                                                                onChange: (e)=>handleInputChange(\"descriptionOfGoods\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS_VEHICLES\",\n                                                                        children: \"SPARE PARTS FOR VEHICLES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LUBRICANT\",\n                                                                        children: \"LUBRICANT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ACCESSORY\",\n                                                                        children: \"ACCESSORY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"OTHER\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Banking & Payment Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Bank Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.bankName,\n                                                                        onChange: (e)=>handleInputChange(\"bankName\", e.target.value),\n                                                                        placeholder: \"Enter bank name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"L/C N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.lcNumber,\n                                                                        onChange: (e)=>handleInputChange(\"lcNumber\", e.target.value),\n                                                                        placeholder: \"Enter L/C number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.validationLC,\n                                                                        onChange: (e)=>handleInputChange(\"validationLC\", e.target.value),\n                                                                        placeholder: \"Enter validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PAYMENT TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.paymentTerm,\n                                                                        onChange: (e)=>handleInputChange(\"paymentTerm\", e.target.value),\n                                                                        placeholder: \"Enter payment term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PRICE TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.priceTerm,\n                                                                        onChange: (e)=>handleInputChange(\"priceTerm\", e.target.value),\n                                                                        placeholder: \"Enter price term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Quantities & Container Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"QUANTITY (PCS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.quantityPcs,\n                                                                        onChange: (e)=>handleInputChange(\"quantityPcs\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 20 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers20,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers20\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 40 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers40,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers40\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number PACKAGES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberPackages,\n                                                                        onChange: (e)=>handleInputChange(\"numberPackages\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: [\n                                                    \"Goods Price in Currency (\",\n                                                    form.currency,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"FOB, Freight and CIF amounts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: [\n                                                            \"Goods Price in \",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 838,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount,\n                                                                        onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 850,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight,\n                                                                        onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF (FOB + Freight)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount + form.freight,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Conversion to DZD (Automatic conversion)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 882,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 885,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 898,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 908,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: (form.fobAmount + form.freight) * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-50 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 911,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Exchange Rate Used: \",\n                                                            form.exchangeRate.toFixed(5),\n                                                            \" DZD/\",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Customs Duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Algeria customs fees and duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Customs Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"COST ALLOCATION NAME (ALGERIA CUSTOMS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.costAllocationNameCustoms,\n                                                                        onChange: (e)=>handleInputChange(\"costAllocationNameCustoms\", e.target.value),\n                                                                        placeholder: \"ALGERIA CUSTOMS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 943,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 950,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.d3Number,\n                                                                        onChange: (e)=>handleInputChange(\"d3Number\", e.target.value),\n                                                                        placeholder: \"Enter D3 number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.d3Date,\n                                                                        onChange: (e)=>handleInputChange(\"d3Date\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 963,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 936,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1004,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1000,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-1 gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Customs Duties2 DZD HT (manually entered)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.customsDuties2Ht,\n                                                                    onChange: (e)=>handleInputChange(\"customsDuties2Ht\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"bg-orange-50 border-orange-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-yellow-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1041,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1054,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD HT'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ht,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1070,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1066,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Port Fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Port and customs inspection fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (IMPORT DELIVERY)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.importDeliveryInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.importDeliveryInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1118,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1121,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1117,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1130,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1133,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1129,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1142,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1145,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1141,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (CUSTOMS INSPECTION)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1162,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.customsInspectionInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.customsInspectionInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1187,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTva,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1208,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc - form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1211,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-blue-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF PORT FEES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1228,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc + form.customsInspectionTtc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1231,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1241,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva + form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA)'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1254,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva + (form.customsInspectionTtc - form.customsInspectionTva),\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1085,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Shipping Company Fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Shipping agency, containers and demurrage fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1283,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.shippingAgencyInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1286,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1282,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1293,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.shippingAgencyInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1296,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1292,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES Total TTC (DZD) *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1305,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1308,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1304,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES TVA (DZD) *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTva,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc - form.shippingAgencyTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (EMPTY CONTAINERS)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1349,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.emptyContainersInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1352,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.emptyContainersInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN Total TTC (DZD)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1371,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTtc,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1370,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN TVA (DZD)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1383,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTva,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1386,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1395,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTtc - form.emptyContainersTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"DEMURRAGE INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.demurrageInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"demurrageInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1418,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"DEMURRAGE INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1425,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.demurrageInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"demurrageInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"DEMURRAGE HT (DZD) (This currency field must be entered manually.)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1437,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.demurrageHt,\n                                                                    onChange: (e)=>handleInputChange(\"demurrageHt\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"bg-orange-50 border-orange-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1440,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-green-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1457,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1460,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1470,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTva + form.emptyContainersTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA)'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1483,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc - form.shippingAgencyTva + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1482,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Other Miscellaneous Expenses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1503,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Various additional costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES INVOICE N#\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1511,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    value: form.miscExpensesInvoiceNumber,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesInvoiceNumber\", e.target.value),\n                                                                    placeholder: \"Enter invoice number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1514,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1510,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1521,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"date\",\n                                                                    value: form.miscExpensesInvoiceDate,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesInvoiceDate\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1524,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1520,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES TTC (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1533,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTtc,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesTtc\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1536,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES TVA (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1545,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTva,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesTva\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1548,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1544,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1557,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTtc - form.miscExpensesTva,\n                                                                    readOnly: true,\n                                                                    className: \"bg-gray-50 text-gray-600\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1560,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 1507,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1506,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1501,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Transit Services Expenses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1577,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Transit and logistics services\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1578,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1576,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES INVOICE N#\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1585,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    value: form.transitServicesInvoiceNumber,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesInvoiceNumber\", e.target.value),\n                                                                    placeholder: \"Enter invoice number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1588,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1584,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES INVOICE DATE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1595,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"date\",\n                                                                    value: form.transitServicesInvoiceDate,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesInvoiceDate\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1598,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1594,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1583,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES TTC (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTtc,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesTtc\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1610,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES TVA (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1619,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTva,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesTva\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1622,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1618,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1631,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTtc - form.transitServicesTva,\n                                                                    readOnly: true,\n                                                                    className: \"bg-gray-50 text-gray-600\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1634,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 1581,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1575,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n        lineNumber: 454,\n        columnNumber: 5\n    }, this);\n}\n_s(ShipmentReception, \"VXikbBX57EuFTz5bPw9OPhzTG9I=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ShipmentReception;\nvar _c;\n$RefreshReg$(_c, \"ShipmentReception\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/shipment-reception.tsx\n"));

/***/ })

});