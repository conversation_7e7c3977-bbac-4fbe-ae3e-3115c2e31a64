import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/layout/Layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Calculator as CalculatorIcon, Save, FileText } from 'lucide-react'
import { calculateLandedCost, formatCurrency, formatExchangeRate } from '@/lib/calculations'

interface CalculatorForm {
  // General Information
  supplierName: string
  orderNumber: string
  orderDate: string
  invoiceNumber: string
  invoiceDate: string
  currency: 'USD' | 'EUR' | 'CNY'
  exchangeRate: number

  // Goods Price
  fobAmount: number
  freight: number

  // Customs Duties
  customsDuties1Ttc: number
  customsDuties1Tva: number
  customsDuties2Ttc: number // Other Customs Duties (HT) - T.E.L, A.M.D, etc.
  customsDuties2Tva: number // Maintenu pour compatibilité backend (toujours 0)

  // Port Fees
  importDeliveryTtc: number
  importDeliveryTva: number
  customsInspectionTtc: number
  customsInspectionTva: number

  // Shipping Fees
  shippingAgencyTtc: number
  shippingAgencyTva: number
  emptyContainersTtc: number
  emptyContainersTva: number
  demurrageHt: number

  // Other Expenses
  miscExpensesTtc: number
  miscExpensesTva: number
  transitExpensesTtc: number
  transitExpensesTva: number
}

const defaultForm: CalculatorForm = {
  supplierName: '',
  orderNumber: '',
  orderDate: '',
  invoiceNumber: '',
  invoiceDate: '',
  currency: 'USD',
  exchangeRate: 134.50000,
  fobAmount: 0,
  freight: 0,
  customsDuties1Ttc: 0,
  customsDuties1Tva: 0,
  customsDuties2Ttc: 0,
  customsDuties2Tva: 0,
  importDeliveryTtc: 0,
  importDeliveryTva: 0,
  customsInspectionTtc: 0,
  customsInspectionTva: 0,
  shippingAgencyTtc: 0,
  shippingAgencyTva: 0,
  emptyContainersTtc: 0,
  emptyContainersTva: 0,
  demurrageHt: 0,
  miscExpensesTtc: 0,
  miscExpensesTva: 0,
  transitExpensesTtc: 0,
  transitExpensesTva: 0
}

export default function Calculator() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [form, setForm] = useState<CalculatorForm>(defaultForm)
  const [calculation, setCalculation] = useState<any>(null)
  const [isCalculating, setIsCalculating] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const handleInputChange = (field: keyof CalculatorForm, value: string | number) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const calculateCosts = () => {
    setIsCalculating(true)

    // Simulate calculation delay
    setTimeout(() => {
      // Create mock shipment data
      const shipment = {
        fob_amount: form.fobAmount,
        freight: form.freight,
        exchange_rate_used: form.exchangeRate,
        fob_amount_dzd: form.fobAmount * form.exchangeRate,
        freight_dzd: form.freight * form.exchangeRate,
        total_cif_dzd: (form.fobAmount + form.freight) * form.exchangeRate
      }

      // Create mock cost data
      const customsDuties = {
        customs_duties1_ttc: form.customsDuties1Ttc,
        customs_duties1_tva: form.customsDuties1Tva,
        customs_duties2_ttc: form.customsDuties2Ttc, // Other Customs Duties (HT) - montant HT direct
        customs_duties2_tva: 0, // Toujours 0 car customsDuties2Ttc est maintenant un montant HT
        total_ttc: form.customsDuties1Ttc + form.customsDuties2Ttc, // Note: customsDuties2Ttc est HT, pas TTC
        total_tva: form.customsDuties1Tva + 0, // Pas de TVA sur les autres droits
        total_ht: (form.customsDuties1Ttc - form.customsDuties1Tva) + form.customsDuties2Ttc // customsDuties2Ttc est déjà HT
      }

      const portFees = {
        import_delivery_ttc: form.importDeliveryTtc,
        import_delivery_tva: form.importDeliveryTva,
        import_delivery_ht: form.importDeliveryTtc - form.importDeliveryTva,
        customs_inspection_ttc: form.customsInspectionTtc,
        customs_inspection_tva: form.customsInspectionTva,
        customs_inspection_ht: form.customsInspectionTtc - form.customsInspectionTva
      }

      const shippingFees = {
        shipping_agency_ttc: form.shippingAgencyTtc,
        shipping_agency_tva: form.shippingAgencyTva,
        shipping_agency_ht: form.shippingAgencyTtc - form.shippingAgencyTva,
        empty_containers_ttc: form.emptyContainersTtc,
        empty_containers_tva: form.emptyContainersTva,
        empty_containers_ht: form.emptyContainersTtc - form.emptyContainersTva,
        demurrage_ht: form.demurrageHt
      }

      const miscExpenses = {
        amount_ttc: form.miscExpensesTtc,
        amount_tva: form.miscExpensesTva,
        amount_ht: form.miscExpensesTtc - form.miscExpensesTva
      }

      const transitExpenses = {
        amount_ttc: form.transitExpensesTtc,
        amount_tva: form.transitExpensesTva,
        amount_ht: form.transitExpensesTtc - form.transitExpensesTva
      }

      const result = calculateLandedCost(
        shipment as any,
        customsDuties as any,
        portFees as any,
        shippingFees as any,
        miscExpenses as any,
        transitExpenses as any
      )

      setCalculation(result)
      setIsCalculating(false)
    }, 1000)
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Cost Calculator</h1>
            <p className="text-gray-600">Calculate landed costs for your imports</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button onClick={calculateCosts} disabled={isCalculating}>
              <CalculatorIcon className="h-4 w-4 mr-2" />
              {isCalculating ? 'Calculating...' : 'Calculate'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Input Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* General Information */}
            <Card>
              <CardHeader>
                <CardTitle>General Information</CardTitle>
                <CardDescription>Basic shipment details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Supplier Name *
                    </label>
                    <Input
                      value={form.supplierName}
                      onChange={(e) => handleInputChange('supplierName', e.target.value)}
                      placeholder="Enter supplier name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Order Number *
                    </label>
                    <Input
                      value={form.orderNumber}
                      onChange={(e) => handleInputChange('orderNumber', e.target.value)}
                      placeholder="Enter order number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Order Date
                    </label>
                    <Input
                      type="date"
                      value={form.orderDate}
                      onChange={(e) => handleInputChange('orderDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Number
                    </label>
                    <Input
                      value={form.invoiceNumber}
                      onChange={(e) => handleInputChange('invoiceNumber', e.target.value)}
                      placeholder="Enter invoice number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={form.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value as 'USD' | 'EUR' | 'CNY')}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="CNY">CNY</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exchange Rate (to DZD) *
                    </label>
                    <Input
                      type="number"
                      step="0.00001"
                      value={form.exchangeRate}
                      onChange={(e) => handleInputChange('exchangeRate', parseFloat(e.target.value) || 0)}
                      placeholder="134.50000"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Goods Price */}
            <Card>
              <CardHeader>
                <CardTitle>Goods Price ({form.currency})</CardTitle>
                <CardDescription>FOB and freight costs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      FOB Amount *
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={form.fobAmount}
                      onChange={(e) => handleInputChange('fobAmount', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Freight
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={form.freight}
                      onChange={(e) => handleInputChange('freight', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="flex justify-between text-sm">
                    <span>Total CIF ({form.currency}):</span>
                    <span className="font-medium">{formatCurrency(form.fobAmount + form.freight)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total CIF (DZD):</span>
                    <span className="font-medium">{formatCurrency((form.fobAmount + form.freight) * form.exchangeRate)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customs Duties */}
            <Card>
              <CardHeader>
                <CardTitle>Customs Duties (DZD)</CardTitle>
                <CardDescription>Algeria customs fees</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Quittance 1</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties1Ttc}
                          onChange={(e) => handleInputChange('customsDuties1Ttc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties1Tva}
                          onChange={(e) => handleInputChange('customsDuties1Tva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties1Ttc - form.customsDuties1Tva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Quittance 2</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Other Customs Duties (HT) "T.E.L , A.M.D, ....."
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties2Ttc}
                          onChange={(e) => handleInputChange('customsDuties2Ttc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Overall Totals */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-3">OVERALL TOTALS OF CUSTOMS DUTIES:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties1Ttc}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsDuties1Tva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total HT
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={(form.customsDuties1Ttc - form.customsDuties1Tva) + form.customsDuties2Ttc}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Port Fees */}
            <Card>
              <CardHeader>
                <CardTitle>Port Fees (DZD)</CardTitle>
                <CardDescription>Port and customs inspection fees</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Import Delivery</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.importDeliveryTtc}
                          onChange={(e) => handleInputChange('importDeliveryTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.importDeliveryTva}
                          onChange={(e) => handleInputChange('importDeliveryTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.importDeliveryTtc - form.importDeliveryTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Customs Inspection</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsInspectionTtc}
                          onChange={(e) => handleInputChange('customsInspectionTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsInspectionTva}
                          onChange={(e) => handleInputChange('customsInspectionTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.customsInspectionTtc - form.customsInspectionTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Overall Totals Port Fees */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-3">OVERALL TOTALS OF PORT FEES:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.importDeliveryTtc + form.customsInspectionTtc}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.importDeliveryTva + form.customsInspectionTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total HT
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={(form.importDeliveryTtc - form.importDeliveryTva) + (form.customsInspectionTtc - form.customsInspectionTva)}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Fees */}
            <Card>
              <CardHeader>
                <CardTitle>Shipping Fees (DZD)</CardTitle>
                <CardDescription>Shipping company and container fees</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Shipping Agency Services</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.shippingAgencyTtc}
                          onChange={(e) => handleInputChange('shippingAgencyTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.shippingAgencyTva}
                          onChange={(e) => handleInputChange('shippingAgencyTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.shippingAgencyTtc - form.shippingAgencyTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Empty Containers Return</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.emptyContainersTtc}
                          onChange={(e) => handleInputChange('emptyContainersTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.emptyContainersTva}
                          onChange={(e) => handleInputChange('emptyContainersTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.emptyContainersTtc - form.emptyContainersTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Demurrage (if present)</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Demurrage HT (manually entered)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.demurrageHt}
                          onChange={(e) => handleInputChange('demurrageHt', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Overall Totals Shipping Fees */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-3">OVERALL TOTALS OF SHIPPING FEES:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.shippingAgencyTva + form.emptyContainersTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total HT
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={(form.shippingAgencyTtc - form.shippingAgencyTva) + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Miscellaneous Expenses */}
            <Card>
              <CardHeader>
                <CardTitle>Other Miscellaneous Expenses (DZD)</CardTitle>
                <CardDescription>Various additional costs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Other Miscellaneous Expenses</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.miscExpensesTtc}
                          onChange={(e) => handleInputChange('miscExpensesTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.miscExpensesTva}
                          onChange={(e) => handleInputChange('miscExpensesTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.miscExpensesTtc - form.miscExpensesTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Transit Services Expenses */}
            <Card>
              <CardHeader>
                <CardTitle>Transit Services Expenses (DZD)</CardTitle>
                <CardDescription>Transit and logistics services</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Transit Services Expenses</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total TTC
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.transitExpensesTtc}
                          onChange={(e) => handleInputChange('transitExpensesTtc', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          TVA
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.transitExpensesTva}
                          onChange={(e) => handleInputChange('transitExpensesTva', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          HT (TTC - TVA)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={form.transitExpensesTtc - form.transitExpensesTva}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Calculation Results</CardTitle>
                <CardDescription>
                  {calculation ? 'Landed cost breakdown' : 'Enter values and click Calculate'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {calculation ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>FOB Amount (DZD):</span>
                        <span>{formatCurrency(calculation.fobAmountDzd)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Freight (DZD):</span>
                        <span>{formatCurrency(calculation.freightDzd)}</span>
                      </div>
                      <div className="flex justify-between text-sm font-medium border-t pt-2">
                        <span>Total CIF (DZD):</span>
                        <span>{formatCurrency(calculation.totalCifDzd)}</span>
                      </div>
                    </div>

                    <div className="space-y-2 border-t pt-4">
                      <h4 className="font-medium text-gray-900">Cost Components (HT)</h4>
                      <div className="flex justify-between text-sm">
                        <span>Customs Duties:</span>
                        <span>{formatCurrency(calculation.customsDutiesHt)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Port Fees:</span>
                        <span>{formatCurrency(calculation.portFeesHt)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Shipping Fees:</span>
                        <span>{formatCurrency(calculation.shippingFeesHt)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Misc. Expenses:</span>
                        <span>{formatCurrency(calculation.miscExpensesHt)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Transit Expenses:</span>
                        <span>{formatCurrency(calculation.transitExpensesHt)}</span>
                      </div>
                    </div>

                    <div className="space-y-2 border-t pt-4">
                      <div className="flex justify-between text-lg font-bold text-primary">
                        <span>Landed Cost (HT):</span>
                        <span>{formatCurrency(calculation.landedCostHt)}</span>
                      </div>
                      <div className="flex justify-between text-sm font-medium">
                        <span>Coefficient:</span>
                        <span>{calculation.landedCostCoefficient.toFixed(5)}x</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Total Paid (TTC):</span>
                        <span>{formatCurrency(calculation.totalPaidTtc)}</span>
                      </div>
                    </div>

                    <div className="pt-4">
                      <Button className="w-full" variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalculatorIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Enter your shipment details</p>
                    <p className="text-sm text-gray-500">Fill in the form and click Calculate to see results</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  )
}
