import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/layout/Layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Calculator as CalculatorIcon, Search, Save, FileText, CheckCircle, AlertCircle, Eye, Edit, Trash2, RefreshCw, Download } from 'lucide-react'

interface ReceptionData {
  id: string
  supplierName: string
  orderNumber: string
  invoiceNumber: string
  exchangeRate: number
  currency: string

  // Goods Price
  fobAmount: number
  freight: number

  // Customs Duties
  customsDuties1Ttc: number
  customsDuties1Tva: number
  customsDuties1Ht: number
  customsDuties2Ht: number

  // Port Fees
  importDeliveryTtc: number
  importDeliveryTva: number
  importDeliveryHt: number
  customsInspectionTtc: number
  customsInspectionTva: number
  customsInspectionHt: number

  // Shipping Fees
  shippingAgencyTtc: number
  shippingAgencyTva: number
  shippingAgencyHt: number
  emptyContainersTtc: number
  emptyContainersTva: number
  emptyContainersHt: number
  demurrageHt: number

  // Misc Expenses
  miscExpensesTtc: number
  miscExpensesTva: number
  miscExpensesHt: number

  // Transit Services
  transitServicesTtc: number
  transitServicesTva: number
  transitServicesHt: number
}

interface GeneratedCosts {
  id: string
  invoiceNumber: string
  receptionId: string
  landedCostTtc: number
  landedCostHt: number
  landedCostCoefficient: number
  totalCustomsDuties: number
  totalPortFees: number
  totalShippingFees: number
  totalMiscExpenses: number
  totalTransitServices: number
  cifDzd: number
  fobDzd: number
  exchangeRateUsed: number
  generatedAt: string
}

export default function Calculator() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [invoiceNumber, setInvoiceNumber] = useState('')
  const [receptionData, setReceptionData] = useState<ReceptionData | null>(null)
  const [generatedCosts, setGeneratedCosts] = useState<GeneratedCosts | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState('')
  const [allReceptions, setAllReceptions] = useState<ReceptionData[]>([])
  const [showAllReceptions, setShowAllReceptions] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Charger toutes les réceptions au démarrage
  useEffect(() => {
    loadAllReceptions()
  }, [])

  const loadAllReceptions = () => {
    try {
      const savedReceptions = JSON.parse(localStorage.getItem('shipment-receptions') || '[]')
      const mappedReceptions = savedReceptions.map((reception: any) => ({
        id: reception.id,
        supplierName: reception.supplierName,
        orderNumber: reception.orderNumber,
        invoiceNumber: reception.invoiceNumber,
        exchangeRate: reception.exchangeRate,
        currency: reception.currency,

        fobAmount: reception.fobAmount,
        freight: reception.freight,

        customsDuties1Ttc: reception.customsDuties1Ttc,
        customsDuties1Tva: reception.customsDuties1Tva,
        customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,
        customsDuties2Ht: reception.customsDuties2Ht,

        importDeliveryTtc: reception.importDeliveryTtc,
        importDeliveryTva: reception.importDeliveryTva,
        importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,
        customsInspectionTtc: reception.customsInspectionTtc,
        customsInspectionTva: reception.customsInspectionTva,
        customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,

        shippingAgencyTtc: reception.shippingAgencyTtc,
        shippingAgencyTva: reception.shippingAgencyTva,
        shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,
        emptyContainersTtc: reception.emptyContainersTtc,
        emptyContainersTva: reception.emptyContainersTva,
        emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,
        demurrageHt: reception.demurrageHt,

        miscExpensesTtc: reception.miscExpensesTtc,
        miscExpensesTva: reception.miscExpensesTva,
        miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,

        transitServicesTtc: reception.transitServicesTtc,
        transitServicesTva: reception.transitServicesTva,
        transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva,

        createdAt: reception.createdAt
      }))
      setAllReceptions(mappedReceptions)
    } catch (error) {
      console.error('Error loading receptions:', error)
    }
  }

  const searchReception = async () => {
    if (!invoiceNumber.trim()) {
      setError('Veuillez saisir un numéro de facture')
      return
    }

    setIsLoading(true)
    setError('')
    setReceptionData(null)

    try {
      // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)
      const savedReceptions = JSON.parse(localStorage.getItem('shipment-receptions') || '[]')
      const reception = savedReceptions.find((r: any) => r.invoiceNumber === invoiceNumber.trim())

      if (!reception) {
        setError(`Aucune réception trouvée pour la facture: ${invoiceNumber}`)
        setIsLoading(false)
        return
      }

      // Mapper les données de réception vers notre interface
      const mappedReception: ReceptionData = {
        id: reception.id,
        supplierName: reception.supplierName,
        orderNumber: reception.orderNumber,
        invoiceNumber: reception.invoiceNumber,
        exchangeRate: reception.exchangeRate,
        currency: reception.currency,

        fobAmount: reception.fobAmount,
        freight: reception.freight,

        customsDuties1Ttc: reception.customsDuties1Ttc,
        customsDuties1Tva: reception.customsDuties1Tva,
        customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,
        customsDuties2Ht: reception.customsDuties2Ht,

        importDeliveryTtc: reception.importDeliveryTtc,
        importDeliveryTva: reception.importDeliveryTva,
        importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,
        customsInspectionTtc: reception.customsInspectionTtc,
        customsInspectionTva: reception.customsInspectionTva,
        customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,

        shippingAgencyTtc: reception.shippingAgencyTtc,
        shippingAgencyTva: reception.shippingAgencyTva,
        shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,
        emptyContainersTtc: reception.emptyContainersTtc,
        emptyContainersTva: reception.emptyContainersTva,
        emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,
        demurrageHt: reception.demurrageHt,

        miscExpensesTtc: reception.miscExpensesTtc,
        miscExpensesTva: reception.miscExpensesTva,
        miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,

        transitServicesTtc: reception.transitServicesTtc,
        transitServicesTva: reception.transitServicesTva,
        transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva
      }

      setReceptionData(mappedReception)

    } catch (error) {
      console.error('Error searching reception:', error)
      setError('Erreur lors de la recherche de la réception')
    } finally {
      setIsLoading(false)
    }
  }

  const generateCosts = async () => {
    if (!receptionData) return

    setIsGenerating(true)

    try {
      // Calculer les coûts
      const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate
      const fobDzd = receptionData.fobAmount * receptionData.exchangeRate

      const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht
      const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt
      const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt
      const totalMiscExpenses = receptionData.miscExpensesHt
      const totalTransitServices = receptionData.transitServicesHt

      const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices

      // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)
      const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0

      // Pour TTC, on ajoute les TVA
      const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva +
                      receptionData.shippingAgencyTva + receptionData.emptyContainersTva +
                      receptionData.miscExpensesTva + receptionData.transitServicesTva

      const landedCostTtc = landedCostHt + totalTva

      const costs: GeneratedCosts = {
        id: Date.now().toString(),
        invoiceNumber: receptionData.invoiceNumber,
        receptionId: receptionData.id,
        landedCostTtc,
        landedCostHt,
        landedCostCoefficient,
        totalCustomsDuties,
        totalPortFees,
        totalShippingFees,
        totalMiscExpenses,
        totalTransitServices,
        cifDzd,
        fobDzd,
        exchangeRateUsed: receptionData.exchangeRate,
        generatedAt: new Date().toISOString()
      }

      setGeneratedCosts(costs)

    } catch (error) {
      console.error('Error generating costs:', error)
      setError('Erreur lors de la génération des coûts')
    } finally {
      setIsGenerating(false)
    }
  }

  const saveCosts = async () => {
    if (!generatedCosts) return

    try {
      // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)
      const savedCosts = JSON.parse(localStorage.getItem('generated-costs') || '[]')
      savedCosts.push(generatedCosts)
      localStorage.setItem('generated-costs', JSON.stringify(savedCosts))

      alert(`Coûts sauvegardés avec succès pour la facture: ${generatedCosts.invoiceNumber}`)

    } catch (error) {
      console.error('Error saving costs:', error)
      alert('Erreur lors de la sauvegarde des coûts')
    }
  }

  const viewReceptionDetails = (reception: ReceptionData) => {
    setReceptionData(reception)
    setInvoiceNumber(reception.invoiceNumber)
    setShowAllReceptions(false)
    setError('')
  }

  const editReception = (reception: ReceptionData) => {
    // Rediriger vers la page de modification avec l'ID
    router.push(`/shipment-reception?edit=${reception.id}`)
  }

  const deleteReception = (reception: ReceptionData) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la réception "${reception.supplierName} - ${reception.orderNumber}" ?`)) {
      try {
        const savedReceptions = JSON.parse(localStorage.getItem('shipment-receptions') || '[]')
        const updatedReceptions = savedReceptions.filter((r: any) => r.id !== reception.id)
        localStorage.setItem('shipment-receptions', JSON.stringify(updatedReceptions))

        // Recharger la liste
        loadAllReceptions()

        // Si c'était la réception actuellement affichée, la réinitialiser
        if (receptionData?.id === reception.id) {
          setReceptionData(null)
          setGeneratedCosts(null)
          setInvoiceNumber('')
        }

        alert(`Réception supprimée avec succès`)

      } catch (error) {
        console.error('Error deleting reception:', error)
        alert('Erreur lors de la suppression de la réception')
      }
    }
  }

  const backToList = () => {
    setShowAllReceptions(true)
    setReceptionData(null)
    setGeneratedCosts(null)
    setInvoiceNumber('')
    setError('')
  }

  const exportSingleReception = (reception: ReceptionData) => {
    try {
      // Calculer les totaux pour cette réception
      const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate
      const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht
      const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt
      const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt
      const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt
      const estimatedLandedCost = cifDzd + totalAllFees

      // Créer un rapport détaillé pour cette réception
      const reportContent = [
        '=== SHIPMENT RECEPTION DETAILED REPORT ===',
        '',
        '--- GENERAL INFORMATION ---',
        `Supplier Name: ${reception.supplierName}`,
        `Order Number: ${reception.orderNumber}`,
        `Invoice Number: ${reception.invoiceNumber}`,
        `Currency: ${reception.currency}`,
        `Exchange Rate: ${reception.exchangeRate.toFixed(5)}`,
        `Created At: ${new Date(reception.createdAt || Date.now()).toLocaleDateString('fr-FR')}`,
        '',
        '--- GOODS PRICE ---',
        `FOB Amount (${reception.currency}): ${reception.fobAmount.toFixed(2)}`,
        `Freight (${reception.currency}): ${reception.freight.toFixed(2)}`,
        `CIF Amount (DZD): ${cifDzd.toFixed(2)}`,
        '',
        '--- CUSTOMS DUTIES ---',
        `Customs Duties 1 TTC (DZD): ${reception.customsDuties1Ttc.toFixed(2)}`,
        `Customs Duties 1 TVA (DZD): ${reception.customsDuties1Tva.toFixed(2)}`,
        `Customs Duties 1 HT (DZD): ${reception.customsDuties1Ht.toFixed(2)}`,
        `Customs Duties 2 HT (DZD): ${reception.customsDuties2Ht.toFixed(2)}`,
        `TOTAL CUSTOMS DUTIES HT: ${totalCustomsDuties.toFixed(2)} DZD`,
        '',
        '--- PORT FEES ---',
        `Import Delivery TTC (DZD): ${reception.importDeliveryTtc.toFixed(2)}`,
        `Import Delivery TVA (DZD): ${reception.importDeliveryTva.toFixed(2)}`,
        `Import Delivery HT (DZD): ${reception.importDeliveryHt.toFixed(2)}`,
        `Customs Inspection TTC (DZD): ${reception.customsInspectionTtc.toFixed(2)}`,
        `Customs Inspection TVA (DZD): ${reception.customsInspectionTva.toFixed(2)}`,
        `Customs Inspection HT (DZD): ${reception.customsInspectionHt.toFixed(2)}`,
        `TOTAL PORT FEES HT: ${totalPortFees.toFixed(2)} DZD`,
        '',
        '--- SHIPPING COMPANY FEES ---',
        `Shipping Agency TTC (DZD): ${reception.shippingAgencyTtc.toFixed(2)}`,
        `Shipping Agency TVA (DZD): ${reception.shippingAgencyTva.toFixed(2)}`,
        `Shipping Agency HT (DZD): ${reception.shippingAgencyHt.toFixed(2)}`,
        `Empty Containers TTC (DZD): ${reception.emptyContainersTtc.toFixed(2)}`,
        `Empty Containers TVA (DZD): ${reception.emptyContainersTva.toFixed(2)}`,
        `Empty Containers HT (DZD): ${reception.emptyContainersHt.toFixed(2)}`,
        `Demurrage HT (DZD): ${reception.demurrageHt.toFixed(2)}`,
        `TOTAL SHIPPING FEES HT: ${totalShippingFees.toFixed(2)} DZD`,
        '',
        '--- MISCELLANEOUS EXPENSES ---',
        `Misc Expenses TTC (DZD): ${reception.miscExpensesTtc.toFixed(2)}`,
        `Misc Expenses TVA (DZD): ${reception.miscExpensesTva.toFixed(2)}`,
        `Misc Expenses HT (DZD): ${reception.miscExpensesHt.toFixed(2)}`,
        '',
        '--- TRANSIT SERVICES ---',
        `Transit Services TTC (DZD): ${reception.transitServicesTtc.toFixed(2)}`,
        `Transit Services TVA (DZD): ${reception.transitServicesTva.toFixed(2)}`,
        `Transit Services HT (DZD): ${reception.transitServicesHt.toFixed(2)}`,
        '',
        '=== SUMMARY ===',
        `CIF Amount (DZD): ${cifDzd.toFixed(2)}`,
        `Total All Fees HT (DZD): ${totalAllFees.toFixed(2)}`,
        `ESTIMATED LANDED COST HT: ${estimatedLandedCost.toFixed(2)} DZD`,
        `ESTIMATED LANDED COST COEFFICIENT: ${(estimatedLandedCost / (reception.fobAmount * reception.exchangeRate)).toFixed(4)}`,
        '',
        '--- BREAKDOWN ---',
        `FOB (DZD): ${(reception.fobAmount * reception.exchangeRate).toFixed(2)}`,
        `Freight (DZD): ${(reception.freight * reception.exchangeRate).toFixed(2)}`,
        `Customs Duties: ${totalCustomsDuties.toFixed(2)}`,
        `Port Fees: ${totalPortFees.toFixed(2)}`,
        `Shipping Fees: ${totalShippingFees.toFixed(2)}`,
        `Misc Expenses: ${reception.miscExpensesHt.toFixed(2)}`,
        `Transit Services: ${reception.transitServicesHt.toFixed(2)}`,
        '',
        '=== END OF REPORT ==='
      ].join('\n')

      // Créer et télécharger le fichier
      const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `reception_${reception.supplierName}_${reception.orderNumber}_${new Date().toISOString().split('T')[0]}.txt`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      alert(`Export réussi pour la réception: ${reception.supplierName} - ${reception.orderNumber}`)

    } catch (error) {
      console.error('Error exporting single reception:', error)
      alert('Erreur lors de l\'export de la réception')
    }
  }

  const exportAllReceptions = () => {
    if (allReceptions.length === 0) {
      alert('Aucune réception à exporter')
      return
    }

    try {
      // Créer les en-têtes du fichier Excel
      const headers = [
        'ID',
        'Supplier Name',
        'Order Number',
        'Invoice Number',
        'Currency',
        'Exchange Rate',
        'FOB Amount',
        'Freight',
        'CIF Amount (DZD)',
        'Customs Duties 1 TTC',
        'Customs Duties 1 TVA',
        'Customs Duties 1 HT',
        'Customs Duties 2 HT',
        'Total Customs Duties HT',
        'Import Delivery TTC',
        'Import Delivery TVA',
        'Import Delivery HT',
        'Customs Inspection TTC',
        'Customs Inspection TVA',
        'Customs Inspection HT',
        'Total Port Fees HT',
        'Shipping Agency TTC',
        'Shipping Agency TVA',
        'Shipping Agency HT',
        'Empty Containers TTC',
        'Empty Containers TVA',
        'Empty Containers HT',
        'Demurrage HT',
        'Total Shipping Fees HT',
        'Misc Expenses TTC',
        'Misc Expenses TVA',
        'Misc Expenses HT',
        'Transit Services TTC',
        'Transit Services TVA',
        'Transit Services HT',
        'Total All Fees HT',
        'Estimated Landed Cost HT',
        'Created At'
      ]

      // Créer les données pour chaque réception
      const csvData = allReceptions.map(reception => {
        const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate
        const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht
        const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt
        const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt
        const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt
        const estimatedLandedCost = cifDzd + totalAllFees

        return [
          reception.id,
          reception.supplierName,
          reception.orderNumber,
          reception.invoiceNumber,
          reception.currency,
          reception.exchangeRate.toFixed(5),
          reception.fobAmount.toFixed(2),
          reception.freight.toFixed(2),
          cifDzd.toFixed(2),
          reception.customsDuties1Ttc.toFixed(2),
          reception.customsDuties1Tva.toFixed(2),
          reception.customsDuties1Ht.toFixed(2),
          reception.customsDuties2Ht.toFixed(2),
          totalCustomsDuties.toFixed(2),
          reception.importDeliveryTtc.toFixed(2),
          reception.importDeliveryTva.toFixed(2),
          reception.importDeliveryHt.toFixed(2),
          reception.customsInspectionTtc.toFixed(2),
          reception.customsInspectionTva.toFixed(2),
          reception.customsInspectionHt.toFixed(2),
          totalPortFees.toFixed(2),
          reception.shippingAgencyTtc.toFixed(2),
          reception.shippingAgencyTva.toFixed(2),
          reception.shippingAgencyHt.toFixed(2),
          reception.emptyContainersTtc.toFixed(2),
          reception.emptyContainersTva.toFixed(2),
          reception.emptyContainersHt.toFixed(2),
          reception.demurrageHt.toFixed(2),
          totalShippingFees.toFixed(2),
          reception.miscExpensesTtc.toFixed(2),
          reception.miscExpensesTva.toFixed(2),
          reception.miscExpensesHt.toFixed(2),
          reception.transitServicesTtc.toFixed(2),
          reception.transitServicesTva.toFixed(2),
          reception.transitServicesHt.toFixed(2),
          totalAllFees.toFixed(2),
          estimatedLandedCost.toFixed(2),
          new Date(reception.createdAt || Date.now()).toLocaleDateString('fr-FR')
        ]
      })

      // Créer le contenu CSV
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => row.map(cell =>
          typeof cell === 'string' && cell.includes(',') ? `"${cell}"` : cell
        ).join(','))
      ].join('\n')

      // Créer et télécharger le fichier
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `shipment_receptions_export_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      alert(`Export réussi ! ${allReceptions.length} réceptions exportées.`)

    } catch (error) {
      console.error('Error exporting receptions:', error)
      alert('Erreur lors de l\'export des réceptions')
    }
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Cost Calculator</h1>
            <p className="text-gray-600">Generate costs from saved shipment receptions</p>
          </div>
        </div>

        {/* All Receptions Table */}
        {showAllReceptions && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  All Shipment Receptions
                </div>
                <div className="flex gap-2">
                  <Button onClick={exportAllReceptions} variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button onClick={loadAllReceptions} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Complete list of all recorded shipment receptions with actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {allReceptions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No shipment receptions found</p>
                  <p className="text-sm">Create a new reception in Shipment Reception page</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-4 py-2 text-left">Supplier Name</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Order Number</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Invoice Number</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Currency</th>
                        <th className="border border-gray-300 px-4 py-2 text-right">FOB Amount</th>
                        <th className="border border-gray-300 px-4 py-2 text-right">Exchange Rate</th>
                        <th className="border border-gray-300 px-4 py-2 text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allReceptions.map((reception) => (
                        <tr key={reception.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2 font-medium">
                            {reception.supplierName}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {reception.orderNumber}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 font-mono text-sm">
                            {reception.invoiceNumber}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {reception.currency}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-right">
                            {reception.fobAmount.toFixed(2)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-right">
                            {reception.exchangeRate.toFixed(5)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            <div className="flex justify-center gap-1">
                              <Button
                                onClick={() => viewReceptionDetails(reception)}
                                size="sm"
                                variant="outline"
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                onClick={() => editReception(reception)}
                                size="sm"
                                variant="outline"
                                title="Edit Reception"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                onClick={() => exportSingleReception(reception)}
                                size="sm"
                                variant="outline"
                                className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                title="Export Reception"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                onClick={() => deleteReception(reception)}
                                size="sm"
                                variant="outline"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="Delete Reception"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Search Section */}
        {!showAllReceptions && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Search Reception by Invoice Number
                </div>
                <Button onClick={backToList} variant="outline" size="sm">
                  ← Back to List
                </Button>
              </CardTitle>
              <CardDescription>Enter the invoice number to retrieve reception data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    value={invoiceNumber}
                    onChange={(e) => setInvoiceNumber(e.target.value)}
                    placeholder="Enter invoice number..."
                    onKeyPress={(e) => e.key === 'Enter' && searchReception()}
                  />
                </div>
                <Button onClick={searchReception} disabled={isLoading}>
                  <Search className="h-4 w-4 mr-2" />
                  {isLoading ? 'Searching...' : 'Search'}
                </Button>
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md">
                  <AlertCircle className="h-4 w-4" />
                  <span>{error}</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Reception Data Display */}
        {receptionData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Reception Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Reception Found
                </CardTitle>
                <CardDescription>Reception data for invoice {receptionData.invoiceNumber}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Supplier:</span>
                    <p>{receptionData.supplierName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Order Number:</span>
                    <p>{receptionData.orderNumber}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Currency:</span>
                    <p>{receptionData.currency}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Exchange Rate:</span>
                    <p>{receptionData.exchangeRate.toFixed(5)}</p>
                  </div>
                </div>

                {/* Goods Price */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Goods Price ({receptionData.currency})</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>FOB Amount: {receptionData.fobAmount.toFixed(2)}</div>
                    <div>Freight: {receptionData.freight.toFixed(2)}</div>
                    <div className="col-span-2 font-medium">
                      CIF DZD: {((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* All Fees Summary */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Fees Summary (DZD HT)</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Customs Duties:</span>
                      <span>{(receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Port Fees:</span>
                      <span>{(receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shipping Fees:</span>
                      <span>{(receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Misc Expenses:</span>
                      <span>{receptionData.miscExpensesHt.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Transit Services:</span>
                      <span>{receptionData.transitServicesHt.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="border-t pt-4">
                  <Button
                    onClick={generateCosts}
                    disabled={isGenerating}
                    className="w-full"
                  >
                    <CalculatorIcon className="h-4 w-4 mr-2" />
                    {isGenerating ? 'Generating Costs...' : 'Generate Costs'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Generated Costs Display */}
            {generatedCosts && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Generated Costs
                  </CardTitle>
                  <CardDescription>Calculated landed costs for invoice {generatedCosts.invoiceNumber}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Main Results */}
                  <div className="bg-green-50 p-4 rounded-md">
                    <h4 className="font-bold text-green-900 mb-3">LANDED COST RESULTS:</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-lg font-bold">
                        <span>Landed Cost HT:</span>
                        <span className="text-green-800">{generatedCosts.landedCostHt.toFixed(2)} DZD</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold">
                        <span>Landed Cost TTC:</span>
                        <span className="text-green-800">{generatedCosts.landedCostTtc.toFixed(2)} DZD</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold border-t pt-2">
                        <span>Landed Cost Coefficient:</span>
                        <span className="text-blue-800">{generatedCosts.landedCostCoefficient.toFixed(4)}</span>
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        Coefficient = Landed Cost HT / FOB (DZD) = {generatedCosts.landedCostHt.toFixed(2)} / {generatedCosts.fobDzd.toFixed(2)}
                      </div>
                    </div>
                  </div>

                  {/* Breakdown */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Cost Breakdown (DZD HT)</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between font-medium text-blue-700">
                        <span>FOB Amount (DZD):</span>
                        <span>{generatedCosts.fobDzd.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>CIF Amount:</span>
                        <span>{generatedCosts.cifDzd.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Customs Duties:</span>
                        <span>{generatedCosts.totalCustomsDuties.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Port Fees:</span>
                        <span>{generatedCosts.totalPortFees.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Shipping Fees:</span>
                        <span>{generatedCosts.totalShippingFees.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Misc Expenses:</span>
                        <span>{generatedCosts.totalMiscExpenses.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Transit Services:</span>
                        <span>{generatedCosts.totalTransitServices.toFixed(2)}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-bold">
                        <span>TOTAL LANDED COST HT:</span>
                        <span>{generatedCosts.landedCostHt.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="border-t pt-4">
                    <Button
                      onClick={saveCosts}
                      className="w-full"
                      variant="outline"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Generated Costs
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </Layout>
  )
}
