"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = ()=>{\n        try {\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplierName,\n                    orderNumber: reception.orderNumber,\n                    invoiceNumber: reception.invoiceNumber,\n                    exchangeRate: reception.exchangeRate,\n                    currency: reception.currency,\n                    fobAmount: reception.fobAmount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customsDuties1Ttc,\n                    customsDuties1Tva: reception.customsDuties1Tva,\n                    customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                    customsDuties2Ht: reception.customsDuties2Ht,\n                    importDeliveryTtc: reception.importDeliveryTtc,\n                    importDeliveryTva: reception.importDeliveryTva,\n                    importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                    customsInspectionTtc: reception.customsInspectionTtc,\n                    customsInspectionTva: reception.customsInspectionTva,\n                    customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                    shippingAgencyTtc: reception.shippingAgencyTtc,\n                    shippingAgencyTva: reception.shippingAgencyTva,\n                    shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                    emptyContainersTtc: reception.emptyContainersTtc,\n                    emptyContainersTva: reception.emptyContainersTva,\n                    emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                    demurrageHt: reception.demurrageHt,\n                    miscExpensesTtc: reception.miscExpensesTtc,\n                    miscExpensesTva: reception.miscExpensesTva,\n                    miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                    transitServicesTtc: reception.transitServicesTtc,\n                    transitServicesTva: reception.transitServicesTva,\n                    transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva,\n                    createdAt: reception.createdAt\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const reception = savedReceptions.find((r)=>r.invoiceNumber === invoiceNumber.trim());\n            if (!reception) {\n                setError(\"Aucune r\\xe9ception trouv\\xe9e pour la facture: \".concat(invoiceNumber));\n                setIsLoading(false);\n                return;\n            }\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplierName,\n                orderNumber: reception.orderNumber,\n                invoiceNumber: reception.invoiceNumber,\n                exchangeRate: reception.exchangeRate,\n                currency: reception.currency,\n                fobAmount: reception.fobAmount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customsDuties1Ttc,\n                customsDuties1Tva: reception.customsDuties1Tva,\n                customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                customsDuties2Ht: reception.customsDuties2Ht,\n                importDeliveryTtc: reception.importDeliveryTtc,\n                importDeliveryTva: reception.importDeliveryTva,\n                importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                customsInspectionTtc: reception.customsInspectionTtc,\n                customsInspectionTva: reception.customsInspectionTva,\n                customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                shippingAgencyTtc: reception.shippingAgencyTtc,\n                shippingAgencyTva: reception.shippingAgencyTva,\n                shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                emptyContainersTtc: reception.emptyContainersTtc,\n                emptyContainersTva: reception.emptyContainersTva,\n                emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                demurrageHt: reception.demurrageHt,\n                miscExpensesTtc: reception.miscExpensesTtc,\n                miscExpensesTva: reception.miscExpensesTva,\n                miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                transitServicesTtc: reception.transitServicesTtc,\n                transitServicesTva: reception.transitServicesTva,\n                transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(\"Co\\xfbts sauvegard\\xe9s avec succ\\xe8s pour la facture: \".concat(generatedCosts.invoiceNumber));\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(\"/shipment-reception?edit=\".concat(reception.id));\n    };\n    const deleteReception = (reception)=>{\n        if (confirm('\\xcates-vous s\\xfbr de vouloir supprimer la r\\xe9ception \"'.concat(reception.supplierName, \" - \").concat(reception.orderNumber, '\" ?'))) {\n            try {\n                const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n                const updatedReceptions = savedReceptions.filter((r)=>r.id !== reception.id);\n                localStorage.setItem(\"shipment-receptions\", JSON.stringify(updatedReceptions));\n                // Recharger la liste\n                loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if ((receptionData === null || receptionData === void 0 ? void 0 : receptionData.id) === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(\"R\\xe9ception supprim\\xe9e avec succ\\xe8s\");\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception\");\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const fobDzd = reception.fobAmount * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const landedCostHt = cifDzd + totalAllFees;\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Créer les données pour Excel (format CSV compatible)\n            const excelData = [\n                // En-têtes\n                [\n                    \"SHIPMENT RECEPTION DETAILED REPORT\"\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GENERAL INFORMATION\"\n                ],\n                [\n                    \"Supplier Name\",\n                    reception.supplierName\n                ],\n                [\n                    \"Order Number\",\n                    reception.orderNumber\n                ],\n                [\n                    \"Invoice Number\",\n                    reception.invoiceNumber\n                ],\n                [\n                    \"Currency\",\n                    reception.currency\n                ],\n                [\n                    \"Exchange Rate\",\n                    reception.exchangeRate.toFixed(5)\n                ],\n                [\n                    \"Created At\",\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GOODS PRICE\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (\" + reception.currency + \")\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"FOB Amount\",\n                    reception.fobAmount.toFixed(2),\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"Freight\",\n                    reception.freight.toFixed(2),\n                    (reception.freight * reception.exchangeRate).toFixed(2)\n                ],\n                [\n                    \"CIF Amount\",\n                    (reception.fobAmount + reception.freight).toFixed(2),\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"CUSTOMS DUTIES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Customs Duties 1\",\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2)\n                ],\n                [\n                    \"Customs Duties 2\",\n                    \"\",\n                    \"\",\n                    reception.customsDuties2Ht.toFixed(2)\n                ],\n                [\n                    \"TOTAL CUSTOMS DUTIES\",\n                    \"\",\n                    \"\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"PORT FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Import Delivery\",\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2)\n                ],\n                [\n                    \"Customs Inspection\",\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL PORT FEES\",\n                    \"\",\n                    \"\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"SHIPPING COMPANY FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Shipping Agency\",\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2)\n                ],\n                [\n                    \"Empty Containers\",\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2)\n                ],\n                [\n                    \"Demurrage\",\n                    \"\",\n                    \"\",\n                    reception.demurrageHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL SHIPPING FEES\",\n                    \"\",\n                    \"\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"MISCELLANEOUS EXPENSES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Misc Expenses\",\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"TRANSIT SERVICES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Transit Services\",\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"COST BREAKDOWN SUMMARY\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"CIF Amount\",\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"Customs Duties HT\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"Port Fees HT\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"Shipping Fees HT\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"Misc Expenses HT\",\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"Transit Services HT\",\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL ALL FEES HT\",\n                    totalAllFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"FINAL RESULTS\"\n                ],\n                [\n                    \"Description\",\n                    \"Value\"\n                ],\n                [\n                    \"FOB Amount (DZD)\",\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"LANDED COST HT (DZD)\",\n                    landedCostHt.toFixed(2)\n                ],\n                [\n                    \"Coefficient of Landed Cost\",\n                    landedCostCoefficient.toFixed(4)\n                ],\n                [\n                    \"Formula\",\n                    \"Landed Cost (DZD) / FOB (DZD)\"\n                ],\n                [\n                    \"Calculation\",\n                    landedCostHt.toFixed(2) + \" / \" + fobDzd.toFixed(2) + \" = \" + landedCostCoefficient.toFixed(4)\n                ]\n            ];\n            // Convertir en CSV pour Excel\n            const csvContent = excelData.map((row)=>row.map((cell)=>typeof cell === \"string\" && (cell.includes(\",\") || cell.includes('\"')) ? '\"'.concat(cell.replace(/\"/g, '\"\"'), '\"') : cell).join(\",\")).join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"reception_\".concat(reception.supplierName, \"_\").concat(reception.orderNumber, \"_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi pour la r\\xe9ception: \".concat(reception.supplierName, \" - \").concat(reception.orderNumber));\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? '\"'.concat(cell, '\"') : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"shipment_receptions_export_\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi ! \".concat(allReceptions.length, \" r\\xe9ceptions export\\xe9es.\"));\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 572,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 665,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 748,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 582,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 581,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"CITHuXboUsUYvZZyHh6Xlw/KVr8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});