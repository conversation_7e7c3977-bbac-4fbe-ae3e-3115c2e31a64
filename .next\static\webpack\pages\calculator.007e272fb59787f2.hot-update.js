"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,Save!=!lucide-react */ \"__barrel_optimize__?names=Calculator,FileText,Save!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_calculations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/calculations */ \"./lib/calculations.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    supplierName: \"\",\n    orderNumber: \"\",\n    orderDate: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    currency: \"USD\",\n    exchangeRate: 134.50000,\n    fobAmount: 0,\n    freight: 0,\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    demurrageHt: 0,\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    transitExpensesTtc: 0,\n    transitExpensesTva: 0\n};\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [calculation, setCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCalculating, setIsCalculating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedDrafts, setSavedDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showDrafts, setShowDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger les brouillons sauvegardés depuis localStorage au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saved = localStorage.getItem(\"calculator-drafts\");\n        if (saved) {\n            try {\n                setSavedDrafts(JSON.parse(saved));\n            } catch (error) {\n                console.error(\"Error loading saved drafts:\", error);\n            }\n        }\n    }, []);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const calculateCosts = ()=>{\n        setIsCalculating(true);\n        // Simulate calculation delay\n        setTimeout(()=>{\n            // Create mock shipment data\n            const shipment = {\n                fob_amount: form.fobAmount,\n                freight: form.freight,\n                exchange_rate_used: form.exchangeRate,\n                fob_amount_dzd: form.fobAmount * form.exchangeRate,\n                freight_dzd: form.freight * form.exchangeRate,\n                total_cif_dzd: (form.fobAmount + form.freight) * form.exchangeRate\n            };\n            // Create mock cost data\n            const customsDuties = {\n                customs_duties1_ttc: form.customsDuties1Ttc,\n                customs_duties1_tva: form.customsDuties1Tva,\n                customs_duties2_ttc: form.customsDuties2Ttc,\n                customs_duties2_tva: 0,\n                total_ttc: form.customsDuties1Ttc + form.customsDuties2Ttc,\n                total_tva: form.customsDuties1Tva + 0,\n                total_ht: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc // customsDuties2Ttc est déjà HT\n            };\n            const portFees = {\n                import_delivery_ttc: form.importDeliveryTtc,\n                import_delivery_tva: form.importDeliveryTva,\n                import_delivery_ht: form.importDeliveryTtc - form.importDeliveryTva,\n                customs_inspection_ttc: form.customsInspectionTtc,\n                customs_inspection_tva: form.customsInspectionTva,\n                customs_inspection_ht: form.customsInspectionTtc - form.customsInspectionTva\n            };\n            const shippingFees = {\n                shipping_agency_ttc: form.shippingAgencyTtc,\n                shipping_agency_tva: form.shippingAgencyTva,\n                shipping_agency_ht: form.shippingAgencyTtc - form.shippingAgencyTva,\n                empty_containers_ttc: form.emptyContainersTtc,\n                empty_containers_tva: form.emptyContainersTva,\n                empty_containers_ht: form.emptyContainersTtc - form.emptyContainersTva,\n                demurrage_ht: form.demurrageHt\n            };\n            const miscExpenses = {\n                amount_ttc: form.miscExpensesTtc,\n                amount_tva: form.miscExpensesTva,\n                amount_ht: form.miscExpensesTtc - form.miscExpensesTva\n            };\n            const transitExpenses = {\n                amount_ttc: form.transitExpensesTtc,\n                amount_tva: form.transitExpensesTva,\n                amount_ht: form.transitExpensesTtc - form.transitExpensesTva\n            };\n            const result = (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.calculateLandedCost)(shipment, customsDuties, portFees, shippingFees, miscExpenses, transitExpenses);\n            setCalculation(result);\n            setIsCalculating(false);\n        }, 1000);\n    };\n    const saveDraft = ()=>{\n        // Générer un nom automatique basé sur le supplier et order number\n        const draftName = form.supplierName && form.orderNumber ? \"\".concat(form.supplierName, \" - \").concat(form.orderNumber) : \"Draft \".concat(new Date().toLocaleDateString(\"fr-FR\"), \" \").concat(new Date().toLocaleTimeString(\"fr-FR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        }));\n        const newDraft = {\n            id: Date.now().toString(),\n            name: draftName,\n            form: {\n                ...form\n            },\n            calculation: calculation ? {\n                ...calculation\n            } : null,\n            savedAt: new Date().toISOString()\n        };\n        const updatedDrafts = [\n            ...savedDrafts,\n            newDraft\n        ];\n        setSavedDrafts(updatedDrafts);\n        // Sauvegarder dans localStorage\n        localStorage.setItem(\"calculator-drafts\", JSON.stringify(updatedDrafts));\n        // Notification de succès\n        alert('Brouillon sauvegard\\xe9: \"'.concat(draftName, '\"'));\n    };\n    const loadDraft = (draft)=>{\n        setForm(draft.form);\n        setCalculation(draft.calculation);\n        setShowDrafts(false);\n        alert('Brouillon charg\\xe9: \"'.concat(draft.name, '\"'));\n    };\n    const deleteDraft = (draftId)=>{\n        const updatedDrafts = savedDrafts.filter((d)=>d.id !== draftId);\n        setSavedDrafts(updatedDrafts);\n        localStorage.setItem(\"calculator-drafts\", JSON.stringify(updatedDrafts));\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Cost Calculator\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Calculate landed costs for your imports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: calculateCosts,\n                                    disabled: isCalculating,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        isCalculating ? \"Calculating...\" : \"Calculate\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"General Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Basic shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Supplier Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Number *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Order Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.orderDate,\n                                                                onChange: (e)=>handleInputChange(\"orderDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Exchange Rate (to DZD) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"134.50000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        form.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"FOB and freight costs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"FOB Amount *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.fobAmount,\n                                                                    onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Freight\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.freight,\n                                                                    onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-4 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Total CIF (\",\n                                                                        form.currency,\n                                                                        \"):\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(form.fobAmount + form.freight)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)((form.fobAmount + form.freight) * form.exchangeRate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Customs Duties (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Algeria customs fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Quittance 2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: 'Other Customs Duties (HT) \"T.E.L , A.M.D, .....\"'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.customsDuties2Ttc,\n                                                                            onChange: (e)=>handleInputChange(\"customsDuties2Ttc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 465,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Tva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ttc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 491,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Port Fees (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Port and customs inspection fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Import Delivery\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 518,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc,\n                                                                                onChange: (e)=>handleInputChange(\"importDeliveryTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTva,\n                                                                                onChange: (e)=>handleInputChange(\"importDeliveryTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 542,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc - form.importDeliveryTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Customs Inspection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 561,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTtc,\n                                                                                onChange: (e)=>handleInputChange(\"customsInspectionTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 573,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTva,\n                                                                                onChange: (e)=>handleInputChange(\"customsInspectionTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.customsInspectionTtc - form.customsInspectionTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF PORT FEES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc + form.customsInspectionTtc,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTva + form.customsInspectionTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.importDeliveryTtc - form.importDeliveryTva + (form.customsInspectionTtc - form.customsInspectionTva),\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Shipping Fees (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Shipping company and container fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Shipping Agency Services\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc,\n                                                                                onChange: (e)=>handleInputChange(\"shippingAgencyTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 673,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTva,\n                                                                                onChange: (e)=>handleInputChange(\"shippingAgencyTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc - form.shippingAgencyTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Empty Containers Return\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTtc,\n                                                                                onChange: (e)=>handleInputChange(\"emptyContainersTtc\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 716,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTva,\n                                                                                onChange: (e)=>handleInputChange(\"emptyContainersTva\", parseFloat(e.target.value) || 0),\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 719,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"HT (TTC - TVA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.emptyContainersTtc - form.emptyContainersTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 731,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"Demurrage (if present)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Demurrage HT (manually entered)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.demurrageHt,\n                                                                            onChange: (e)=>handleInputChange(\"demurrageHt\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"OVERALL TOTALS OF SHIPPING FEES:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 769,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total TVA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 779,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTva + form.emptyContainersTva,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 782,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                children: \"Total HT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                type: \"number\",\n                                                                                step: \"0.01\",\n                                                                                value: form.shippingAgencyTtc - form.shippingAgencyTva + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt,\n                                                                                readOnly: true,\n                                                                                className: \"bg-gray-50 text-gray-600\",\n                                                                                placeholder: \"0.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Other Miscellaneous Expenses (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Various additional costs\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Other Miscellaneous Expenses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Total TTC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 822,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTtc,\n                                                                            onChange: (e)=>handleInputChange(\"miscExpensesTtc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"TVA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTva,\n                                                                            onChange: (e)=>handleInputChange(\"miscExpensesTva\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"HT (TTC - TVA)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.miscExpensesTtc - form.miscExpensesTva,\n                                                                            readOnly: true,\n                                                                            className: \"bg-gray-50 text-gray-600\",\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Transit Services Expenses (DZD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"Transit and logistics services\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Transit Services Expenses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"Total TTC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.transitExpensesTtc,\n                                                                            onChange: (e)=>handleInputChange(\"transitExpensesTtc\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"TVA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.transitExpensesTva,\n                                                                            onChange: (e)=>handleInputChange(\"transitExpensesTva\", parseFloat(e.target.value) || 0),\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 891,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: \"HT (TTC - TVA)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 900,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: form.transitExpensesTtc - form.transitExpensesTva,\n                                                                            readOnly: true,\n                                                                            className: \"bg-gray-50 text-gray-600\",\n                                                                            placeholder: \"0.00\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 903,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Calculation Results\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: calculation ? \"Landed cost breakdown\" : \"Enter values and click Calculate\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: calculation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.fobAmountDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Freight (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.freightDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total CIF (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalCifDzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Cost Components (HT)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.customsDutiesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.portFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.shippingFeesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 958,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 956,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc. Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 961,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.miscExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.transitExpensesHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 border-t pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-primary\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost (HT):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.landedCostHt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        calculation.landedCostCoefficient.toFixed(5),\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total Paid (TTC):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_calculations__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(calculation.totalPaidTtc)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 981,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        className: \"w-full\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.FileText, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Generate Report\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_Save_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calculator, {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Enter your shipment details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Fill in the form and click Calculate to see results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"Nw2Zrj/FLe98wSP+mpFYmIAwWw4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});