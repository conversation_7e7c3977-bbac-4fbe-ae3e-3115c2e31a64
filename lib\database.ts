import Database from 'better-sqlite3'
import path from 'path'

// <PERSON><PERSON>er ou ouvrir la base de données
const dbPath = path.join(process.cwd(), 'data', 'shipments.db')
const db = new Database(dbPath)

// Activer les clés étrangères
db.pragma('foreign_keys = ON')

// Créer la table des réceptions de marchandises
const createShipmentReceptionsTable = `
  CREATE TABLE IF NOT EXISTS shipment_receptions (
    id TEXT PRIMARY KEY,
    
    -- Arrival Notice
    voyage_number TEXT,
    bill_of_lading TEXT,
    vessel_name TEXT,
    shipowner TEXT,
    actual_time_of_arrival TEXT,
    
    -- General Information
    exchange_rate REAL NOT NULL,
    currency TEXT NOT NULL,
    supplier_name TEXT NOT NULL,
    shipment_type TEXT,
    order_number TEXT NOT NULL,
    date_of_order_number TEXT,
    invoice_number TEXT NOT NULL,
    invoice_date TEXT,
    from_location TEXT,
    operation_type TEXT,
    description_of_goods TEXT,
    
    -- Banking Information
    bank_name TEXT,
    bank_address TEXT,
    swift_code TEXT,
    account_number TEXT,
    beneficiary_name TEXT,
    beneficiary_address TEXT,
    
    -- Quantities and Container Information
    total_quantity REAL,
    unit_of_measure TEXT,
    total_weight_kg REAL,
    total_volume_m3 REAL,
    number_of_containers INTEGER,
    container_type TEXT,
    container_numbers TEXT,
    
    -- Goods Price in Currency
    fob_amount REAL NOT NULL DEFAULT 0,
    freight REAL NOT NULL DEFAULT 0,
    cif_amount_currency REAL,
    fob_amount_dzd REAL,
    freight_dzd REAL,
    cif_amount_dzd REAL,
    
    -- Customs Duties
    d3_number TEXT,
    d3_date TEXT,
    customs_duties_1_ttc REAL NOT NULL DEFAULT 0,
    customs_duties_1_tva REAL NOT NULL DEFAULT 0,
    customs_duties_2_ht REAL NOT NULL DEFAULT 0,
    
    -- Port Fees
    import_delivery_invoice_number TEXT,
    import_delivery_invoice_date TEXT,
    import_delivery_ttc REAL NOT NULL DEFAULT 0,
    import_delivery_tva REAL NOT NULL DEFAULT 0,
    customs_inspection_invoice_number TEXT,
    customs_inspection_invoice_date TEXT,
    customs_inspection_ttc REAL NOT NULL DEFAULT 0,
    customs_inspection_tva REAL NOT NULL DEFAULT 0,
    
    -- Shipping Company Fees
    shipping_agency_invoice_number TEXT,
    shipping_agency_invoice_date TEXT,
    shipping_agency_ttc REAL NOT NULL DEFAULT 0,
    shipping_agency_tva REAL NOT NULL DEFAULT 0,
    empty_containers_invoice_number TEXT,
    empty_containers_invoice_date TEXT,
    empty_containers_ttc REAL NOT NULL DEFAULT 0,
    empty_containers_tva REAL NOT NULL DEFAULT 0,
    demurrage_invoice_number TEXT,
    demurrage_invoice_date TEXT,
    demurrage_ht REAL NOT NULL DEFAULT 0,
    
    -- Miscellaneous Expenses
    misc_expenses_invoice_number TEXT,
    misc_expenses_invoice_date TEXT,
    misc_expenses_ttc REAL NOT NULL DEFAULT 0,
    misc_expenses_tva REAL NOT NULL DEFAULT 0,
    
    -- Transit Services
    transit_services_invoice_number TEXT,
    transit_services_invoice_date TEXT,
    transit_services_ttc REAL NOT NULL DEFAULT 0,
    transit_services_tva REAL NOT NULL DEFAULT 0,
    
    -- Metadata
    status TEXT DEFAULT 'completed',
    created_at TEXT NOT NULL,
    updated_at TEXT,
    created_by TEXT,
    
    UNIQUE(invoice_number)
  )
`

// Créer la table des coûts générés
const createGeneratedCostsTable = `
  CREATE TABLE IF NOT EXISTS generated_costs (
    id TEXT PRIMARY KEY,
    invoice_number TEXT NOT NULL,
    reception_id TEXT NOT NULL,
    landed_cost_ttc REAL NOT NULL,
    landed_cost_ht REAL NOT NULL,
    landed_cost_coefficient REAL NOT NULL,
    total_customs_duties REAL NOT NULL,
    total_port_fees REAL NOT NULL,
    total_shipping_fees REAL NOT NULL,
    total_misc_expenses REAL NOT NULL,
    total_transit_services REAL NOT NULL,
    cif_dzd REAL NOT NULL,
    fob_dzd REAL NOT NULL,
    exchange_rate_used REAL NOT NULL,
    generated_at TEXT NOT NULL,
    
    FOREIGN KEY (reception_id) REFERENCES shipment_receptions(id) ON DELETE CASCADE
  )
`

// Créer la table des brouillons
const createDraftsTable = `
  CREATE TABLE IF NOT EXISTS reception_drafts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    form_data TEXT NOT NULL,
    saved_at TEXT NOT NULL,
    created_by TEXT
  )
`

// Initialiser les tables
db.exec(createShipmentReceptionsTable)
db.exec(createGeneratedCostsTable)
db.exec(createDraftsTable)

// Fonctions pour les réceptions
export const shipmentReceptions = {
  // Créer une nouvelle réception
  create: db.prepare(`
    INSERT INTO shipment_receptions (
      id, voyage_number, bill_of_lading, vessel_name, shipowner, actual_time_of_arrival,
      exchange_rate, currency, supplier_name, shipment_type, order_number, date_of_order_number,
      invoice_number, invoice_date, from_location, operation_type, description_of_goods,
      bank_name, bank_address, swift_code, account_number, beneficiary_name, beneficiary_address,
      total_quantity, unit_of_measure, total_weight_kg, total_volume_m3, number_of_containers,
      container_type, container_numbers, fob_amount, freight, cif_amount_currency,
      fob_amount_dzd, freight_dzd, cif_amount_dzd, d3_number, d3_date,
      customs_duties_1_ttc, customs_duties_1_tva, customs_duties_2_ht,
      import_delivery_invoice_number, import_delivery_invoice_date, import_delivery_ttc, import_delivery_tva,
      customs_inspection_invoice_number, customs_inspection_invoice_date, customs_inspection_ttc, customs_inspection_tva,
      shipping_agency_invoice_number, shipping_agency_invoice_date, shipping_agency_ttc, shipping_agency_tva,
      empty_containers_invoice_number, empty_containers_invoice_date, empty_containers_ttc, empty_containers_tva,
      demurrage_invoice_number, demurrage_invoice_date, demurrage_ht,
      misc_expenses_invoice_number, misc_expenses_invoice_date, misc_expenses_ttc, misc_expenses_tva,
      transit_services_invoice_number, transit_services_invoice_date, transit_services_ttc, transit_services_tva,
      status, created_at, created_by
    ) VALUES (
      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
    )
  `),

  // Récupérer toutes les réceptions
  getAll: db.prepare('SELECT * FROM shipment_receptions ORDER BY created_at DESC'),

  // Récupérer une réception par ID
  getById: db.prepare('SELECT * FROM shipment_receptions WHERE id = ?'),

  // Récupérer une réception par numéro de facture
  getByInvoiceNumber: db.prepare('SELECT * FROM shipment_receptions WHERE invoice_number = ?'),

  // Mettre à jour une réception
  update: db.prepare(`
    UPDATE shipment_receptions SET
      voyage_number = ?, bill_of_lading = ?, vessel_name = ?, shipowner = ?, actual_time_of_arrival = ?,
      exchange_rate = ?, currency = ?, supplier_name = ?, shipment_type = ?, order_number = ?, date_of_order_number = ?,
      invoice_number = ?, invoice_date = ?, from_location = ?, operation_type = ?, description_of_goods = ?,
      bank_name = ?, bank_address = ?, swift_code = ?, account_number = ?, beneficiary_name = ?, beneficiary_address = ?,
      total_quantity = ?, unit_of_measure = ?, total_weight_kg = ?, total_volume_m3 = ?, number_of_containers = ?,
      container_type = ?, container_numbers = ?, fob_amount = ?, freight = ?, cif_amount_currency = ?,
      fob_amount_dzd = ?, freight_dzd = ?, cif_amount_dzd = ?, d3_number = ?, d3_date = ?,
      customs_duties_1_ttc = ?, customs_duties_1_tva = ?, customs_duties_2_ht = ?,
      import_delivery_invoice_number = ?, import_delivery_invoice_date = ?, import_delivery_ttc = ?, import_delivery_tva = ?,
      customs_inspection_invoice_number = ?, customs_inspection_invoice_date = ?, customs_inspection_ttc = ?, customs_inspection_tva = ?,
      shipping_agency_invoice_number = ?, shipping_agency_invoice_date = ?, shipping_agency_ttc = ?, shipping_agency_tva = ?,
      empty_containers_invoice_number = ?, empty_containers_invoice_date = ?, empty_containers_ttc = ?, empty_containers_tva = ?,
      demurrage_invoice_number = ?, demurrage_invoice_date = ?, demurrage_ht = ?,
      misc_expenses_invoice_number = ?, misc_expenses_invoice_date = ?, misc_expenses_ttc = ?, misc_expenses_tva = ?,
      transit_services_invoice_number = ?, transit_services_invoice_date = ?, transit_services_ttc = ?, transit_services_tva = ?,
      updated_at = ?
    WHERE id = ?
  `),

  // Supprimer une réception
  delete: db.prepare('DELETE FROM shipment_receptions WHERE id = ?'),

  // Compter le nombre total de réceptions
  count: db.prepare('SELECT COUNT(*) as count FROM shipment_receptions')
}

// Fonctions pour les coûts générés
export const generatedCosts = {
  create: db.prepare(`
    INSERT INTO generated_costs (
      id, invoice_number, reception_id, landed_cost_ttc, landed_cost_ht, landed_cost_coefficient,
      total_customs_duties, total_port_fees, total_shipping_fees, total_misc_expenses, total_transit_services,
      cif_dzd, fob_dzd, exchange_rate_used, generated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  getAll: db.prepare('SELECT * FROM generated_costs ORDER BY generated_at DESC'),
  getByInvoiceNumber: db.prepare('SELECT * FROM generated_costs WHERE invoice_number = ?'),
  delete: db.prepare('DELETE FROM generated_costs WHERE id = ?')
}

// Fonctions pour les brouillons
export const drafts = {
  create: db.prepare('INSERT INTO reception_drafts (id, name, form_data, saved_at, created_by) VALUES (?, ?, ?, ?, ?)'),
  getAll: db.prepare('SELECT * FROM reception_drafts ORDER BY saved_at DESC'),
  delete: db.prepare('DELETE FROM reception_drafts WHERE id = ?')
}

export default db
