"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = ()=>{\n        try {\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplierName,\n                    orderNumber: reception.orderNumber,\n                    invoiceNumber: reception.invoiceNumber,\n                    exchangeRate: reception.exchangeRate,\n                    currency: reception.currency,\n                    fobAmount: reception.fobAmount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customsDuties1Ttc,\n                    customsDuties1Tva: reception.customsDuties1Tva,\n                    customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                    customsDuties2Ht: reception.customsDuties2Ht,\n                    importDeliveryTtc: reception.importDeliveryTtc,\n                    importDeliveryTva: reception.importDeliveryTva,\n                    importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                    customsInspectionTtc: reception.customsInspectionTtc,\n                    customsInspectionTva: reception.customsInspectionTva,\n                    customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                    shippingAgencyTtc: reception.shippingAgencyTtc,\n                    shippingAgencyTva: reception.shippingAgencyTva,\n                    shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                    emptyContainersTtc: reception.emptyContainersTtc,\n                    emptyContainersTva: reception.emptyContainersTva,\n                    emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                    demurrageHt: reception.demurrageHt,\n                    miscExpensesTtc: reception.miscExpensesTtc,\n                    miscExpensesTva: reception.miscExpensesTva,\n                    miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                    transitServicesTtc: reception.transitServicesTtc,\n                    transitServicesTva: reception.transitServicesTva,\n                    transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva,\n                    createdAt: reception.createdAt\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const reception = savedReceptions.find((r)=>r.invoiceNumber === invoiceNumber.trim());\n            if (!reception) {\n                setError(\"Aucune r\\xe9ception trouv\\xe9e pour la facture: \".concat(invoiceNumber));\n                setIsLoading(false);\n                return;\n            }\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplierName,\n                orderNumber: reception.orderNumber,\n                invoiceNumber: reception.invoiceNumber,\n                exchangeRate: reception.exchangeRate,\n                currency: reception.currency,\n                fobAmount: reception.fobAmount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customsDuties1Ttc,\n                customsDuties1Tva: reception.customsDuties1Tva,\n                customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                customsDuties2Ht: reception.customsDuties2Ht,\n                importDeliveryTtc: reception.importDeliveryTtc,\n                importDeliveryTva: reception.importDeliveryTva,\n                importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                customsInspectionTtc: reception.customsInspectionTtc,\n                customsInspectionTva: reception.customsInspectionTva,\n                customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                shippingAgencyTtc: reception.shippingAgencyTtc,\n                shippingAgencyTva: reception.shippingAgencyTva,\n                shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                emptyContainersTtc: reception.emptyContainersTtc,\n                emptyContainersTva: reception.emptyContainersTva,\n                emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                demurrageHt: reception.demurrageHt,\n                miscExpensesTtc: reception.miscExpensesTtc,\n                miscExpensesTva: reception.miscExpensesTva,\n                miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                transitServicesTtc: reception.transitServicesTtc,\n                transitServicesTva: reception.transitServicesTva,\n                transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(\"Co\\xfbts sauvegard\\xe9s avec succ\\xe8s pour la facture: \".concat(generatedCosts.invoiceNumber));\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(\"/shipment-reception?edit=\".concat(reception.id));\n    };\n    const deleteReception = (reception)=>{\n        if (confirm('\\xcates-vous s\\xfbr de vouloir supprimer la r\\xe9ception \"'.concat(reception.supplierName, \" - \").concat(reception.orderNumber, '\" ?'))) {\n            try {\n                const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n                const updatedReceptions = savedReceptions.filter((r)=>r.id !== reception.id);\n                localStorage.setItem(\"shipment-receptions\", JSON.stringify(updatedReceptions));\n                // Recharger la liste\n                loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if ((receptionData === null || receptionData === void 0 ? void 0 : receptionData.id) === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(\"R\\xe9ception supprim\\xe9e avec succ\\xe8s\");\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception\");\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const estimatedLandedCost = cifDzd + totalAllFees;\n            // Créer un rapport détaillé pour cette réception\n            const reportContent = [\n                \"=== SHIPMENT RECEPTION DETAILED REPORT ===\",\n                \"\",\n                \"--- GENERAL INFORMATION ---\",\n                \"Supplier Name: \".concat(reception.supplierName),\n                \"Order Number: \".concat(reception.orderNumber),\n                \"Invoice Number: \".concat(reception.invoiceNumber),\n                \"Currency: \".concat(reception.currency),\n                \"Exchange Rate: \".concat(reception.exchangeRate.toFixed(5)),\n                \"Created At: \".concat(new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")),\n                \"\",\n                \"--- GOODS PRICE ---\",\n                \"FOB Amount (\".concat(reception.currency, \"): \").concat(reception.fobAmount.toFixed(2)),\n                \"Freight (\".concat(reception.currency, \"): \").concat(reception.freight.toFixed(2)),\n                \"CIF Amount (DZD): \".concat(cifDzd.toFixed(2)),\n                \"\",\n                \"--- CUSTOMS DUTIES ---\",\n                \"Customs Duties 1 TTC (DZD): \".concat(reception.customsDuties1Ttc.toFixed(2)),\n                \"Customs Duties 1 TVA (DZD): \".concat(reception.customsDuties1Tva.toFixed(2)),\n                \"Customs Duties 1 HT (DZD): \".concat(reception.customsDuties1Ht.toFixed(2)),\n                \"Customs Duties 2 HT (DZD): \".concat(reception.customsDuties2Ht.toFixed(2)),\n                \"TOTAL CUSTOMS DUTIES HT: \".concat(totalCustomsDuties.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- PORT FEES ---\",\n                \"Import Delivery TTC (DZD): \".concat(reception.importDeliveryTtc.toFixed(2)),\n                \"Import Delivery TVA (DZD): \".concat(reception.importDeliveryTva.toFixed(2)),\n                \"Import Delivery HT (DZD): \".concat(reception.importDeliveryHt.toFixed(2)),\n                \"Customs Inspection TTC (DZD): \".concat(reception.customsInspectionTtc.toFixed(2)),\n                \"Customs Inspection TVA (DZD): \".concat(reception.customsInspectionTva.toFixed(2)),\n                \"Customs Inspection HT (DZD): \".concat(reception.customsInspectionHt.toFixed(2)),\n                \"TOTAL PORT FEES HT: \".concat(totalPortFees.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- SHIPPING COMPANY FEES ---\",\n                \"Shipping Agency TTC (DZD): \".concat(reception.shippingAgencyTtc.toFixed(2)),\n                \"Shipping Agency TVA (DZD): \".concat(reception.shippingAgencyTva.toFixed(2)),\n                \"Shipping Agency HT (DZD): \".concat(reception.shippingAgencyHt.toFixed(2)),\n                \"Empty Containers TTC (DZD): \".concat(reception.emptyContainersTtc.toFixed(2)),\n                \"Empty Containers TVA (DZD): \".concat(reception.emptyContainersTva.toFixed(2)),\n                \"Empty Containers HT (DZD): \".concat(reception.emptyContainersHt.toFixed(2)),\n                \"Demurrage HT (DZD): \".concat(reception.demurrageHt.toFixed(2)),\n                \"TOTAL SHIPPING FEES HT: \".concat(totalShippingFees.toFixed(2), \" DZD\"),\n                \"\",\n                \"--- MISCELLANEOUS EXPENSES ---\",\n                \"Misc Expenses TTC (DZD): \".concat(reception.miscExpensesTtc.toFixed(2)),\n                \"Misc Expenses TVA (DZD): \".concat(reception.miscExpensesTva.toFixed(2)),\n                \"Misc Expenses HT (DZD): \".concat(reception.miscExpensesHt.toFixed(2)),\n                \"\",\n                \"--- TRANSIT SERVICES ---\",\n                \"Transit Services TTC (DZD): \".concat(reception.transitServicesTtc.toFixed(2)),\n                \"Transit Services TVA (DZD): \".concat(reception.transitServicesTva.toFixed(2)),\n                \"Transit Services HT (DZD): \".concat(reception.transitServicesHt.toFixed(2)),\n                \"\",\n                \"=== SUMMARY ===\",\n                \"CIF Amount (DZD): \".concat(cifDzd.toFixed(2)),\n                \"Total All Fees HT (DZD): \".concat(totalAllFees.toFixed(2)),\n                \"ESTIMATED LANDED COST HT: \".concat(estimatedLandedCost.toFixed(2), \" DZD\"),\n                \"ESTIMATED LANDED COST COEFFICIENT: \".concat((estimatedLandedCost / (reception.fobAmount * reception.exchangeRate)).toFixed(4)),\n                \"\",\n                \"--- BREAKDOWN ---\",\n                \"FOB (DZD): \".concat((reception.fobAmount * reception.exchangeRate).toFixed(2)),\n                \"Freight (DZD): \".concat((reception.freight * reception.exchangeRate).toFixed(2)),\n                \"Customs Duties: \".concat(totalCustomsDuties.toFixed(2)),\n                \"Port Fees: \".concat(totalPortFees.toFixed(2)),\n                \"Shipping Fees: \".concat(totalShippingFees.toFixed(2)),\n                \"Misc Expenses: \".concat(reception.miscExpensesHt.toFixed(2)),\n                \"Transit Services: \".concat(reception.transitServicesHt.toFixed(2)),\n                \"\",\n                \"=== END OF REPORT ===\"\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier\n            const blob = new Blob([\n                reportContent\n            ], {\n                type: \"text/plain;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"reception_\".concat(reception.supplierName, \"_\").concat(reception.orderNumber, \"_\").concat(new Date().toISOString().split(\"T\")[0], \".txt\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export r\\xe9ussi pour la r\\xe9ception: \".concat(reception.supplierName, \" - \").concat(reception.orderNumber));\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? '\"'.concat(cell, '\"') : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"shipment_receptions_export_\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export r\\xe9ussi ! \".concat(allReceptions.length, \" r\\xe9ceptions export\\xe9es.\"));\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"CITHuXboUsUYvZZyHh6Xlw/KVr8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});