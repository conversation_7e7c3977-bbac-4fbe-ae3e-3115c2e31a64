import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/layout/Layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ship, Save, FileText } from 'lucide-react'

interface ShipmentReceptionForm {
  // Arrival Notice
  voyageNumber: string
  billOfLading: string
  vesselName: string
  shipowner: string
  actualTimeOfArrival: string

  // General Information
  exchangeRate: number
  currency: 'USD' | 'EUR' | 'CNY'
  supplierName: string
  shipmentType: 'SEA' | 'AIR' | 'EXPRESS' | 'OTHER'
  orderNumber: string
  dateOfOrderNumber: string
  invoiceNumber: string
  invoiceDate: string
  fromLocation: string
  toLocation: string
  operationType: 'OPEX' | 'INVESTMENT' | 'REINVESTMENT' | 'RAW_MATERIALS' | 'SPARE_PARTS' | 'GOODS_RESALE' | 'SERVICES' | 'TEMPORARY_IMPORT'
  descriptionOfGoods: 'SPARE_PARTS_VEHICLES' | 'LUBRICANT' | 'ACCESSORY' | 'OTHER'
  bankName: string
  lcNumber: string
  validationLC: string
  paymentTerm: string
  priceTerm: string
  quantityPcs: number
  numberContainers20: number
  numberContainers40: number
  numberPackages: number

  // Goods Price in Currency
  fobAmount: number
  freight: number
  totalAmountCif: number

  // Conversion to DZD (calculated automatically)
  fobAmountDzd: number
  freightDzd: number
  totalAmountCifDzd: number

  // Customs Duties
  costAllocationNameCustoms: string
  d3Number: string
  d3Date: string

  // Quittance 1
  customsDuties1Ttc: number
  customsDuties1Tva: number
  customsDuties1Ht: number

  // Quittance 2
  customsDuties2Ttc: number
  customsDuties2Tva: number
  customsDuties2Ht: number

  // Overall Totals Customs Duties
  customsDutiesOverallTtc: number
  customsDutiesOverallTva: number
  customsDutiesOverallHt: number

  // Port Fees - Import Delivery
  costAllocationNameImportDelivery: string
  importDeliveryInvoiceNumber: string
  importDeliveryInvoiceDate: string
  importDeliveryTtc: number
  importDeliveryTva: number
  importDeliveryHt: number

  // Port Fees - Customs Inspection
  costAllocationNameCustomsInspection: string
  customsInspectionInvoiceNumber: string
  customsInspectionInvoiceDate: string
  customsInspectionTtc: number
  customsInspectionTva: number
  customsInspectionHt: number

  // Overall Totals Port Fees
  portFeesOverallTtc: number
  portFeesOverallTva: number
  portFeesOverallHt: number

  // Shipping Company Fees - Shipping Agency Services
  costAllocationNameShippingAgency: string
  shippingAgencyInvoiceNumber: string
  shippingAgencyInvoiceDate: string
  shippingAgencyTtc: number
  shippingAgencyTva: number
  shippingAgencyHt: number

  // Shipping Company Fees - Empty Containers
  costAllocationNameEmptyContainers: string
  emptyContainersInvoiceNumber: string
  emptyContainersInvoiceDate: string
  emptyContainersTtc: number
  emptyContainersTva: number
  emptyContainersHt: number

  // Shipping Company Fees - Demurrage
  costAllocationNameDemurrage: string
  demurrageInvoiceNumber: string
  demurrageInvoiceDate: string
  demurrageHt: number

  // Overall Totals Shipping Company Fees
  shippingFeesOverallTtc: number
  shippingFeesOverallTva: number
  shippingFeesOverallHt: number

  // Other Miscellaneous Expenses
  costAllocationNameMiscExpenses: string
  miscExpensesInvoiceNumber: string
  miscExpensesInvoiceDate: string
  miscExpensesTtc: number
  miscExpensesTva: number
  miscExpensesHt: number

  // Transit Services Expenses
  costAllocationNameTransitServices: string
  transitServicesInvoiceNumber: string
  transitServicesInvoiceDate: string
  transitServicesTtc: number
  transitServicesTva: number
  transitServicesHt: number
}

const defaultForm: ShipmentReceptionForm = {
  // Arrival Notice
  voyageNumber: '',
  billOfLading: '',
  vesselName: '',
  shipowner: '',
  actualTimeOfArrival: '',

  // General Information
  exchangeRate: 0, // Sera récupéré depuis les Settings de l'admin
  currency: 'USD',
  supplierName: '',
  shipmentType: 'SEA',
  orderNumber: '',
  dateOfOrderNumber: '',
  invoiceNumber: '',
  invoiceDate: '',
  fromLocation: '',
  toLocation: '',
  operationType: 'OPEX',
  descriptionOfGoods: 'SPARE_PARTS_VEHICLES',
  bankName: '',
  lcNumber: '',
  validationLC: '',
  paymentTerm: '',
  priceTerm: '',
  quantityPcs: 0,
  numberContainers20: 0,
  numberContainers40: 0,
  numberPackages: 0,

  // Goods Price in Currency
  fobAmount: 0,
  freight: 0,
  totalAmountCif: 0,

  // Conversion to DZD (calculated automatically)
  fobAmountDzd: 0,
  freightDzd: 0,
  totalAmountCifDzd: 0,

  // Customs Duties
  costAllocationNameCustoms: 'ALGERIA CUSTOMS',
  d3Number: '',
  d3Date: '',

  // Quittance 1
  customsDuties1Ttc: 0,
  customsDuties1Tva: 0,
  customsDuties1Ht: 0,

  // Quittance 2
  customsDuties2Ttc: 0,
  customsDuties2Tva: 0,
  customsDuties2Ht: 0,

  // Overall Totals Customs Duties
  customsDutiesOverallTtc: 0,
  customsDutiesOverallTva: 0,
  customsDutiesOverallHt: 0,

  // Port Fees - Import Delivery
  costAllocationNameImportDelivery: 'IMPORT DELIVERY',
  importDeliveryInvoiceNumber: '',
  importDeliveryInvoiceDate: '',
  importDeliveryTtc: 0,
  importDeliveryTva: 0,
  importDeliveryHt: 0,

  // Port Fees - Customs Inspection
  costAllocationNameCustomsInspection: 'CUSTOMS INSPECTION',
  customsInspectionInvoiceNumber: '',
  customsInspectionInvoiceDate: '',
  customsInspectionTtc: 0,
  customsInspectionTva: 0,
  customsInspectionHt: 0,

  // Overall Totals Port Fees
  portFeesOverallTtc: 0,
  portFeesOverallTva: 0,
  portFeesOverallHt: 0,

  // Shipping Company Fees - Shipping Agency Services
  costAllocationNameShippingAgency: 'SHIPPING AGENCY SERVICES',
  shippingAgencyInvoiceNumber: '',
  shippingAgencyInvoiceDate: '',
  shippingAgencyTtc: 0,
  shippingAgencyTva: 0,
  shippingAgencyHt: 0,

  // Shipping Company Fees - Empty Containers
  costAllocationNameEmptyContainers: 'EMPTY CONTAINERS',
  emptyContainersInvoiceNumber: '',
  emptyContainersInvoiceDate: '',
  emptyContainersTtc: 0,
  emptyContainersTva: 0,
  emptyContainersHt: 0,

  // Shipping Company Fees - Demurrage
  costAllocationNameDemurrage: 'DEMURRAGE IF PRESENT',
  demurrageInvoiceNumber: '',
  demurrageInvoiceDate: '',
  demurrageHt: 0,

  // Overall Totals Shipping Company Fees
  shippingFeesOverallTtc: 0,
  shippingFeesOverallTva: 0,
  shippingFeesOverallHt: 0,

  // Other Miscellaneous Expenses
  costAllocationNameMiscExpenses: 'OTHER MISCELLANEOUS EXPENSES',
  miscExpensesInvoiceNumber: '',
  miscExpensesInvoiceDate: '',
  miscExpensesTtc: 0,
  miscExpensesTva: 0,
  miscExpensesHt: 0,

  // Transit Services Expenses
  costAllocationNameTransitServices: 'TRANSIT SERVICES EXPENSES',
  transitServicesInvoiceNumber: '',
  transitServicesInvoiceDate: '',
  transitServicesTtc: 0,
  transitServicesTva: 0,
  transitServicesHt: 0
}

export default function ShipmentReception() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [form, setForm] = useState<ShipmentReceptionForm>(defaultForm)
  const [currentExchangeRate, setCurrentExchangeRate] = useState<number>(0)
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Récupérer le taux de change depuis les Settings de l'admin
  useEffect(() => {
    const fetchExchangeRate = async () => {
      try {
        setIsLoadingExchangeRate(true)
        // TODO: Remplacer par l'API réelle des Settings
        // const response = await fetch('/api/settings/exchange-rate')
        // const data = await response.json()

        // Pour l'instant, simulation avec localStorage ou valeur par défaut
        const savedRate = localStorage.getItem('admin-exchange-rate')
        const rate = savedRate ? parseFloat(savedRate) : 134.50000

        setCurrentExchangeRate(rate)
        setForm(prev => ({
          ...prev,
          exchangeRate: rate
        }))
      } catch (error) {
        console.error('Error fetching exchange rate:', error)
        // Valeur de fallback
        const fallbackRate = 134.50000
        setCurrentExchangeRate(fallbackRate)
        setForm(prev => ({
          ...prev,
          exchangeRate: fallbackRate
        }))
      } finally {
        setIsLoadingExchangeRate(false)
      }
    }

    if (status === 'authenticated') {
      fetchExchangeRate()
    }
  }, [status])

  const handleInputChange = (field: keyof ShipmentReceptionForm, value: string | number) => {
    setForm(prev => {
      const updated = {
        ...prev,
        [field]: value
      }

      // Calculs automatiques pour les conversions DZD
      if (field === 'fobAmount' || field === 'exchangeRate') {
        updated.fobAmountDzd = updated.fobAmount * updated.exchangeRate
        updated.totalAmountCif = updated.fobAmount + updated.freight
        updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate
      }

      if (field === 'freight' || field === 'exchangeRate') {
        updated.freightDzd = updated.freight * updated.exchangeRate
        updated.totalAmountCif = updated.fobAmount + updated.freight
        updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate
      }

      // Calculs automatiques pour les champs HT
      if (field === 'customsDuties1Ttc' || field === 'customsDuties1Tva') {
        updated.customsDuties1Ht = updated.customsDuties1Ttc - updated.customsDuties1Tva
      }

      // Calculs des totaux Customs Duties
      updated.customsDutiesOverallTtc = updated.customsDuties1Ttc
      updated.customsDutiesOverallTva = updated.customsDuties1Tva
      updated.customsDutiesOverallHt = (updated.customsDuties1Ttc - updated.customsDuties1Tva) + updated.customsDuties2Ht

      // Calculs automatiques Port Fees
      if (field === 'importDeliveryTtc' || field === 'importDeliveryTva') {
        updated.importDeliveryHt = updated.importDeliveryTtc - updated.importDeliveryTva
      }

      if (field === 'customsInspectionTtc' || field === 'customsInspectionTva') {
        updated.customsInspectionHt = updated.customsInspectionTtc - updated.customsInspectionTva
      }

      // Calculs des totaux Port Fees
      updated.portFeesOverallTtc = updated.importDeliveryTtc + updated.customsInspectionTtc
      updated.portFeesOverallTva = updated.importDeliveryTva + updated.customsInspectionTva
      updated.portFeesOverallHt = updated.importDeliveryHt + updated.customsInspectionHt

      // Calculs automatiques Shipping Fees
      if (field === 'shippingAgencyTtc' || field === 'shippingAgencyTva') {
        updated.shippingAgencyHt = updated.shippingAgencyTtc - updated.shippingAgencyTva
      }

      if (field === 'emptyContainersTtc' || field === 'emptyContainersTva') {
        updated.emptyContainersHt = updated.emptyContainersTtc - updated.emptyContainersTva
      }

      // Calculs des totaux Shipping Fees
      updated.shippingFeesOverallTtc = updated.shippingAgencyTtc + updated.emptyContainersTtc + updated.demurrageHt
      updated.shippingFeesOverallTva = updated.shippingAgencyTva + updated.emptyContainersTva
      updated.shippingFeesOverallHt = updated.shippingAgencyHt + updated.emptyContainersHt + updated.demurrageHt

      // Calculs automatiques Misc Expenses
      if (field === 'miscExpensesTtc' || field === 'miscExpensesTva') {
        updated.miscExpensesHt = updated.miscExpensesTtc - updated.miscExpensesTva
      }

      // Calculs automatiques Transit Services
      if (field === 'transitServicesTtc' || field === 'transitServicesTva') {
        updated.transitServicesHt = updated.transitServicesTtc - updated.transitServicesTva
      }

      return updated
    })
  }

  const saveDraft = () => {
    // Générer un nom automatique basé sur le supplier et order number
    const draftName = form.supplierName && form.orderNumber
      ? `${form.supplierName} - ${form.orderNumber}`
      : `Reception Draft ${new Date().toLocaleDateString('fr-FR')} ${new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`

    // Sauvegarder dans localStorage pour l'instant
    const savedDrafts = JSON.parse(localStorage.getItem('shipment-reception-drafts') || '[]')
    const newDraft = {
      id: Date.now().toString(),
      name: draftName,
      form: { ...form },
      savedAt: new Date().toISOString()
    }

    savedDrafts.push(newDraft)
    localStorage.setItem('shipment-reception-drafts', JSON.stringify(savedDrafts))

    alert(`Brouillon sauvegardé: "${draftName}"`)
  }

  const saveReception = async () => {
    try {
      // Validation des champs obligatoires
      if (!form.supplierName || !form.orderNumber || !form.invoiceNumber) {
        alert('Veuillez remplir les champs obligatoires : Supplier Name, Order Number, Invoice Number')
        return
      }

      // Sauvegarder dans la base de données via l'API
      const response = await fetch('/api/shipment-receptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          // Arrival Notice
          voyageNumber: form.voyageNumber,
          billOfLading: form.billOfLading,
          vesselName: form.vesselName,
          shipowner: form.shipowner,
          actualTimeOfArrival: form.actualTimeOfArrival,

          // General Information
          exchangeRate: form.exchangeRate,
          currency: form.currency,
          supplierName: form.supplierName,
          shipmentType: form.shipmentType,
          orderNumber: form.orderNumber,
          dateOfOrderNumber: form.dateOfOrderNumber,
          invoiceNumber: form.invoiceNumber,
          invoiceDate: form.invoiceDate,
          fromLocation: form.fromLocation,
          operationType: form.operationType,
          descriptionOfGoods: form.descriptionOfGoods,

          // Banking Information
          bankName: form.bankName,
          bankAddress: form.bankAddress,
          swiftCode: form.swiftCode,
          accountNumber: form.accountNumber,
          beneficiaryName: form.beneficiaryName,
          beneficiaryAddress: form.beneficiaryAddress,

          // Quantities and Container Information
          totalQuantity: form.totalQuantity,
          unitOfMeasure: form.unitOfMeasure,
          totalWeightKg: form.totalWeightKg,
          totalVolumeM3: form.totalVolumeM3,
          numberOfContainers: form.numberOfContainers,
          containerType: form.containerType,
          containerNumbers: form.containerNumbers,

          // Goods Price
          fobAmount: form.fobAmount,
          freight: form.freight,
          cifAmountCurrency: form.fobAmount + form.freight,
          fobAmountDzd: form.fobAmount * form.exchangeRate,
          freightDzd: form.freight * form.exchangeRate,
          cifAmountDzd: (form.fobAmount + form.freight) * form.exchangeRate,

          // Customs Duties
          d3Number: form.d3Number,
          d3Date: form.d3Date,
          customsDuties1Ttc: form.customsDuties1Ttc,
          customsDuties1Tva: form.customsDuties1Tva,
          customsDuties2Ht: form.customsDuties2Ht,

          // Port Fees
          importDeliveryInvoiceNumber: form.importDeliveryInvoiceNumber,
          importDeliveryInvoiceDate: form.importDeliveryInvoiceDate,
          importDeliveryTtc: form.importDeliveryTtc,
          importDeliveryTva: form.importDeliveryTva,
          customsInspectionInvoiceNumber: form.customsInspectionInvoiceNumber,
          customsInspectionInvoiceDate: form.customsInspectionInvoiceDate,
          customsInspectionTtc: form.customsInspectionTtc,
          customsInspectionTva: form.customsInspectionTva,

          // Shipping Company Fees
          shippingAgencyInvoiceNumber: form.shippingAgencyInvoiceNumber,
          shippingAgencyInvoiceDate: form.shippingAgencyInvoiceDate,
          shippingAgencyTtc: form.shippingAgencyTtc,
          shippingAgencyTva: form.shippingAgencyTva,
          emptyContainersInvoiceNumber: form.emptyContainersInvoiceNumber,
          emptyContainersInvoiceDate: form.emptyContainersInvoiceDate,
          emptyContainersTtc: form.emptyContainersTtc,
          emptyContainersTva: form.emptyContainersTva,
          demurrageInvoiceNumber: form.demurrageInvoiceNumber,
          demurrageInvoiceDate: form.demurrageInvoiceDate,
          demurrageHt: form.demurrageHt,

          // Miscellaneous Expenses
          miscExpensesInvoiceNumber: form.miscExpensesInvoiceNumber,
          miscExpensesInvoiceDate: form.miscExpensesInvoiceDate,
          miscExpensesTtc: form.miscExpensesTtc,
          miscExpensesTva: form.miscExpensesTva,

          // Transit Services
          transitServicesInvoiceNumber: form.transitServicesInvoiceNumber,
          transitServicesInvoiceDate: form.transitServicesInvoiceDate,
          transitServicesTtc: form.transitServicesTtc,
          transitServicesTva: form.transitServicesTva
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erreur lors de la sauvegarde')
      }

      const result = await response.json()

      alert(`Réception sauvegardée avec succès dans la base de données: "${form.supplierName} - ${form.orderNumber}"`)

      // Optionnel: Rediriger vers la liste des réceptions
      // router.push('/calculator')

    } catch (error) {
      console.error('Error saving reception:', error)
      alert('Erreur lors de la sauvegarde de la réception: ' + (error as Error).message)
    }
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Shipment Reception</h1>
            <p className="text-gray-600">Record merchandise reception from arrival notice to container release</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={saveDraft}>
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button onClick={saveReception}>
              <FileText className="h-4 w-4 mr-2" />
              Save Reception
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          {/* Main Form */}
          <div className="space-y-6">

            {/* Arrival Notice Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Ship className="h-5 w-5" />
                  Arrival Notice
                </CardTitle>
                <CardDescription>Vessel arrival and shipping information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Voyage Number - "CALL AT PORT" (ESCALE) N°
                    </label>
                    <Input
                      value={form.voyageNumber}
                      onChange={(e) => handleInputChange('voyageNumber', e.target.value)}
                      placeholder="Enter voyage number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bill of Lading (B/L) (Connaissement) N°
                    </label>
                    <Input
                      value={form.billOfLading}
                      onChange={(e) => handleInputChange('billOfLading', e.target.value)}
                      placeholder="Enter B/L number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vessel Name (Navire)
                    </label>
                    <Input
                      value={form.vesselName}
                      onChange={(e) => handleInputChange('vesselName', e.target.value)}
                      placeholder="Enter vessel name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Shipowner (Armateur)
                    </label>
                    <Input
                      value={form.shipowner}
                      onChange={(e) => handleInputChange('shipowner', e.target.value)}
                      placeholder="Enter shipowner name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Actual Time of Arrival (Date Accostage)
                    </label>
                    <Input
                      type="datetime-local"
                      value={form.actualTimeOfArrival}
                      onChange={(e) => handleInputChange('actualTimeOfArrival', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* General Information Section */}
            <Card>
              <CardHeader>
                <CardTitle>General Information</CardTitle>
                <CardDescription>Basic shipment and order details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Première ligne */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exchange Rate Used : DZD/{form.currency} *
                      {isLoadingExchangeRate && <span className="text-xs text-blue-600 ml-2">(Loading...)</span>}
                    </label>
                    <Input
                      type="number"
                      step="0.00001"
                      value={form.exchangeRate}
                      onChange={(e) => handleInputChange('exchangeRate', parseFloat(e.target.value) || 0)}
                      placeholder="0.00000"
                      className="bg-blue-50"
                      disabled={isLoadingExchangeRate}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={form.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value as 'USD' | 'EUR' | 'CNY')}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="CNY">CNY</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SUPPLIER NAME *
                    </label>
                    <Input
                      value={form.supplierName}
                      onChange={(e) => handleInputChange('supplierName', e.target.value)}
                      placeholder="Enter supplier name"
                    />
                  </div>
                </div>

                {/* Deuxième ligne */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Shipment Type *
                    </label>
                    <select
                      value={form.shipmentType}
                      onChange={(e) => handleInputChange('shipmentType', e.target.value as 'SEA' | 'AIR' | 'EXPRESS' | 'OTHER')}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="SEA">SEA</option>
                      <option value="AIR">AIR</option>
                      <option value="EXPRESS">Express</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ORDER NUMBER * (odoo)
                    </label>
                    <Input
                      value={form.orderNumber}
                      onChange={(e) => handleInputChange('orderNumber', e.target.value)}
                      placeholder="Enter order number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      DATE OF ORDER NUMBER *
                    </label>
                    <Input
                      type="date"
                      value={form.dateOfOrderNumber}
                      onChange={(e) => handleInputChange('dateOfOrderNumber', e.target.value)}
                    />
                  </div>
                </div>

                {/* Troisième ligne */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      INVOICE #
                    </label>
                    <Input
                      value={form.invoiceNumber}
                      onChange={(e) => handleInputChange('invoiceNumber', e.target.value)}
                      placeholder="Enter invoice number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Date
                    </label>
                    <Input
                      type="date"
                      value={form.invoiceDate}
                      onChange={(e) => handleInputChange('invoiceDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      FROM
                    </label>
                    <Input
                      value={form.fromLocation}
                      onChange={(e) => handleInputChange('fromLocation', e.target.value)}
                      placeholder="Origin location"
                    />
                  </div>
                </div>

                {/* Quatrième ligne */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      TO
                    </label>
                    <Input
                      value={form.toLocation}
                      onChange={(e) => handleInputChange('toLocation', e.target.value)}
                      placeholder="Destination location"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      OPERATION TYPE
                    </label>
                    <select
                      value={form.operationType}
                      onChange={(e) => handleInputChange('operationType', e.target.value)}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="OPEX">Operational Expenses (OpEx)</option>
                      <option value="INVESTMENT">Investment (or Equipment)</option>
                      <option value="REINVESTMENT">Reinvestment</option>
                      <option value="RAW_MATERIALS">Raw Materials and Semi-finished Products</option>
                      <option value="SPARE_PARTS">Spare Parts (for maintenance or specific resale)</option>
                      <option value="GOODS_RESALE">Goods for Resale as Is</option>
                      <option value="SERVICES">Services (intangible)</option>
                      <option value="TEMPORARY_IMPORT">Temporary Importation (or Temporary Admission)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      DESCRIPTION OF THE GOODS
                    </label>
                    <select
                      value={form.descriptionOfGoods}
                      onChange={(e) => handleInputChange('descriptionOfGoods', e.target.value)}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="SPARE_PARTS_VEHICLES">SPARE PARTS FOR VEHICLES</option>
                      <option value="LUBRICANT">LUBRICANT</option>
                      <option value="ACCESSORY">ACCESSORY</option>
                      <option value="OTHER">OTHER</option>
                    </select>
                  </div>
                </div>

                {/* Cinquième ligne - Banking Information */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">Banking & Payment Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bank Name
                      </label>
                      <Input
                        value={form.bankName}
                        onChange={(e) => handleInputChange('bankName', e.target.value)}
                        placeholder="Enter bank name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        L/C N#
                      </label>
                      <Input
                        value={form.lcNumber}
                        onChange={(e) => handleInputChange('lcNumber', e.target.value)}
                        placeholder="Enter L/C number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Validation L.C
                      </label>
                      <Input
                        value={form.validationLC}
                        onChange={(e) => handleInputChange('validationLC', e.target.value)}
                        placeholder="Enter validation L.C"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PAYMENT TERM
                      </label>
                      <Input
                        value={form.paymentTerm}
                        onChange={(e) => handleInputChange('paymentTerm', e.target.value)}
                        placeholder="Enter payment term"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PRICE TERM
                      </label>
                      <Input
                        value={form.priceTerm}
                        onChange={(e) => handleInputChange('priceTerm', e.target.value)}
                        placeholder="Enter price term"
                      />
                    </div>
                  </div>
                </div>

                {/* Sixième ligne - Quantities & Containers */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">Quantities & Container Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        QUANTITY (PCS)
                      </label>
                      <Input
                        type="number"
                        value={form.quantityPcs}
                        onChange={(e) => handleInputChange('quantityPcs', parseInt(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Number CONTAINERS 20 Pieds
                      </label>
                      <Input
                        type="number"
                        value={form.numberContainers20}
                        onChange={(e) => handleInputChange('numberContainers20', parseInt(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Number CONTAINERS 40 Pieds
                      </label>
                      <Input
                        type="number"
                        value={form.numberContainers40}
                        onChange={(e) => handleInputChange('numberContainers40', parseInt(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Number PACKAGES
                      </label>
                      <Input
                        type="number"
                        value={form.numberPackages}
                        onChange={(e) => handleInputChange('numberPackages', parseInt(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Goods Price in Currency */}
            <Card>
              <CardHeader>
                <CardTitle>Goods Price in Currency ({form.currency})</CardTitle>
                <CardDescription>FOB, Freight and CIF amounts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Prix en devise */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Goods Price in {form.currency}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        FOB AMOUNT
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.fobAmount}
                        onChange={(e) => handleInputChange('fobAmount', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        FREIGHT
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.freight}
                        onChange={(e) => handleInputChange('freight', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TOTAL AMOUNT CIF (FOB + Freight)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.fobAmount + form.freight}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* Conversion to DZD */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">Conversion to DZD (Automatic conversion)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        FOB AMOUNT DZD (Automatic conversion)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.fobAmount * form.exchangeRate}
                        readOnly
                        className="bg-blue-50 text-blue-800 font-medium"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        FREIGHT DZD (Automatic conversion)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.freight * form.exchangeRate}
                        readOnly
                        className="bg-blue-50 text-blue-800 font-medium"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={(form.fobAmount + form.freight) * form.exchangeRate}
                        readOnly
                        className="bg-green-50 text-green-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  <div className="mt-2 text-xs text-gray-500">
                    Exchange Rate Used: {form.exchangeRate.toFixed(5)} DZD/{form.currency}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customs Duties */}
            <Card>
              <CardHeader>
                <CardTitle>Customs Duties</CardTitle>
                <CardDescription>Algeria customs fees and duties</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Cost Allocation Name et D3 Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Customs Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        COST ALLOCATION NAME (ALGERIA CUSTOMS)
                      </label>
                      <Input
                        value={form.costAllocationNameCustoms}
                        onChange={(e) => handleInputChange('costAllocationNameCustoms', e.target.value)}
                        placeholder="ALGERIA CUSTOMS"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        D3 N#
                      </label>
                      <Input
                        value={form.d3Number}
                        onChange={(e) => handleInputChange('d3Number', e.target.value)}
                        placeholder="Enter D3 number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        D3 Date
                      </label>
                      <Input
                        type="date"
                        value={form.d3Date}
                        onChange={(e) => handleInputChange('d3Date', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* QUITTANCE 1 */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">QUITTANCE 1</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties1 DZD Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties1Ttc}
                        onChange={(e) => handleInputChange('customsDuties1Ttc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties1 DZD TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties1Tva}
                        onChange={(e) => handleInputChange('customsDuties1Tva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties1 DZD HT (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties1Ttc - form.customsDuties1Tva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* QUITTANCE 2 */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">QUITTANCE 2</h4>
                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties2 DZD HT (manually entered)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties2Ht}
                        onChange={(e) => handleInputChange('customsDuties2Ht', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className="bg-orange-50 border-orange-300"
                      />
                    </div>
                  </div>
                </div>

                {/* OVERALL TOTALS OF CUSTOMS DUTIES */}
                <div className="border-t pt-4 bg-yellow-50 p-4 rounded-md">
                  <h4 className="font-bold text-gray-900 mb-3">OVERALL TOTALS OF CUSTOMS DUTIES:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties "Overall Totals" DZD Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties1Ttc}
                        readOnly
                        className="bg-yellow-100 text-yellow-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties "Overall Totals" DZD TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsDuties1Tva}
                        readOnly
                        className="bg-yellow-100 text-yellow-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Customs Duties "Overall Totals" DZD HT
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={(form.customsDuties1Ttc - form.customsDuties1Tva) + form.customsDuties2Ht}
                        readOnly
                        className="bg-yellow-100 text-yellow-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Port Fees */}
            <Card>
              <CardHeader>
                <CardTitle>Port Fees</CardTitle>
                <CardDescription>Port and customs inspection fees</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* IMPORT DELIVERY */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (IMPORT DELIVERY)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IMPORT DELIVERY INVOICE N#
                      </label>
                      <Input
                        value={form.importDeliveryInvoiceNumber}
                        onChange={(e) => handleInputChange('importDeliveryInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IMPORT DELIVERY INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.importDeliveryInvoiceDate}
                        onChange={(e) => handleInputChange('importDeliveryInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IMPORT DELIVERY Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.importDeliveryTtc}
                        onChange={(e) => handleInputChange('importDeliveryTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IMPORT DELIVERY TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.importDeliveryTva}
                        onChange={(e) => handleInputChange('importDeliveryTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IMPORT DELIVERY HT (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.importDeliveryTtc - form.importDeliveryTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* CUSTOMS INSPECTION */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (CUSTOMS INSPECTION)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CUSTOMS INSPECTION INVOICE N#
                      </label>
                      <Input
                        value={form.customsInspectionInvoiceNumber}
                        onChange={(e) => handleInputChange('customsInspectionInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CUSTOMS INSPECTION INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.customsInspectionInvoiceDate}
                        onChange={(e) => handleInputChange('customsInspectionInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CUSTOMS INSPECTION Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsInspectionTtc}
                        onChange={(e) => handleInputChange('customsInspectionTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CUSTOMS INSPECTION TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsInspectionTva}
                        onChange={(e) => handleInputChange('customsInspectionTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        CUSTOMS INSPECTION HT (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.customsInspectionTtc - form.customsInspectionTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* OVERALL TOTALS OF PORT FEES */}
                <div className="border-t pt-4 bg-blue-50 p-4 rounded-md">
                  <h4 className="font-bold text-gray-900 mb-3">OVERALL TOTALS OF PORT FEES:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PORT FEES "Overall Totals" DZD Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.importDeliveryTtc + form.customsInspectionTtc}
                        readOnly
                        className="bg-blue-100 text-blue-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PORT FEES "Overall Totals" DZD TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.importDeliveryTva + form.customsInspectionTva}
                        readOnly
                        className="bg-blue-100 text-blue-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PORT FEES "Overall Totals" DZD HT (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={(form.importDeliveryTtc - form.importDeliveryTva) + (form.customsInspectionTtc - form.customsInspectionTva)}
                        readOnly
                        className="bg-blue-100 text-blue-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Company Fees */}
            <Card>
              <CardHeader>
                <CardTitle>Shipping Company Fees</CardTitle>
                <CardDescription>Shipping agency, containers and demurrage fees</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* SHIPPING AGENCY SERVICES */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (SHIPPING AGENCY SERVICES)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING AGENCY SERVICES INVOICE N#
                      </label>
                      <Input
                        value={form.shippingAgencyInvoiceNumber}
                        onChange={(e) => handleInputChange('shippingAgencyInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING AGENCY SERVICES INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.shippingAgencyInvoiceDate}
                        onChange={(e) => handleInputChange('shippingAgencyInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING AGENCY SERVICES Total TTC (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.shippingAgencyTtc}
                        onChange={(e) => handleInputChange('shippingAgencyTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING AGENCY SERVICES TVA (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.shippingAgencyTva}
                        onChange={(e) => handleInputChange('shippingAgencyTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.shippingAgencyTtc - form.shippingAgencyTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* EMPTY CONTAINERS */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (EMPTY CONTAINERS)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EMPTY CONTAINERS RETURN INVOICE N#
                      </label>
                      <Input
                        value={form.emptyContainersInvoiceNumber}
                        onChange={(e) => handleInputChange('emptyContainersInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EMPTY CONTAINERS RETURN INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.emptyContainersInvoiceDate}
                        onChange={(e) => handleInputChange('emptyContainersInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EMPTY CONTAINERS RETURN Total TTC (DZD)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.emptyContainersTtc}
                        onChange={(e) => handleInputChange('emptyContainersTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EMPTY CONTAINERS RETURN TVA (DZD)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.emptyContainersTva}
                        onChange={(e) => handleInputChange('emptyContainersTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.emptyContainersTtc - form.emptyContainersTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>

                {/* DEMURRAGE IF PRESENT */}
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (DEMURRAGE IF PRESENT)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DEMURRAGE INVOICE N#
                      </label>
                      <Input
                        value={form.demurrageInvoiceNumber}
                        onChange={(e) => handleInputChange('demurrageInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DEMURRAGE INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.demurrageInvoiceDate}
                        onChange={(e) => handleInputChange('demurrageInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DEMURRAGE HT (DZD) (This currency field must be entered manually.)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.demurrageHt}
                        onChange={(e) => handleInputChange('demurrageHt', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className="bg-orange-50 border-orange-300"
                      />
                    </div>
                  </div>
                </div>

                {/* OVERALL TOTALS OF SHIPPING COMPANY FEES */}
                <div className="border-t pt-4 bg-green-50 p-4 rounded-md">
                  <h4 className="font-bold text-gray-900 mb-3">OVERALL TOTALS OF SHIPPING COMPANY FEES:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING COMPANY FEES "Overall Totals" DZD Total TTC
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt}
                        readOnly
                        className="bg-green-100 text-green-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING COMPANY FEES "Overall Totals" DZD TVA
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.shippingAgencyTva + form.emptyContainersTva}
                        readOnly
                        className="bg-green-100 text-green-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SHIPPING COMPANY FEES "Overall Totals" DZD HT (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={(form.shippingAgencyTtc - form.shippingAgencyTva) + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt}
                        readOnly
                        className="bg-green-100 text-green-800 font-bold"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Other Miscellaneous Expenses */}
            <Card>
              <CardHeader>
                <CardTitle>Other Miscellaneous Expenses</CardTitle>
                <CardDescription>Various additional costs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        OTHER MISCELLANEOUS EXPENSES INVOICE N#
                      </label>
                      <Input
                        value={form.miscExpensesInvoiceNumber}
                        onChange={(e) => handleInputChange('miscExpensesInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        OTHER MISCELLANEOUS EXPENSES INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.miscExpensesInvoiceDate}
                        onChange={(e) => handleInputChange('miscExpensesInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        OTHER MISCELLANEOUS EXPENSES TTC (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.miscExpensesTtc}
                        onChange={(e) => handleInputChange('miscExpensesTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        OTHER MISCELLANEOUS EXPENSES TVA (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.miscExpensesTva}
                        onChange={(e) => handleInputChange('miscExpensesTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.miscExpensesTtc - form.miscExpensesTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Transit Services Expenses */}
            <Card>
              <CardHeader>
                <CardTitle>Transit Services Expenses</CardTitle>
                <CardDescription>Transit and logistics services</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TRANSIT SERVICES EXPENSES INVOICE N#
                      </label>
                      <Input
                        value={form.transitServicesInvoiceNumber}
                        onChange={(e) => handleInputChange('transitServicesInvoiceNumber', e.target.value)}
                        placeholder="Enter invoice number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TRANSIT SERVICES EXPENSES INVOICE DATE
                      </label>
                      <Input
                        type="date"
                        value={form.transitServicesInvoiceDate}
                        onChange={(e) => handleInputChange('transitServicesInvoiceDate', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TRANSIT SERVICES EXPENSES TTC (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.transitServicesTtc}
                        onChange={(e) => handleInputChange('transitServicesTtc', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TRANSIT SERVICES EXPENSES TVA (DZD) *
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.transitServicesTva}
                        onChange={(e) => handleInputChange('transitServicesTva', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={form.transitServicesTtc - form.transitServicesTva}
                        readOnly
                        className="bg-gray-50 text-gray-600"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </Layout>
  )
}
