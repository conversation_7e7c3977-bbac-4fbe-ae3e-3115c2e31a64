import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Layout from '@/components/layout/Layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ship, Save, FileText } from 'lucide-react'

interface ShipmentReceptionForm {
  // Arrival Notice
  voyageNumber: string
  billOfLading: string
  vesselName: string
  shipowner: string
  actualTimeOfArrival: string

  // General Information
  exchangeRate: number
  currency: 'USD' | 'EUR' | 'CNY'
  supplierName: string
  shipmentType: 'SEA' | 'AIR' | 'EXPRESS' | 'OTHER'
  orderNumber: string
  dateOfOrderNumber: string
  invoiceNumber: string
  invoiceDate: string
  fromLocation: string
  toLocation: string
  operationType: 'OPEX' | 'INVESTMENT' | 'REINVESTMENT' | 'RAW_MATERIALS' | 'SPARE_PARTS' | 'GOODS_RESALE' | 'SERVICES' | 'TEMPORARY_IMPORT'
  descriptionOfGoods: 'SPARE_PARTS_VEHICLES' | 'LUBRICANT' | 'ACCESSORY' | 'OTHER'
  bankName: string
  lcNumber: string
  validationLC: string
  paymentTerm: string
  priceTerm: string
  quantityPcs: number
  numberContainers20: number
  numberContainers40: number
  numberPackages: number

  // Goods Price in Currency
  fobAmount: number
  freight: number
  totalAmountCif: number

  // Conversion to DZD (calculated automatically)
  fobAmountDzd: number
  freightDzd: number
  totalAmountCifDzd: number

  // Customs Duties
  costAllocationNameCustoms: string
  d3Number: string
  d3Date: string

  // Quittance 1
  customsDuties1Ttc: number
  customsDuties1Tva: number
  customsDuties1Ht: number

  // Quittance 2
  customsDuties2Ttc: number
  customsDuties2Tva: number
  customsDuties2Ht: number

  // Overall Totals Customs Duties
  customsDutiesOverallTtc: number
  customsDutiesOverallTva: number
  customsDutiesOverallHt: number

  // Port Fees - Import Delivery
  costAllocationNameImportDelivery: string
  importDeliveryInvoiceNumber: string
  importDeliveryInvoiceDate: string
  importDeliveryTtc: number
  importDeliveryTva: number
  importDeliveryHt: number

  // Port Fees - Customs Inspection
  costAllocationNameCustomsInspection: string
  customsInspectionInvoiceNumber: string
  customsInspectionInvoiceDate: string
  customsInspectionTtc: number
  customsInspectionTva: number
  customsInspectionHt: number

  // Overall Totals Port Fees
  portFeesOverallTtc: number
  portFeesOverallTva: number
  portFeesOverallHt: number

  // Shipping Company Fees - Shipping Agency Services
  costAllocationNameShippingAgency: string
  shippingAgencyInvoiceNumber: string
  shippingAgencyInvoiceDate: string
  shippingAgencyTtc: number
  shippingAgencyTva: number
  shippingAgencyHt: number

  // Shipping Company Fees - Empty Containers
  costAllocationNameEmptyContainers: string
  emptyContainersInvoiceNumber: string
  emptyContainersInvoiceDate: string
  emptyContainersTtc: number
  emptyContainersTva: number
  emptyContainersHt: number

  // Shipping Company Fees - Demurrage
  costAllocationNameDemurrage: string
  demurrageInvoiceNumber: string
  demurrageInvoiceDate: string
  demurrageHt: number

  // Overall Totals Shipping Company Fees
  shippingFeesOverallTtc: number
  shippingFeesOverallTva: number
  shippingFeesOverallHt: number

  // Other Miscellaneous Expenses
  costAllocationNameMiscExpenses: string
  miscExpensesInvoiceNumber: string
  miscExpensesInvoiceDate: string
  miscExpensesTtc: number
  miscExpensesTva: number
  miscExpensesHt: number

  // Transit Services Expenses
  costAllocationNameTransitServices: string
  transitServicesInvoiceNumber: string
  transitServicesInvoiceDate: string
  transitServicesTtc: number
  transitServicesTva: number
  transitServicesHt: number
}

const defaultForm: ShipmentReceptionForm = {
  // Arrival Notice
  voyageNumber: '',
  billOfLading: '',
  vesselName: '',
  shipowner: '',
  actualTimeOfArrival: '',

  // General Information
  exchangeRate: 0, // Sera récupéré depuis les Settings de l'admin
  currency: 'USD',
  supplierName: '',
  shipmentType: 'SEA',
  orderNumber: '',
  dateOfOrderNumber: '',
  invoiceNumber: '',
  invoiceDate: '',
  fromLocation: '',
  toLocation: '',
  operationType: 'OPEX',
  descriptionOfGoods: 'SPARE_PARTS_VEHICLES',
  bankName: '',
  lcNumber: '',
  validationLC: '',
  paymentTerm: '',
  priceTerm: '',
  quantityPcs: 0,
  numberContainers20: 0,
  numberContainers40: 0,
  numberPackages: 0,

  // Goods Price in Currency
  fobAmount: 0,
  freight: 0,
  totalAmountCif: 0,

  // Conversion to DZD (calculated automatically)
  fobAmountDzd: 0,
  freightDzd: 0,
  totalAmountCifDzd: 0,

  // Customs Duties
  costAllocationNameCustoms: 'ALGERIA CUSTOMS',
  d3Number: '',
  d3Date: '',

  // Quittance 1
  customsDuties1Ttc: 0,
  customsDuties1Tva: 0,
  customsDuties1Ht: 0,

  // Quittance 2
  customsDuties2Ttc: 0,
  customsDuties2Tva: 0,
  customsDuties2Ht: 0,

  // Overall Totals Customs Duties
  customsDutiesOverallTtc: 0,
  customsDutiesOverallTva: 0,
  customsDutiesOverallHt: 0,

  // Port Fees - Import Delivery
  costAllocationNameImportDelivery: 'IMPORT DELIVERY',
  importDeliveryInvoiceNumber: '',
  importDeliveryInvoiceDate: '',
  importDeliveryTtc: 0,
  importDeliveryTva: 0,
  importDeliveryHt: 0,

  // Port Fees - Customs Inspection
  costAllocationNameCustomsInspection: 'CUSTOMS INSPECTION',
  customsInspectionInvoiceNumber: '',
  customsInspectionInvoiceDate: '',
  customsInspectionTtc: 0,
  customsInspectionTva: 0,
  customsInspectionHt: 0,

  // Overall Totals Port Fees
  portFeesOverallTtc: 0,
  portFeesOverallTva: 0,
  portFeesOverallHt: 0,

  // Shipping Company Fees - Shipping Agency Services
  costAllocationNameShippingAgency: 'SHIPPING AGENCY SERVICES',
  shippingAgencyInvoiceNumber: '',
  shippingAgencyInvoiceDate: '',
  shippingAgencyTtc: 0,
  shippingAgencyTva: 0,
  shippingAgencyHt: 0,

  // Shipping Company Fees - Empty Containers
  costAllocationNameEmptyContainers: 'EMPTY CONTAINERS',
  emptyContainersInvoiceNumber: '',
  emptyContainersInvoiceDate: '',
  emptyContainersTtc: 0,
  emptyContainersTva: 0,
  emptyContainersHt: 0,

  // Shipping Company Fees - Demurrage
  costAllocationNameDemurrage: 'DEMURRAGE IF PRESENT',
  demurrageInvoiceNumber: '',
  demurrageInvoiceDate: '',
  demurrageHt: 0,

  // Overall Totals Shipping Company Fees
  shippingFeesOverallTtc: 0,
  shippingFeesOverallTva: 0,
  shippingFeesOverallHt: 0,

  // Other Miscellaneous Expenses
  costAllocationNameMiscExpenses: 'OTHER MISCELLANEOUS EXPENSES',
  miscExpensesInvoiceNumber: '',
  miscExpensesInvoiceDate: '',
  miscExpensesTtc: 0,
  miscExpensesTva: 0,
  miscExpensesHt: 0,

  // Transit Services Expenses
  costAllocationNameTransitServices: 'TRANSIT SERVICES EXPENSES',
  transitServicesInvoiceNumber: '',
  transitServicesInvoiceDate: '',
  transitServicesTtc: 0,
  transitServicesTva: 0,
  transitServicesHt: 0
}

export default function ShipmentReception() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [form, setForm] = useState<ShipmentReceptionForm>(defaultForm)
  const [currentExchangeRate, setCurrentExchangeRate] = useState<number>(0)
  const [isLoadingExchangeRate, setIsLoadingExchangeRate] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Récupérer le taux de change depuis les Settings de l'admin
  useEffect(() => {
    const fetchExchangeRate = async () => {
      try {
        setIsLoadingExchangeRate(true)
        // TODO: Remplacer par l'API réelle des Settings
        // const response = await fetch('/api/settings/exchange-rate')
        // const data = await response.json()

        // Pour l'instant, simulation avec localStorage ou valeur par défaut
        const savedRate = localStorage.getItem('admin-exchange-rate')
        const rate = savedRate ? parseFloat(savedRate) : 134.50000

        setCurrentExchangeRate(rate)
        setForm(prev => ({
          ...prev,
          exchangeRate: rate
        }))
      } catch (error) {
        console.error('Error fetching exchange rate:', error)
        // Valeur de fallback
        const fallbackRate = 134.50000
        setCurrentExchangeRate(fallbackRate)
        setForm(prev => ({
          ...prev,
          exchangeRate: fallbackRate
        }))
      } finally {
        setIsLoadingExchangeRate(false)
      }
    }

    if (status === 'authenticated') {
      fetchExchangeRate()
    }
  }, [status])

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Shipment Reception</h1>
            <p className="text-gray-600">Record merchandise reception from arrival notice to container release</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              Save Reception
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          {/* Main Form */}
          <div className="space-y-6">

            {/* Arrival Notice Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Ship className="h-5 w-5" />
                  Arrival Notice
                </CardTitle>
                <CardDescription>Vessel arrival and shipping information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Voyage Number - "CALL AT PORT" (ESCALE) N°
                    </label>
                    <Input
                      placeholder="Enter voyage number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bill of Lading (B/L) (Connaissement) N°
                    </label>
                    <Input
                      placeholder="Enter B/L number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vessel Name (Navire)
                    </label>
                    <Input
                      placeholder="Enter vessel name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Shipowner (Armateur)
                    </label>
                    <Input
                      placeholder="Enter shipowner name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Actual Time of Arrival (Date Accostage)
                    </label>
                    <Input
                      type="datetime-local"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* General Information Section */}
            <Card>
              <CardHeader>
                <CardTitle>General Information</CardTitle>
                <CardDescription>Basic shipment and order details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exchange Rate Used : DZD/(CURRENCY) *
                    </label>
                    <Input
                      type="number"
                      step="0.00001"
                      placeholder="134.50000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="CNY">CNY</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SUPPLIER NAME *
                    </label>
                    <Input
                      placeholder="Enter supplier name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Shipment Type *
                    </label>
                    <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                      <option value="SEA">SEA</option>
                      <option value="AIR">AIR</option>
                      <option value="EXPRESS">Express</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ORDER NUMBER * (odoo)
                    </label>
                    <Input
                      placeholder="Enter order number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      DATE OF ORDER NUMBER *
                    </label>
                    <Input
                      type="date"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Placeholder for more sections */}
            <Card>
              <CardHeader>
                <CardTitle>More Sections Coming...</CardTitle>
                <CardDescription>Invoice details, operation type, goods description, etc.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Les autres sections seront ajoutées dans les prochaines étapes...</p>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </Layout>
  )
}
