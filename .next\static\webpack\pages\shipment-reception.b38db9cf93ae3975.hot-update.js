"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/shipment-reception",{

/***/ "./pages/shipment-reception.tsx":
/*!**************************************!*\
  !*** ./pages/shipment-reception.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShipmentReception; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Save,Ship!=!lucide-react */ \"__barrel_optimize__?names=FileText,Save,Ship!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst defaultForm = {\n    // Arrival Notice\n    voyageNumber: \"\",\n    billOfLading: \"\",\n    vesselName: \"\",\n    shipowner: \"\",\n    actualTimeOfArrival: \"\",\n    // General Information\n    exchangeRate: 0,\n    currency: \"USD\",\n    supplierName: \"\",\n    shipmentType: \"SEA\",\n    orderNumber: \"\",\n    dateOfOrderNumber: \"\",\n    invoiceNumber: \"\",\n    invoiceDate: \"\",\n    fromLocation: \"\",\n    toLocation: \"\",\n    operationType: \"OPEX\",\n    descriptionOfGoods: \"SPARE_PARTS_VEHICLES\",\n    bankName: \"\",\n    lcNumber: \"\",\n    validationLC: \"\",\n    paymentTerm: \"\",\n    priceTerm: \"\",\n    quantityPcs: 0,\n    numberContainers20: 0,\n    numberContainers40: 0,\n    numberPackages: 0,\n    // Goods Price in Currency\n    fobAmount: 0,\n    freight: 0,\n    totalAmountCif: 0,\n    // Conversion to DZD (calculated automatically)\n    fobAmountDzd: 0,\n    freightDzd: 0,\n    totalAmountCifDzd: 0,\n    // Customs Duties\n    costAllocationNameCustoms: \"ALGERIA CUSTOMS\",\n    d3Number: \"\",\n    d3Date: \"\",\n    // Quittance 1\n    customsDuties1Ttc: 0,\n    customsDuties1Tva: 0,\n    customsDuties1Ht: 0,\n    // Quittance 2\n    customsDuties2Ttc: 0,\n    customsDuties2Tva: 0,\n    customsDuties2Ht: 0,\n    // Overall Totals Customs Duties\n    customsDutiesOverallTtc: 0,\n    customsDutiesOverallTva: 0,\n    customsDutiesOverallHt: 0,\n    // Port Fees - Import Delivery\n    costAllocationNameImportDelivery: \"IMPORT DELIVERY\",\n    importDeliveryInvoiceNumber: \"\",\n    importDeliveryInvoiceDate: \"\",\n    importDeliveryTtc: 0,\n    importDeliveryTva: 0,\n    importDeliveryHt: 0,\n    // Port Fees - Customs Inspection\n    costAllocationNameCustomsInspection: \"CUSTOMS INSPECTION\",\n    customsInspectionInvoiceNumber: \"\",\n    customsInspectionInvoiceDate: \"\",\n    customsInspectionTtc: 0,\n    customsInspectionTva: 0,\n    customsInspectionHt: 0,\n    // Overall Totals Port Fees\n    portFeesOverallTtc: 0,\n    portFeesOverallTva: 0,\n    portFeesOverallHt: 0,\n    // Shipping Company Fees - Shipping Agency Services\n    costAllocationNameShippingAgency: \"SHIPPING AGENCY SERVICES\",\n    shippingAgencyInvoiceNumber: \"\",\n    shippingAgencyInvoiceDate: \"\",\n    shippingAgencyTtc: 0,\n    shippingAgencyTva: 0,\n    shippingAgencyHt: 0,\n    // Shipping Company Fees - Empty Containers\n    costAllocationNameEmptyContainers: \"EMPTY CONTAINERS\",\n    emptyContainersInvoiceNumber: \"\",\n    emptyContainersInvoiceDate: \"\",\n    emptyContainersTtc: 0,\n    emptyContainersTva: 0,\n    emptyContainersHt: 0,\n    // Shipping Company Fees - Demurrage\n    costAllocationNameDemurrage: \"DEMURRAGE IF PRESENT\",\n    demurrageInvoiceNumber: \"\",\n    demurrageInvoiceDate: \"\",\n    demurrageHt: 0,\n    // Overall Totals Shipping Company Fees\n    shippingFeesOverallTtc: 0,\n    shippingFeesOverallTva: 0,\n    shippingFeesOverallHt: 0,\n    // Other Miscellaneous Expenses\n    costAllocationNameMiscExpenses: \"OTHER MISCELLANEOUS EXPENSES\",\n    miscExpensesInvoiceNumber: \"\",\n    miscExpensesInvoiceDate: \"\",\n    miscExpensesTtc: 0,\n    miscExpensesTva: 0,\n    miscExpensesHt: 0,\n    // Transit Services Expenses\n    costAllocationNameTransitServices: \"TRANSIT SERVICES EXPENSES\",\n    transitServicesInvoiceNumber: \"\",\n    transitServicesInvoiceDate: \"\",\n    transitServicesTtc: 0,\n    transitServicesTva: 0,\n    transitServicesHt: 0\n};\nfunction ShipmentReception() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultForm);\n    const [currentExchangeRate, setCurrentExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoadingExchangeRate, setIsLoadingExchangeRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Récupérer le taux de change depuis les Settings de l'admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchExchangeRate = async ()=>{\n            try {\n                setIsLoadingExchangeRate(true);\n                // TODO: Remplacer par l'API réelle des Settings\n                // const response = await fetch('/api/settings/exchange-rate')\n                // const data = await response.json()\n                // Pour l'instant, simulation avec localStorage ou valeur par défaut\n                const savedRate = localStorage.getItem(\"admin-exchange-rate\");\n                const rate = savedRate ? parseFloat(savedRate) : 134.50000;\n                setCurrentExchangeRate(rate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: rate\n                    }));\n            } catch (error) {\n                console.error(\"Error fetching exchange rate:\", error);\n                // Valeur de fallback\n                const fallbackRate = 134.50000;\n                setCurrentExchangeRate(fallbackRate);\n                setForm((prev)=>({\n                        ...prev,\n                        exchangeRate: fallbackRate\n                    }));\n            } finally{\n                setIsLoadingExchangeRate(false);\n            }\n        };\n        if (status === \"authenticated\") {\n            fetchExchangeRate();\n        }\n    }, [\n        status\n    ]);\n    const handleInputChange = (field, value)=>{\n        setForm((prev)=>{\n            const updated = {\n                ...prev,\n                [field]: value\n            };\n            // Calculs automatiques pour les conversions DZD\n            if (field === \"fobAmount\" || field === \"exchangeRate\") {\n                updated.fobAmountDzd = updated.fobAmount * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            if (field === \"freight\" || field === \"exchangeRate\") {\n                updated.freightDzd = updated.freight * updated.exchangeRate;\n                updated.totalAmountCif = updated.fobAmount + updated.freight;\n                updated.totalAmountCifDzd = updated.totalAmountCif * updated.exchangeRate;\n            }\n            // Calculs automatiques pour les champs HT\n            if (field === \"customsDuties1Ttc\" || field === \"customsDuties1Tva\") {\n                updated.customsDuties1Ht = updated.customsDuties1Ttc - updated.customsDuties1Tva;\n            }\n            // Calculs des totaux Customs Duties\n            updated.customsDutiesOverallTtc = updated.customsDuties1Ttc;\n            updated.customsDutiesOverallTva = updated.customsDuties1Tva;\n            updated.customsDutiesOverallHt = updated.customsDuties1Ttc - updated.customsDuties1Tva + updated.customsDuties2Ht;\n            // Calculs automatiques Port Fees\n            if (field === \"importDeliveryTtc\" || field === \"importDeliveryTva\") {\n                updated.importDeliveryHt = updated.importDeliveryTtc - updated.importDeliveryTva;\n            }\n            if (field === \"customsInspectionTtc\" || field === \"customsInspectionTva\") {\n                updated.customsInspectionHt = updated.customsInspectionTtc - updated.customsInspectionTva;\n            }\n            // Calculs des totaux Port Fees\n            updated.portFeesOverallTtc = updated.importDeliveryTtc + updated.customsInspectionTtc;\n            updated.portFeesOverallTva = updated.importDeliveryTva + updated.customsInspectionTva;\n            updated.portFeesOverallHt = updated.importDeliveryHt + updated.customsInspectionHt;\n            // Calculs automatiques Shipping Fees\n            if (field === \"shippingAgencyTtc\" || field === \"shippingAgencyTva\") {\n                updated.shippingAgencyHt = updated.shippingAgencyTtc - updated.shippingAgencyTva;\n            }\n            if (field === \"emptyContainersTtc\" || field === \"emptyContainersTva\") {\n                updated.emptyContainersHt = updated.emptyContainersTtc - updated.emptyContainersTva;\n            }\n            // Calculs des totaux Shipping Fees\n            updated.shippingFeesOverallTtc = updated.shippingAgencyTtc + updated.emptyContainersTtc + updated.demurrageHt;\n            updated.shippingFeesOverallTva = updated.shippingAgencyTva + updated.emptyContainersTva;\n            updated.shippingFeesOverallHt = updated.shippingAgencyHt + updated.emptyContainersHt + updated.demurrageHt;\n            // Calculs automatiques Misc Expenses\n            if (field === \"miscExpensesTtc\" || field === \"miscExpensesTva\") {\n                updated.miscExpensesHt = updated.miscExpensesTtc - updated.miscExpensesTva;\n            }\n            // Calculs automatiques Transit Services\n            if (field === \"transitServicesTtc\" || field === \"transitServicesTva\") {\n                updated.transitServicesHt = updated.transitServicesTtc - updated.transitServicesTva;\n            }\n            return updated;\n        });\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                lineNumber: 387,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 386,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Shipment Reception\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Record merchandise reception from arrival notice to container release\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save Reception\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-1 gap-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Save_Ship_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Ship, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Arrival Notice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Vessel arrival and shipping information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: 'Voyage Number - \"CALL AT PORT\" (ESCALE) N\\xb0'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.voyageNumber,\n                                                            onChange: (e)=>handleInputChange(\"voyageNumber\", e.target.value),\n                                                            placeholder: \"Enter voyage number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Bill of Lading (B/L) (Connaissement) N\\xb0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.billOfLading,\n                                                            onChange: (e)=>handleInputChange(\"billOfLading\", e.target.value),\n                                                            placeholder: \"Enter B/L number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Vessel Name (Navire)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.vesselName,\n                                                            onChange: (e)=>handleInputChange(\"vesselName\", e.target.value),\n                                                            placeholder: \"Enter vessel name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Shipowner (Armateur)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            value: form.shipowner,\n                                                            onChange: (e)=>handleInputChange(\"shipowner\", e.target.value),\n                                                            placeholder: \"Enter shipowner name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Actual Time of Arrival (Date Accostage)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            type: \"datetime-local\",\n                                                            value: form.actualTimeOfArrival,\n                                                            onChange: (e)=>handleInputChange(\"actualTimeOfArrival\", e.target.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"General Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Basic shipment and order details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: [\n                                                                    \"Exchange Rate Used : DZD/\",\n                                                                    form.currency,\n                                                                    \" *\",\n                                                                    isLoadingExchangeRate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-blue-600 ml-2\",\n                                                                        children: \"(Loading...)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.00001\",\n                                                                value: form.exchangeRate,\n                                                                onChange: (e)=>handleInputChange(\"exchangeRate\", parseFloat(e.target.value) || 0),\n                                                                placeholder: \"0.00000\",\n                                                                className: \"bg-blue-50\",\n                                                                disabled: isLoadingExchangeRate\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.currency,\n                                                                onChange: (e)=>handleInputChange(\"currency\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CNY\",\n                                                                        children: \"CNY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"SUPPLIER NAME *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.supplierName,\n                                                                onChange: (e)=>handleInputChange(\"supplierName\", e.target.value),\n                                                                placeholder: \"Enter supplier name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Shipment Type *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.shipmentType,\n                                                                onChange: (e)=>handleInputChange(\"shipmentType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SEA\",\n                                                                        children: \"SEA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"AIR\",\n                                                                        children: \"AIR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EXPRESS\",\n                                                                        children: \"Express\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"ORDER NUMBER * (odoo)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.orderNumber,\n                                                                onChange: (e)=>handleInputChange(\"orderNumber\", e.target.value),\n                                                                placeholder: \"Enter order number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DATE OF ORDER NUMBER *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.dateOfOrderNumber,\n                                                                onChange: (e)=>handleInputChange(\"dateOfOrderNumber\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"INVOICE #\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.invoiceNumber,\n                                                                onChange: (e)=>handleInputChange(\"invoiceNumber\", e.target.value),\n                                                                placeholder: \"Enter invoice number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Invoice Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                type: \"date\",\n                                                                value: form.invoiceDate,\n                                                                onChange: (e)=>handleInputChange(\"invoiceDate\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"FROM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.fromLocation,\n                                                                onChange: (e)=>handleInputChange(\"fromLocation\", e.target.value),\n                                                                placeholder: \"Origin location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"TO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                value: form.toLocation,\n                                                                onChange: (e)=>handleInputChange(\"toLocation\", e.target.value),\n                                                                placeholder: \"Destination location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"OPERATION TYPE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.operationType,\n                                                                onChange: (e)=>handleInputChange(\"operationType\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OPEX\",\n                                                                        children: \"Operational Expenses (OpEx)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"INVESTMENT\",\n                                                                        children: \"Investment (or Equipment)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"REINVESTMENT\",\n                                                                        children: \"Reinvestment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"RAW_MATERIALS\",\n                                                                        children: \"Raw Materials and Semi-finished Products\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS\",\n                                                                        children: \"Spare Parts (for maintenance or specific resale)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"GOODS_RESALE\",\n                                                                        children: \"Goods for Resale as Is\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SERVICES\",\n                                                                        children: \"Services (intangible)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"TEMPORARY_IMPORT\",\n                                                                        children: \"Temporary Importation (or Temporary Admission)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"DESCRIPTION OF THE GOODS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: form.descriptionOfGoods,\n                                                                onChange: (e)=>handleInputChange(\"descriptionOfGoods\", e.target.value),\n                                                                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SPARE_PARTS_VEHICLES\",\n                                                                        children: \"SPARE PARTS FOR VEHICLES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LUBRICANT\",\n                                                                        children: \"LUBRICANT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ACCESSORY\",\n                                                                        children: \"ACCESSORY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 649,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"OTHER\",\n                                                                        children: \"OTHER\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Banking & Payment Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Bank Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.bankName,\n                                                                        onChange: (e)=>handleInputChange(\"bankName\", e.target.value),\n                                                                        placeholder: \"Enter bank name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"L/C N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.lcNumber,\n                                                                        onChange: (e)=>handleInputChange(\"lcNumber\", e.target.value),\n                                                                        placeholder: \"Enter L/C number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.validationLC,\n                                                                        onChange: (e)=>handleInputChange(\"validationLC\", e.target.value),\n                                                                        placeholder: \"Enter validation L.C\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PAYMENT TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.paymentTerm,\n                                                                        onChange: (e)=>handleInputChange(\"paymentTerm\", e.target.value),\n                                                                        placeholder: \"Enter payment term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"PRICE TERM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 702,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.priceTerm,\n                                                                        onChange: (e)=>handleInputChange(\"priceTerm\", e.target.value),\n                                                                        placeholder: \"Enter price term\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Quantities & Container Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"QUANTITY (PCS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.quantityPcs,\n                                                                        onChange: (e)=>handleInputChange(\"quantityPcs\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 20 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers20,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers20\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number CONTAINERS 40 Pieds\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberContainers40,\n                                                                        onChange: (e)=>handleInputChange(\"numberContainers40\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Number PACKAGES\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        value: form.numberPackages,\n                                                                        onChange: (e)=>handleInputChange(\"numberPackages\", parseInt(e.target.value) || 0),\n                                                                        placeholder: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: [\n                                                    \"Goods Price in Currency (\",\n                                                    form.currency,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"FOB, Freight and CIF amounts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: [\n                                                            \"Goods Price in \",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount,\n                                                                        onChange: (e)=>handleInputChange(\"fobAmount\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight,\n                                                                        onChange: (e)=>handleInputChange(\"freight\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF (FOB + Freight)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount + form.freight,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Conversion to DZD (Automatic conversion)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FOB AMOUNT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.fobAmount * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"FREIGHT DZD (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.freight * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-50 text-blue-800 font-medium\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 849,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: (form.fobAmount + form.freight) * form.exchangeRate,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-50 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Exchange Rate Used: \",\n                                                            form.exchangeRate.toFixed(5),\n                                                            \" DZD/\",\n                                                            form.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Customs Duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Algeria customs fees and duties\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"Customs Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"COST ALLOCATION NAME (ALGERIA CUSTOMS)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 881,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.costAllocationNameCustoms,\n                                                                        onChange: (e)=>handleInputChange(\"costAllocationNameCustoms\", e.target.value),\n                                                                        placeholder: \"ALGERIA CUSTOMS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.d3Number,\n                                                                        onChange: (e)=>handleInputChange(\"d3Number\", e.target.value),\n                                                                        placeholder: \"Enter D3 number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"D3 Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 901,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.d3Date,\n                                                                        onChange: (e)=>handleInputChange(\"d3Date\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 918,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Ttc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 930,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        onChange: (e)=>handleInputChange(\"customsDuties1Tva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"Customs Duties1 DZD HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 942,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 945,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"QUITTANCE 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-1 gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Customs Duties2 DZD HT (manually entered)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.customsDuties2Ht,\n                                                                    onChange: (e)=>handleInputChange(\"customsDuties2Ht\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"bg-orange-50 border-orange-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-yellow-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF CUSTOMS DUTIES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 981,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Tva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'Customs Duties \"Overall Totals\" DZD HT'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsDuties1Ttc - form.customsDuties1Tva + form.customsDuties2Ht,\n                                                                        readOnly: true,\n                                                                        className: \"bg-yellow-100 text-yellow-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 870,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Port Fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Port and customs inspection fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (IMPORT DELIVERY)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1037,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.importDeliveryInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1040,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1047,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.importDeliveryInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1050,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1046,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1071,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva,\n                                                                        onChange: (e)=>handleInputChange(\"importDeliveryTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"IMPORT DELIVERY HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1083,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (CUSTOMS INSPECTION)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1103,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.customsInspectionInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.customsInspectionInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1116,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1112,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION Total TTC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1125,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION TVA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1137,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTva,\n                                                                        onChange: (e)=>handleInputChange(\"customsInspectionTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1140,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"CUSTOMS INSPECTION HT (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1149,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.customsInspectionTtc - form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1152,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1148,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-blue-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF PORT FEES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc + form.customsInspectionTtc,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTva + form.customsInspectionTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1185,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'PORT FEES \"Overall Totals\" DZD HT (TTC - TVA)'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1195,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.importDeliveryTtc - form.importDeliveryTva + (form.customsInspectionTtc - form.customsInspectionTva),\n                                                                        readOnly: true,\n                                                                        className: \"bg-blue-100 text-blue-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1198,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Shipping Company Fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Shipping agency, containers and demurrage fees\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (SHIPPING AGENCY SERVICES)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1224,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.shippingAgencyInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1227,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1223,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.shippingAgencyInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES Total TTC (DZD) *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1246,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES TVA (DZD) *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1258,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTva,\n                                                                        onChange: (e)=>handleInputChange(\"shippingAgencyTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1261,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1270,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc - form.shippingAgencyTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1273,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1269,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (EMPTY CONTAINERS)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1290,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.emptyContainersInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1293,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1300,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.emptyContainersInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1303,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN Total TTC (DZD)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTtc,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersTtc\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1315,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN TVA (DZD)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTva,\n                                                                        onChange: (e)=>handleInputChange(\"emptyContainersTva\", parseFloat(e.target.value) || 0),\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1327,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1323,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.emptyContainersTtc - form.emptyContainersTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-gray-50 text-gray-600\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"COST ALLOCATION NAME (DEMURRAGE IF PRESENT)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"DEMURRAGE INVOICE N#\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1356,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        value: form.demurrageInvoiceNumber,\n                                                                        onChange: (e)=>handleInputChange(\"demurrageInvoiceNumber\", e.target.value),\n                                                                        placeholder: \"Enter invoice number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: \"DEMURRAGE INVOICE DATE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"date\",\n                                                                        value: form.demurrageInvoiceDate,\n                                                                        onChange: (e)=>handleInputChange(\"demurrageInvoiceDate\", e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"DEMURRAGE HT (DZD) (This currency field must be entered manually.)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.demurrageHt,\n                                                                    onChange: (e)=>handleInputChange(\"demurrageHt\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\",\n                                                                    className: \"bg-orange-50 border-orange-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1381,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 bg-green-50 p-4 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 mb-3\",\n                                                        children: \"OVERALL TOTALS OF SHIPPING COMPANY FEES:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD Total TTC'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc + form.emptyContainersTtc + form.demurrageHt,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1401,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1397,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD TVA'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1411,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTva + form.emptyContainersTva,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1414,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                        children: 'SHIPPING COMPANY FEES \"Overall Totals\" DZD HT (TTC - TVA)'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1424,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: form.shippingAgencyTtc - form.shippingAgencyTva + (form.emptyContainersTtc - form.emptyContainersTva) + form.demurrageHt,\n                                                                        readOnly: true,\n                                                                        className: \"bg-green-100 text-green-800 font-bold\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                        lineNumber: 1427,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                        lineNumber: 1396,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Other Miscellaneous Expenses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Various additional costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES INVOICE N#\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1452,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    value: form.miscExpensesInvoiceNumber,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesInvoiceNumber\", e.target.value),\n                                                                    placeholder: \"Enter invoice number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1455,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1451,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES INVOICE DATE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1462,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"date\",\n                                                                    value: form.miscExpensesInvoiceDate,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesInvoiceDate\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1465,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1450,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES TTC (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1474,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTtc,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesTtc\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1477,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES TVA (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1486,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTva,\n                                                                    onChange: (e)=>handleInputChange(\"miscExpensesTva\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1489,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1498,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.miscExpensesTtc - form.miscExpensesTva,\n                                                                    readOnly: true,\n                                                                    className: \"bg-gray-50 text-gray-600\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1501,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1497,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 1448,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1447,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Transit Services Expenses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1518,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Transit and logistics services\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                lineNumber: 1519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1523,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES INVOICE N#\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1526,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    value: form.transitServicesInvoiceNumber,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesInvoiceNumber\", e.target.value),\n                                                                    placeholder: \"Enter invoice number\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1529,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES INVOICE DATE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1536,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"date\",\n                                                                    value: form.transitServicesInvoiceDate,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesInvoiceDate\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1539,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1535,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1524,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES TTC (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1548,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTtc,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesTtc\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1551,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1547,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES TVA (DZD) *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1560,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTva,\n                                                                    onChange: (e)=>handleInputChange(\"transitServicesTva\", parseFloat(e.target.value) || 0),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1563,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: form.transitServicesTtc - form.transitServicesTva,\n                                                                    readOnly: true,\n                                                                    className: \"bg-gray-50 text-gray-600\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                            lineNumber: 1571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                                    lineNumber: 1546,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                            lineNumber: 1522,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                        lineNumber: 1521,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\shipment-reception.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s(ShipmentReception, \"VXikbBX57EuFTz5bPw9OPhzTG9I=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ShipmentReception;\nvar _c;\n$RefreshReg$(_c, \"ShipmentReception\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/shipment-reception.tsx\n"));

/***/ })

});