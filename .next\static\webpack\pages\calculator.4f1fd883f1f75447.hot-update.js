"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/calculator",{

/***/ "./pages/calculator.tsx":
/*!******************************!*\
  !*** ./pages/calculator.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Calculator,CheckCircle,Download,Edit,Eye,FileText,RefreshCw,Save,Search,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Calculator() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [invoiceNumber, setInvoiceNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [receptionData, setReceptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedCosts, setGeneratedCosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allReceptions, setAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAllReceptions, setShowAllReceptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Charger toutes les réceptions au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllReceptions();\n    }, []);\n    const loadAllReceptions = async ()=>{\n        try {\n            // Charger depuis la base de données via l'API\n            const response = await fetch(\"/api/shipment-receptions\");\n            if (!response.ok) {\n                throw new Error(\"Erreur lors du chargement des r\\xe9ceptions\");\n            }\n            const savedReceptions = await response.json();\n            const mappedReceptions = savedReceptions.map((reception)=>({\n                    id: reception.id,\n                    supplierName: reception.supplier_name,\n                    orderNumber: reception.order_number,\n                    invoiceNumber: reception.invoice_number,\n                    exchangeRate: reception.exchange_rate,\n                    currency: reception.currency,\n                    fobAmount: reception.fob_amount,\n                    freight: reception.freight,\n                    customsDuties1Ttc: reception.customs_duties_1_ttc,\n                    customsDuties1Tva: reception.customs_duties_1_tva,\n                    customsDuties1Ht: reception.customs_duties_1_ttc - reception.customs_duties_1_tva,\n                    customsDuties2Ht: reception.customs_duties_2_ht,\n                    importDeliveryTtc: reception.import_delivery_ttc,\n                    importDeliveryTva: reception.import_delivery_tva,\n                    importDeliveryHt: reception.import_delivery_ttc - reception.import_delivery_tva,\n                    customsInspectionTtc: reception.customs_inspection_ttc,\n                    customsInspectionTva: reception.customs_inspection_tva,\n                    customsInspectionHt: reception.customs_inspection_ttc - reception.customs_inspection_tva,\n                    shippingAgencyTtc: reception.shipping_agency_ttc,\n                    shippingAgencyTva: reception.shipping_agency_tva,\n                    shippingAgencyHt: reception.shipping_agency_ttc - reception.shipping_agency_tva,\n                    emptyContainersTtc: reception.empty_containers_ttc,\n                    emptyContainersTva: reception.empty_containers_tva,\n                    emptyContainersHt: reception.empty_containers_ttc - reception.empty_containers_tva,\n                    demurrageHt: reception.demurrage_ht,\n                    miscExpensesTtc: reception.misc_expenses_ttc,\n                    miscExpensesTva: reception.misc_expenses_tva,\n                    miscExpensesHt: reception.misc_expenses_ttc - reception.misc_expenses_tva,\n                    transitServicesTtc: reception.transit_services_ttc,\n                    transitServicesTva: reception.transit_services_tva,\n                    transitServicesHt: reception.transit_services_ttc - reception.transit_services_tva,\n                    createdAt: reception.created_at\n                }));\n            setAllReceptions(mappedReceptions);\n        } catch (error) {\n            console.error(\"Error loading receptions:\", error);\n            setError(\"Erreur lors du chargement des r\\xe9ceptions depuis la base de donn\\xe9es\");\n        }\n    };\n    const searchReception = async ()=>{\n        if (!invoiceNumber.trim()) {\n            setError(\"Veuillez saisir un num\\xe9ro de facture\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setReceptionData(null);\n        try {\n            // Rechercher dans les réceptions sauvegardées (localStorage pour l'instant)\n            const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n            const reception = savedReceptions.find((r)=>r.invoiceNumber === invoiceNumber.trim());\n            if (!reception) {\n                setError(\"Aucune r\\xe9ception trouv\\xe9e pour la facture: \".concat(invoiceNumber));\n                setIsLoading(false);\n                return;\n            }\n            // Mapper les données de réception vers notre interface\n            const mappedReception = {\n                id: reception.id,\n                supplierName: reception.supplierName,\n                orderNumber: reception.orderNumber,\n                invoiceNumber: reception.invoiceNumber,\n                exchangeRate: reception.exchangeRate,\n                currency: reception.currency,\n                fobAmount: reception.fobAmount,\n                freight: reception.freight,\n                customsDuties1Ttc: reception.customsDuties1Ttc,\n                customsDuties1Tva: reception.customsDuties1Tva,\n                customsDuties1Ht: reception.customsDuties1Ttc - reception.customsDuties1Tva,\n                customsDuties2Ht: reception.customsDuties2Ht,\n                importDeliveryTtc: reception.importDeliveryTtc,\n                importDeliveryTva: reception.importDeliveryTva,\n                importDeliveryHt: reception.importDeliveryTtc - reception.importDeliveryTva,\n                customsInspectionTtc: reception.customsInspectionTtc,\n                customsInspectionTva: reception.customsInspectionTva,\n                customsInspectionHt: reception.customsInspectionTtc - reception.customsInspectionTva,\n                shippingAgencyTtc: reception.shippingAgencyTtc,\n                shippingAgencyTva: reception.shippingAgencyTva,\n                shippingAgencyHt: reception.shippingAgencyTtc - reception.shippingAgencyTva,\n                emptyContainersTtc: reception.emptyContainersTtc,\n                emptyContainersTva: reception.emptyContainersTva,\n                emptyContainersHt: reception.emptyContainersTtc - reception.emptyContainersTva,\n                demurrageHt: reception.demurrageHt,\n                miscExpensesTtc: reception.miscExpensesTtc,\n                miscExpensesTva: reception.miscExpensesTva,\n                miscExpensesHt: reception.miscExpensesTtc - reception.miscExpensesTva,\n                transitServicesTtc: reception.transitServicesTtc,\n                transitServicesTva: reception.transitServicesTva,\n                transitServicesHt: reception.transitServicesTtc - reception.transitServicesTva\n            };\n            setReceptionData(mappedReception);\n        } catch (error) {\n            console.error(\"Error searching reception:\", error);\n            setError(\"Erreur lors de la recherche de la r\\xe9ception\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateCosts = async ()=>{\n        if (!receptionData) return;\n        setIsGenerating(true);\n        try {\n            // Calculer les coûts\n            const cifDzd = (receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate;\n            const fobDzd = receptionData.fobAmount * receptionData.exchangeRate;\n            const totalCustomsDuties = receptionData.customsDuties1Ht + receptionData.customsDuties2Ht;\n            const totalPortFees = receptionData.importDeliveryHt + receptionData.customsInspectionHt;\n            const totalShippingFees = receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt;\n            const totalMiscExpenses = receptionData.miscExpensesHt;\n            const totalTransitServices = receptionData.transitServicesHt;\n            const landedCostHt = cifDzd + totalCustomsDuties + totalPortFees + totalShippingFees + totalMiscExpenses + totalTransitServices;\n            // Calcul du coefficient de landed cost = LANDED COST HT / FOB (DZD)\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Pour TTC, on ajoute les TVA\n            const totalTva = receptionData.customsDuties1Tva + receptionData.importDeliveryTva + receptionData.customsInspectionTva + receptionData.shippingAgencyTva + receptionData.emptyContainersTva + receptionData.miscExpensesTva + receptionData.transitServicesTva;\n            const landedCostTtc = landedCostHt + totalTva;\n            const costs = {\n                id: Date.now().toString(),\n                invoiceNumber: receptionData.invoiceNumber,\n                receptionId: receptionData.id,\n                landedCostTtc,\n                landedCostHt,\n                landedCostCoefficient,\n                totalCustomsDuties,\n                totalPortFees,\n                totalShippingFees,\n                totalMiscExpenses,\n                totalTransitServices,\n                cifDzd,\n                fobDzd,\n                exchangeRateUsed: receptionData.exchangeRate,\n                generatedAt: new Date().toISOString()\n            };\n            setGeneratedCosts(costs);\n        } catch (error) {\n            console.error(\"Error generating costs:\", error);\n            setError(\"Erreur lors de la g\\xe9n\\xe9ration des co\\xfbts\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const saveCosts = async ()=>{\n        if (!generatedCosts) return;\n        try {\n            // Sauvegarder dans la table des coûts générés (localStorage pour l'instant)\n            const savedCosts = JSON.parse(localStorage.getItem(\"generated-costs\") || \"[]\");\n            savedCosts.push(generatedCosts);\n            localStorage.setItem(\"generated-costs\", JSON.stringify(savedCosts));\n            alert(\"Co\\xfbts sauvegard\\xe9s avec succ\\xe8s pour la facture: \".concat(generatedCosts.invoiceNumber));\n        } catch (error) {\n            console.error(\"Error saving costs:\", error);\n            alert(\"Erreur lors de la sauvegarde des co\\xfbts\");\n        }\n    };\n    const viewReceptionDetails = (reception)=>{\n        setReceptionData(reception);\n        setInvoiceNumber(reception.invoiceNumber);\n        setShowAllReceptions(false);\n        setError(\"\");\n    };\n    const editReception = (reception)=>{\n        // Rediriger vers la page de modification avec l'ID\n        router.push(\"/shipment-reception?edit=\".concat(reception.id));\n    };\n    const deleteReception = (reception)=>{\n        if (confirm('\\xcates-vous s\\xfbr de vouloir supprimer la r\\xe9ception \"'.concat(reception.supplierName, \" - \").concat(reception.orderNumber, '\" ?'))) {\n            try {\n                const savedReceptions = JSON.parse(localStorage.getItem(\"shipment-receptions\") || \"[]\");\n                const updatedReceptions = savedReceptions.filter((r)=>r.id !== reception.id);\n                localStorage.setItem(\"shipment-receptions\", JSON.stringify(updatedReceptions));\n                // Recharger la liste\n                loadAllReceptions();\n                // Si c'était la réception actuellement affichée, la réinitialiser\n                if ((receptionData === null || receptionData === void 0 ? void 0 : receptionData.id) === reception.id) {\n                    setReceptionData(null);\n                    setGeneratedCosts(null);\n                    setInvoiceNumber(\"\");\n                }\n                alert(\"R\\xe9ception supprim\\xe9e avec succ\\xe8s\");\n            } catch (error) {\n                console.error(\"Error deleting reception:\", error);\n                alert(\"Erreur lors de la suppression de la r\\xe9ception\");\n            }\n        }\n    };\n    const backToList = ()=>{\n        setShowAllReceptions(true);\n        setReceptionData(null);\n        setGeneratedCosts(null);\n        setInvoiceNumber(\"\");\n        setError(\"\");\n    };\n    const exportSingleReception = (reception)=>{\n        try {\n            // Calculer les totaux pour cette réception\n            const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n            const fobDzd = reception.fobAmount * reception.exchangeRate;\n            const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n            const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n            const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n            const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n            const landedCostHt = cifDzd + totalAllFees;\n            const landedCostCoefficient = fobDzd > 0 ? landedCostHt / fobDzd : 0;\n            // Créer les données pour Excel (format CSV compatible)\n            const excelData = [\n                // En-têtes\n                [\n                    \"SHIPMENT RECEPTION DETAILED REPORT\"\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GENERAL INFORMATION\"\n                ],\n                [\n                    \"Supplier Name\",\n                    reception.supplierName\n                ],\n                [\n                    \"Order Number\",\n                    reception.orderNumber\n                ],\n                [\n                    \"Invoice Number\",\n                    reception.invoiceNumber\n                ],\n                [\n                    \"Currency\",\n                    reception.currency\n                ],\n                [\n                    \"Exchange Rate\",\n                    reception.exchangeRate.toFixed(5)\n                ],\n                [\n                    \"Created At\",\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"GOODS PRICE\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (\" + reception.currency + \")\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"FOB Amount\",\n                    reception.fobAmount.toFixed(2),\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"Freight\",\n                    reception.freight.toFixed(2),\n                    (reception.freight * reception.exchangeRate).toFixed(2)\n                ],\n                [\n                    \"CIF Amount\",\n                    (reception.fobAmount + reception.freight).toFixed(2),\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"CUSTOMS DUTIES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Customs Duties 1\",\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2)\n                ],\n                [\n                    \"Customs Duties 2\",\n                    \"\",\n                    \"\",\n                    reception.customsDuties2Ht.toFixed(2)\n                ],\n                [\n                    \"TOTAL CUSTOMS DUTIES\",\n                    \"\",\n                    \"\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"PORT FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Import Delivery\",\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2)\n                ],\n                [\n                    \"Customs Inspection\",\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL PORT FEES\",\n                    \"\",\n                    \"\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"SHIPPING COMPANY FEES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Shipping Agency\",\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2)\n                ],\n                [\n                    \"Empty Containers\",\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2)\n                ],\n                [\n                    \"Demurrage\",\n                    \"\",\n                    \"\",\n                    reception.demurrageHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL SHIPPING FEES\",\n                    \"\",\n                    \"\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"MISCELLANEOUS EXPENSES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Misc Expenses\",\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"TRANSIT SERVICES\"\n                ],\n                [\n                    \"Description\",\n                    \"TTC (DZD)\",\n                    \"TVA (DZD)\",\n                    \"HT (DZD)\"\n                ],\n                [\n                    \"Transit Services\",\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"COST BREAKDOWN SUMMARY\"\n                ],\n                [\n                    \"Description\",\n                    \"Amount (DZD)\"\n                ],\n                [\n                    \"CIF Amount\",\n                    cifDzd.toFixed(2)\n                ],\n                [\n                    \"Customs Duties HT\",\n                    totalCustomsDuties.toFixed(2)\n                ],\n                [\n                    \"Port Fees HT\",\n                    totalPortFees.toFixed(2)\n                ],\n                [\n                    \"Shipping Fees HT\",\n                    totalShippingFees.toFixed(2)\n                ],\n                [\n                    \"Misc Expenses HT\",\n                    reception.miscExpensesHt.toFixed(2)\n                ],\n                [\n                    \"Transit Services HT\",\n                    reception.transitServicesHt.toFixed(2)\n                ],\n                [\n                    \"TOTAL ALL FEES HT\",\n                    totalAllFees.toFixed(2)\n                ],\n                [\n                    \"\"\n                ],\n                [\n                    \"FINAL RESULTS\"\n                ],\n                [\n                    \"Description\",\n                    \"Value\"\n                ],\n                [\n                    \"FOB Amount (DZD)\",\n                    fobDzd.toFixed(2)\n                ],\n                [\n                    \"LANDED COST HT (DZD)\",\n                    landedCostHt.toFixed(2)\n                ],\n                [\n                    \"Coefficient of Landed Cost\",\n                    landedCostCoefficient.toFixed(4)\n                ],\n                [\n                    \"Formula\",\n                    \"Landed Cost (DZD) / FOB (DZD)\"\n                ],\n                [\n                    \"Calculation\",\n                    landedCostHt.toFixed(2) + \" / \" + fobDzd.toFixed(2) + \" = \" + landedCostCoefficient.toFixed(4)\n                ]\n            ];\n            // Convertir en CSV pour Excel\n            const csvContent = excelData.map((row)=>row.map((cell)=>typeof cell === \"string\" && (cell.includes(\",\") || cell.includes('\"')) ? '\"'.concat(cell.replace(/\"/g, '\"\"'), '\"') : cell).join(\",\")).join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"text/csv;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"reception_\".concat(reception.supplierName, \"_\").concat(reception.orderNumber, \"_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi pour la r\\xe9ception: \".concat(reception.supplierName, \" - \").concat(reception.orderNumber));\n        } catch (error) {\n            console.error(\"Error exporting single reception:\", error);\n            alert(\"Erreur lors de l'export de la r\\xe9ception\");\n        }\n    };\n    const exportAllReceptions = ()=>{\n        if (allReceptions.length === 0) {\n            alert(\"Aucune r\\xe9ception \\xe0 exporter\");\n            return;\n        }\n        try {\n            // Créer les en-têtes du fichier Excel\n            const headers = [\n                \"ID\",\n                \"Supplier Name\",\n                \"Order Number\",\n                \"Invoice Number\",\n                \"Currency\",\n                \"Exchange Rate\",\n                \"FOB Amount\",\n                \"Freight\",\n                \"CIF Amount (DZD)\",\n                \"Customs Duties 1 TTC\",\n                \"Customs Duties 1 TVA\",\n                \"Customs Duties 1 HT\",\n                \"Customs Duties 2 HT\",\n                \"Total Customs Duties HT\",\n                \"Import Delivery TTC\",\n                \"Import Delivery TVA\",\n                \"Import Delivery HT\",\n                \"Customs Inspection TTC\",\n                \"Customs Inspection TVA\",\n                \"Customs Inspection HT\",\n                \"Total Port Fees HT\",\n                \"Shipping Agency TTC\",\n                \"Shipping Agency TVA\",\n                \"Shipping Agency HT\",\n                \"Empty Containers TTC\",\n                \"Empty Containers TVA\",\n                \"Empty Containers HT\",\n                \"Demurrage HT\",\n                \"Total Shipping Fees HT\",\n                \"Misc Expenses TTC\",\n                \"Misc Expenses TVA\",\n                \"Misc Expenses HT\",\n                \"Transit Services TTC\",\n                \"Transit Services TVA\",\n                \"Transit Services HT\",\n                \"Total All Fees HT\",\n                \"Estimated Landed Cost HT\",\n                \"Created At\"\n            ];\n            // Créer les données pour chaque réception\n            const csvData = allReceptions.map((reception)=>{\n                const cifDzd = (reception.fobAmount + reception.freight) * reception.exchangeRate;\n                const totalCustomsDuties = reception.customsDuties1Ht + reception.customsDuties2Ht;\n                const totalPortFees = reception.importDeliveryHt + reception.customsInspectionHt;\n                const totalShippingFees = reception.shippingAgencyHt + reception.emptyContainersHt + reception.demurrageHt;\n                const totalAllFees = totalCustomsDuties + totalPortFees + totalShippingFees + reception.miscExpensesHt + reception.transitServicesHt;\n                const estimatedLandedCost = cifDzd + totalAllFees;\n                return [\n                    reception.id,\n                    reception.supplierName,\n                    reception.orderNumber,\n                    reception.invoiceNumber,\n                    reception.currency,\n                    reception.exchangeRate.toFixed(5),\n                    reception.fobAmount.toFixed(2),\n                    reception.freight.toFixed(2),\n                    cifDzd.toFixed(2),\n                    reception.customsDuties1Ttc.toFixed(2),\n                    reception.customsDuties1Tva.toFixed(2),\n                    reception.customsDuties1Ht.toFixed(2),\n                    reception.customsDuties2Ht.toFixed(2),\n                    totalCustomsDuties.toFixed(2),\n                    reception.importDeliveryTtc.toFixed(2),\n                    reception.importDeliveryTva.toFixed(2),\n                    reception.importDeliveryHt.toFixed(2),\n                    reception.customsInspectionTtc.toFixed(2),\n                    reception.customsInspectionTva.toFixed(2),\n                    reception.customsInspectionHt.toFixed(2),\n                    totalPortFees.toFixed(2),\n                    reception.shippingAgencyTtc.toFixed(2),\n                    reception.shippingAgencyTva.toFixed(2),\n                    reception.shippingAgencyHt.toFixed(2),\n                    reception.emptyContainersTtc.toFixed(2),\n                    reception.emptyContainersTva.toFixed(2),\n                    reception.emptyContainersHt.toFixed(2),\n                    reception.demurrageHt.toFixed(2),\n                    totalShippingFees.toFixed(2),\n                    reception.miscExpensesTtc.toFixed(2),\n                    reception.miscExpensesTva.toFixed(2),\n                    reception.miscExpensesHt.toFixed(2),\n                    reception.transitServicesTtc.toFixed(2),\n                    reception.transitServicesTva.toFixed(2),\n                    reception.transitServicesHt.toFixed(2),\n                    totalAllFees.toFixed(2),\n                    estimatedLandedCost.toFixed(2),\n                    new Date(reception.createdAt || Date.now()).toLocaleDateString(\"fr-FR\")\n                ];\n            });\n            // Créer le contenu CSV\n            const csvContent = [\n                headers.join(\",\"),\n                ...csvData.map((row)=>row.map((cell)=>typeof cell === \"string\" && cell.includes(\",\") ? '\"'.concat(cell, '\"') : cell).join(\",\"))\n            ].join(\"\\n\");\n            // Créer et télécharger le fichier Excel\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8;\"\n            });\n            const link = document.createElement(\"a\");\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", \"shipment_receptions_export_\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert(\"Export Excel r\\xe9ussi ! \".concat(allReceptions.length, \" r\\xe9ceptions export\\xe9es.\"));\n        } catch (error) {\n            console.error(\"Error exporting receptions:\", error);\n            alert(\"Erreur lors de l'export des r\\xe9ceptions\");\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                lineNumber: 580,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Cost Calculator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Generate costs from saved shipment receptions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this),\n                showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"All Shipment Receptions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: exportAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Export\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: loadAllReceptions,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.RefreshCw, {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Refresh\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Complete list of all recorded shipment receptions with actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: allReceptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FileText, {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No shipment receptions found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Create a new reception in Shipment Reception page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full border-collapse border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Supplier Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Invoice Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-left\",\n                                                        children: \"Currency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                        children: \"Exchange Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"border border-gray-300 px-4 py-2 text-center\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: allReceptions.map((reception)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-medium\",\n                                                            children: reception.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 font-mono text-sm\",\n                                                            children: reception.invoiceNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: reception.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.fobAmount.toFixed(2)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2 text-right\",\n                                                            children: reception.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-gray-300 px-4 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>viewReceptionDetails(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"View Details\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>editReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        title: \"Edit Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>exportSingleReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                        title: \"Export Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Download, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        onClick: ()=>deleteReception(reception),\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                        title: \"Delete Reception\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, reception.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 11\n                }, this),\n                !showAllReceptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Search Reception by Invoice Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: backToList,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"← Back to List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Enter the invoice number to retrieve reception data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                value: invoiceNumber,\n                                                onChange: (e)=>setInvoiceNumber(e.target.value),\n                                                placeholder: \"Enter invoice number...\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && searchReception()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: searchReception,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Search, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Searching...\" : \"Search\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 11\n                }, this),\n                receptionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reception Found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Reception data for invoice \",\n                                                receptionData.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Supplier:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.supplierName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Order Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Currency:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Exchange Rate:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: receptionData.exchangeRate.toFixed(5)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: [\n                                                        \"Goods Price (\",\n                                                        receptionData.currency,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"FOB Amount: \",\n                                                                receptionData.fobAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Freight: \",\n                                                                receptionData.freight.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 font-medium\",\n                                                            children: [\n                                                                \"CIF DZD: \",\n                                                                ((receptionData.fobAmount + receptionData.freight) * receptionData.exchangeRate).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Fees Summary (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.customsDuties1Ht + receptionData.customsDuties2Ht).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.importDeliveryHt + receptionData.customsInspectionHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (receptionData.shippingAgencyHt + receptionData.emptyContainersHt + receptionData.demurrageHt).toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.miscExpensesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: receptionData.transitServicesHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: generateCosts,\n                                                disabled: isGenerating,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Calculator, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isGenerating ? \"Generating Costs...\" : \"Generate Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 13\n                        }, this),\n                        generatedCosts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generated Costs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: [\n                                                \"Calculated landed costs for invoice \",\n                                                generatedCosts.invoiceNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-green-900 mb-3\",\n                                                    children: \"LANDED COST RESULTS:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostHt.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost TTC:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        generatedCosts.landedCostTtc.toFixed(2),\n                                                                        \" DZD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold border-t pt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Landed Cost Coefficient:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: generatedCosts.landedCostCoefficient.toFixed(4)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: [\n                                                                \"Coefficient = Landed Cost HT / FOB (DZD) = \",\n                                                                generatedCosts.landedCostHt.toFixed(2),\n                                                                \" / \",\n                                                                generatedCosts.fobDzd.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"Cost Breakdown (DZD HT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between font-medium text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"FOB Amount (DZD):\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.fobDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CIF Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.cifDzd.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Customs Duties:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalCustomsDuties.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Port Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalPortFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Shipping Fees:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalShippingFees.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Misc Expenses:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalMiscExpenses.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Transit Services:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.totalTransitServices.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t pt-2 flex justify-between font-bold\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"TOTAL LANDED COST HT:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: generatedCosts.landedCostHt.toFixed(2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: saveCosts,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_CheckCircle_Download_Edit_Eye_FileText_RefreshCw_Save_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Save, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Save Generated Costs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                                    lineNumber: 848,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n                    lineNumber: 755,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n            lineNumber: 589,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\calculator.tsx\",\n        lineNumber: 588,\n        columnNumber: 5\n    }, this);\n}\n_s(Calculator, \"CITHuXboUsUYvZZyHh6Xlw/KVr8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Calculator;\nvar _c;\n$RefreshReg$(_c, \"Calculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/calculator.tsx\n"));

/***/ })

});