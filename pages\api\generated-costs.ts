import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth/next'
import { authOptions } from './auth/[...nextauth]'
import { generatedCosts } from '@/lib/database'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions)

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  try {
    switch (req.method) {
      case 'GET':
        // Récupérer tous les coûts générés ou un coût spécifique
        if (req.query.invoiceNumber) {
          const costs = generatedCosts.getByInvoiceNumber.get(req.query.invoiceNumber as string)
          if (!costs) {
            return res.status(404).json({ error: 'Generated costs not found' })
          }
          return res.status(200).json(costs)
        } else {
          const allCosts = generatedCosts.getAll.all()
          return res.status(200).json(allCosts)
        }

      case 'POST':
        // Créer de nouveaux coûts générés
        const newCosts = req.body
        
        // Générer un ID unique
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
        
        const costsData = [
          id,
          newCosts.invoiceNumber,
          newCosts.receptionId,
          newCosts.landedCostTtc,
          newCosts.landedCostHt,
          newCosts.landedCostCoefficient,
          newCosts.totalCustomsDuties,
          newCosts.totalPortFees,
          newCosts.totalShippingFees,
          newCosts.totalMiscExpenses,
          newCosts.totalTransitServices,
          newCosts.cifDzd,
          newCosts.fobDzd,
          newCosts.exchangeRateUsed,
          new Date().toISOString()
        ]

        const result = generatedCosts.create.run(...costsData)
        
        return res.status(201).json({ 
          id, 
          message: 'Generated costs saved successfully',
          insertId: result.lastInsertRowid 
        })

      case 'DELETE':
        // Supprimer des coûts générés
        if (!req.query.id) {
          return res.status(400).json({ error: 'Costs ID is required' })
        }

        const deleteResult = generatedCosts.delete.run(req.query.id as string)
        
        if (deleteResult.changes === 0) {
          return res.status(404).json({ error: 'Generated costs not found' })
        }

        return res.status(200).json({ message: 'Generated costs deleted successfully' })

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE'])
        return res.status(405).json({ error: `Method ${req.method} not allowed` })
    }
  } catch (error) {
    console.error('Database error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
