"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.tsx":
/*!***************************!*\
  !*** ./pages/reports.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,DollarSign,Download,FileText,Package,PieChart,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!./node_modules/recharts/es6/index.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! xlsx */ \"./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst mockData = {\n    monthlyShipments: [\n        {\n            month: \"Jan\",\n            shipments: 12,\n            value: 1200000\n        },\n        {\n            month: \"Feb\",\n            shipments: 8,\n            value: 850000\n        },\n        {\n            month: \"Mar\",\n            shipments: 15,\n            value: 1650000\n        },\n        {\n            month: \"Apr\",\n            shipments: 10,\n            value: 1100000\n        },\n        {\n            month: \"May\",\n            shipments: 18,\n            value: 2100000\n        },\n        {\n            month: \"Jun\",\n            shipments: 14,\n            value: 1800000\n        }\n    ],\n    costBreakdown: [\n        {\n            name: \"FOB Amount\",\n            value: 65,\n            amount: 6500000\n        },\n        {\n            name: \"Customs Duties\",\n            value: 15,\n            amount: 1500000\n        },\n        {\n            name: \"Port Fees\",\n            value: 8,\n            amount: 800000\n        },\n        {\n            name: \"Shipping Fees\",\n            value: 7,\n            amount: 700000\n        },\n        {\n            name: \"Other Costs\",\n            value: 5,\n            amount: 500000\n        }\n    ],\n    recentShipments: [\n        {\n            id: \"1\",\n            orderNumber: \"ORD-2024-001\",\n            supplier: \"ABC Motors Ltd\",\n            date: \"2024-01-15\",\n            fobAmount: 125000,\n            landedCost: 168750,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"2\",\n            orderNumber: \"ORD-2024-002\",\n            supplier: \"XYZ Parts Co\",\n            date: \"2024-01-12\",\n            fobAmount: 89000,\n            landedCost: 120150,\n            coefficient: 1.35,\n            status: \"Completed\"\n        },\n        {\n            id: \"3\",\n            orderNumber: \"ORD-2024-003\",\n            supplier: \"Global Supply Inc\",\n            date: \"2024-01-10\",\n            fobAmount: 156000,\n            landedCost: 210600,\n            coefficient: 1.35,\n            status: \"In Progress\"\n        }\n    ]\n};\nconst COLORS = [\n    \"#3b82f6\",\n    \"#ef4444\",\n    \"#f59e0b\",\n    \"#10b981\",\n    \"#8b5cf6\"\n];\nfunction Reports() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        from: \"2024-01-01\",\n        to: \"2024-06-30\"\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"fr-FR\", {\n            style: \"currency\",\n            currency: \"DZD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatUSD = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const handleExportReport = (reportType)=>{\n        try {\n            const fileName = \"\".concat(reportType, \"_Report_\").concat(new Date().toISOString().split(\"T\")[0], \".xlsx\");\n            let workbook;\n            if (reportType === \"Comprehensive\") {\n                workbook = generateComprehensiveReport();\n            } else if (reportType === \"Cost_Analysis\") {\n                workbook = generateCostAnalysisReport();\n            } else if (reportType === \"Monthly_Summary\") {\n                workbook = generateMonthlySummaryReport();\n            } else if (reportType.startsWith(\"Shipment_\")) {\n                const orderNumber = reportType.replace(\"Shipment_\", \"\");\n                workbook = generateShipmentReport(orderNumber);\n            } else {\n                workbook = generateComprehensiveReport();\n            }\n            // Generate and download the file\n            xlsx__WEBPACK_IMPORTED_MODULE_9__.writeFile(workbook, fileName);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Rapport \"'.concat(fileName, '\" g\\xe9n\\xe9r\\xe9 et t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s!'));\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"Erreur lors de la g\\xe9n\\xe9ration du rapport\");\n        }\n    };\n    const generateComprehensiveReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Summary sheet\n        const summaryData = [\n            [\n                \"RAPPORT COMPLET - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9SUM\\xc9 EX\\xc9CUTIF\"\n            ],\n            [\n                \"Total des exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Valeur totale (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ],\n            [\n                \"Exp\\xe9ditions par mois:\",\n                Math.round(totalShipments / 6)\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION DES CO\\xdbTS\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\") + \" DZD\"\n                ])\n        ];\n        const summaryWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(summaryData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, summaryWs, \"R\\xe9sum\\xe9\");\n        // Shipments detail sheet\n        const shipmentsData = [\n            [\n                \"N\\xb0 Commande\",\n                \"Fournisseur\",\n                \"Date\",\n                \"Montant FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Statut\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.supplier,\n                    s.date,\n                    s.fobAmount,\n                    s.landedCost,\n                    s.coefficient.toFixed(3),\n                    s.status\n                ])\n        ];\n        const shipmentsWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentsData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, shipmentsWs, \"D\\xe9tail Exp\\xe9ditions\");\n        // Monthly data sheet\n        const monthlyData = [\n            [\n                \"Mois\",\n                \"Nombre d'exp\\xe9ditions\",\n                \"Valeur (DZD)\"\n            ],\n            ...mockData.monthlyShipments.map((m)=>[\n                    m.month,\n                    m.shipments,\n                    m.value\n                ])\n        ];\n        const monthlyWs = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, monthlyWs, \"Donn\\xe9es Mensuelles\");\n        return wb;\n    };\n    const generateCostAnalysisReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const costData = [\n            [\n                \"ANALYSE DES CO\\xdbTS - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"R\\xc9PARTITION D\\xc9TAILL\\xc9E DES CO\\xdbTS\"\n            ],\n            [\n                \"Composant\",\n                \"Pourcentage\",\n                \"Montant (DZD)\",\n                \"Description\"\n            ],\n            ...mockData.costBreakdown.map((item)=>[\n                    item.name,\n                    \"\".concat(item.value, \"%\"),\n                    item.amount.toLocaleString(\"fr-FR\"),\n                    getComponentDescription(item.name)\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"ANALYSE PAR EXP\\xc9DITION\"\n            ],\n            [\n                \"N\\xb0 Commande\",\n                \"FOB (USD)\",\n                \"Co\\xfbt D\\xe9barqu\\xe9 (DZD)\",\n                \"Coefficient\",\n                \"Marge vs FOB\"\n            ],\n            ...mockData.recentShipments.map((s)=>[\n                    s.orderNumber,\n                    s.fobAmount.toLocaleString(\"en-US\"),\n                    s.landedCost.toLocaleString(\"fr-FR\"),\n                    s.coefficient.toFixed(3),\n                    \"\".concat(((s.coefficient - 1) * 100).toFixed(1), \"%\")\n                ])\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(costData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Analyse Co\\xfbts\");\n        return wb;\n    };\n    const generateMonthlySummaryReport = ()=>{\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        const monthlyData = [\n            [\n                \"R\\xc9SUM\\xc9 MENSUEL - IMPORT & LOGISTICS CALCULATOR\"\n            ],\n            [\n                \"P\\xe9riode:\",\n                \"\".concat(dateRange.from, \" \\xe0 \").concat(dateRange.to)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PERFORMANCE MENSUELLE\"\n            ],\n            [\n                \"Mois\",\n                \"Exp\\xe9ditions\",\n                \"Valeur (DZD)\",\n                \"Valeur Moyenne\",\n                \"\\xc9volution\"\n            ],\n            ...mockData.monthlyShipments.map((m, index)=>[\n                    m.month,\n                    m.shipments,\n                    m.value.toLocaleString(\"fr-FR\"),\n                    Math.round(m.value / m.shipments).toLocaleString(\"fr-FR\"),\n                    index > 0 ? \"\".concat(((m.value - mockData.monthlyShipments[index - 1].value) / mockData.monthlyShipments[index - 1].value * 100).toFixed(1), \"%\") : \"N/A\"\n                ]),\n            [\n                \"\"\n            ],\n            [\n                \"TOTAUX\"\n            ],\n            [\n                \"Total exp\\xe9ditions:\",\n                totalShipments\n            ],\n            [\n                \"Total valeur (DZD):\",\n                totalValue.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Moyenne mensuelle:\",\n                Math.round(totalValue / 6).toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Coefficient moyen:\",\n                avgCoefficient.toFixed(3)\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(monthlyData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"R\\xe9sum\\xe9 Mensuel\");\n        return wb;\n    };\n    const generateShipmentReport = (orderNumber)=>{\n        const shipment = mockData.recentShipments.find((s)=>s.orderNumber === orderNumber);\n        if (!shipment) {\n            throw new Error(\"Exp\\xe9dition non trouv\\xe9e\");\n        }\n        const wb = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_new();\n        // Calculs détaillés basés sur les formules de l'application\n        const exchangeRate = 134.50000 // Taux de change USD vers DZD\n        ;\n        const fobAmountDzd = shipment.fobAmount * exchangeRate;\n        const freightDzd = shipment.fobAmount * 0.15 * exchangeRate // Estimation 15% du FOB\n        ;\n        const totalCifDzd = fobAmountDzd + freightDzd;\n        // Frais portuaires (estimations basées sur les données réelles)\n        const importDeliveryTtc = totalCifDzd * 0.025 // 2.5% du CIF\n        ;\n        const importDeliveryTva = importDeliveryTtc * 0.19 / 1.19 // TVA 19%\n        ;\n        const importDeliveryHt = importDeliveryTtc - importDeliveryTva;\n        const customsInspectionTtc = totalCifDzd * 0.015 // 1.5% du CIF\n        ;\n        const customsInspectionTva = customsInspectionTtc * 0.19 / 1.19;\n        const customsInspectionHt = customsInspectionTtc - customsInspectionTva;\n        const portFeesTotalTtc = importDeliveryTtc + customsInspectionTtc;\n        const portFeesTotalHt = importDeliveryHt + customsInspectionHt;\n        // Frais de transport maritime\n        const shippingAgencyTtc = totalCifDzd * 0.035 // 3.5% du CIF\n        ;\n        const shippingAgencyTva = shippingAgencyTtc * 0.19 / 1.19;\n        const shippingAgencyHt = shippingAgencyTtc - shippingAgencyTva;\n        const emptyContainersTtc = totalCifDzd * 0.02 // 2% du CIF\n        ;\n        const emptyContainersTva = emptyContainersTtc * 0.19 / 1.19;\n        const emptyContainersHt = emptyContainersTtc - emptyContainersTva;\n        const demurrageHt = totalCifDzd * 0.01 // 1% du CIF (HT)\n        ;\n        const shippingFeesTotalTtc = shippingAgencyTtc + emptyContainersTtc + demurrageHt;\n        const shippingFeesTotalHt = shippingAgencyHt + emptyContainersHt + demurrageHt;\n        // Autres frais\n        const miscExpensesTtc = totalCifDzd * 0.025 // 2.5% du CIF\n        ;\n        const miscExpensesTva = miscExpensesTtc * 0.19 / 1.19;\n        const miscExpensesHt = miscExpensesTtc - miscExpensesTva;\n        const transitExpensesTtc = totalCifDzd * 0.02 // 2% du CIF\n        ;\n        const transitExpensesTva = transitExpensesTtc * 0.19 / 1.19;\n        const transitExpensesHt = transitExpensesTtc - transitExpensesTva;\n        // Calculs des totaux\n        const landedCostTtc = totalCifDzd + portFeesTotalTtc + shippingFeesTotalTtc + miscExpensesTtc + transitExpensesTtc;\n        const landedCostHt = totalCifDzd + portFeesTotalHt + shippingFeesTotalHt + miscExpensesHt + transitExpensesHt;\n        const shipmentData = [\n            [\n                \"RAPPORT D'EXP\\xc9DITION - \".concat(orderNumber)\n            ],\n            [\n                \"Date de g\\xe9n\\xe9ration:\",\n                new Date().toLocaleDateString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"INFORMATIONS G\\xc9N\\xc9RALES\"\n            ],\n            [\n                \"N\\xb0 Commande:\",\n                shipment.orderNumber\n            ],\n            [\n                \"Fournisseur:\",\n                shipment.supplier\n            ],\n            [\n                \"Date:\",\n                shipment.date\n            ],\n            [\n                \"Statut:\",\n                shipment.status\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"D\\xc9TAIL FINANCIER\"\n            ],\n            [\n                \"Montant FOB (USD):\",\n                shipment.fobAmount.toLocaleString(\"en-US\")\n            ],\n            [\n                \"Taux de change (USD/DZD):\",\n                exchangeRate.toFixed(5)\n            ],\n            [\n                \"Montant FOB (DZD):\",\n                fobAmountDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"Fret (DZD):\",\n                freightDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"TOTAL AMOUNT CIF DZD (FOB + Freight):\",\n                totalCifDzd.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CO\\xdbTS D\\xc9TAILL\\xc9S\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Livraison Import:\"\n            ],\n            [\n                \"  TTC:\",\n                importDeliveryTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                importDeliveryTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                importDeliveryHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"PORT FEES - Inspection Douani\\xe8re:\"\n            ],\n            [\n                \"  TTC:\",\n                customsInspectionTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                customsInspectionTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                customsInspectionHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'PORT FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                portFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Agence Maritime:\"\n            ],\n            [\n                \"  TTC:\",\n                shippingAgencyTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                shippingAgencyTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                shippingAgencyHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Conteneurs Vides:\"\n            ],\n            [\n                \"  TTC:\",\n                emptyContainersTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                emptyContainersTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                emptyContainersHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"SHIPPING COMPANY FEES - Surestaries:\"\n            ],\n            [\n                \"  HT:\",\n                demurrageHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                'SHIPPING COMPANY FEES \"Overall Totals\":'\n            ],\n            [\n                \"  TTC:\",\n                shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"OTHER MISCELLANEOUS EXPENSES:\"\n            ],\n            [\n                \"  TTC:\",\n                miscExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                miscExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                miscExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"TRANSIT SERVICES EXPENSES:\"\n            ],\n            [\n                \"  TTC:\",\n                transitExpensesTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  TVA:\",\n                transitExpensesTva.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  HT:\",\n                transitExpensesHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"CALCULS FINAUX\"\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost TTC:\",\n                landedCostTtc.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees TTC + Shipping Fees TTC + Misc Expenses TTC + Transit Expenses TTC\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalTtc.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesTtc.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesTtc.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Landed Cost HT:\",\n                landedCostHt.toLocaleString(\"fr-FR\") + \" DZD\"\n            ],\n            [\n                \"  = CIF + Port Fees HT + Shipping Fees HT + Misc Expenses HT + Transit Expenses HT\"\n            ],\n            [\n                \"  = \" + totalCifDzd.toLocaleString(\"fr-FR\") + \" + \" + portFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + shippingFeesTotalHt.toLocaleString(\"fr-FR\") + \" + \" + miscExpensesHt.toLocaleString(\"fr-FR\") + \" + \" + transitExpensesHt.toLocaleString(\"fr-FR\")\n            ],\n            [\n                \"\"\n            ],\n            [\n                \"Coefficient TTC:\",\n                (landedCostTtc / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Coefficient HT:\",\n                (landedCostHt / fobAmountDzd).toFixed(3)\n            ],\n            [\n                \"Marge vs FOB (TTC):\",\n                \"\".concat(((landedCostTtc / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ],\n            [\n                \"Marge vs FOB (HT):\",\n                \"\".concat(((landedCostHt / fobAmountDzd - 1) * 100).toFixed(1), \"%\")\n            ]\n        ];\n        const ws = xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.aoa_to_sheet(shipmentData);\n        xlsx__WEBPACK_IMPORTED_MODULE_9__.utils.book_append_sheet(wb, ws, \"Exp\\xe9dition \".concat(orderNumber));\n        return wb;\n    };\n    const getComponentDescription = (component)=>{\n        const descriptions = {\n            \"FOB Amount\": \"Valeur Free On Board - Prix marchandise au port de d\\xe9part\",\n            \"Customs Duties\": \"Droits de douane et taxes d'importation\",\n            \"Port Fees\": \"Frais portuaires et de manutention\",\n            \"Shipping Fees\": \"Frais de transport maritime et terrestre\",\n            \"Other Costs\": \"Frais divers (assurance, transit, etc.)\"\n        };\n        return descriptions[component] || \"Description non disponible\";\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                lineNumber: 398,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 397,\n            columnNumber: 7\n        }, this);\n    }\n    const totalShipments = mockData.monthlyShipments.reduce((sum, item)=>sum + item.shipments, 0);\n    const totalValue = mockData.monthlyShipments.reduce((sum, item)=>sum + item.value, 0);\n    const avgCoefficient = 1.35;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Reports & Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive insights into your import costs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.from,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    from: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                        type: \"date\",\n                                        value: dateRange.to,\n                                        onChange: (e)=>setDateRange((prev)=>({\n                                                    ...prev,\n                                                    to: e.target.value\n                                                })),\n                                        className: \"w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Shipments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Package, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: totalShipments\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+12% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"+8% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Coefficient\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                avgCoefficient.toFixed(2),\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"-2% from last period\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Avg Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: Math.round(totalShipments / 6)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"shipments per month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Monthly Shipments\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Shipment volume and value trends\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.BarChart, {\n                                            data: mockData.monthlyShipments,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"shipments\" ? value : formatCurrency(value),\n                                                            name === \"shipments\" ? \"Shipments\" : \"Value (DZD)\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                                    dataKey: \"shipments\",\n                                                    fill: \"#3b82f6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cost Breakdown\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Distribution of import costs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 300,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        formatter: (value, name)=>[\n                                                                \"\".concat(value, \"%\"),\n                                                                name\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                                                        data: mockData.costBreakdown,\n                                                        children: mockData.costBreakdown.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Cell, {\n                                                                fill: COLORS[index % COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 space-y-2\",\n                                            children: mockData.costBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                                    style: {\n                                                                        backgroundColor: COLORS[index % COLORS.length]\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Generate Reports\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Export detailed reports for analysis and record keeping\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Comprehensive\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Comprehensive Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"All shipments & costs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Cost_Analysis\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.BarChart3, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cost Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Detailed cost breakdown\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        className: \"h-20 flex-col\",\n                                        onClick: ()=>handleExportReport(\"Monthly_Summary\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                className: \"h-6 w-6 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Monthly Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Period-based analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Shipments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Latest import calculations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Order Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"FOB Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Landed Cost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4\",\n                                                        children: \"Coefficient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockData.recentShipments.map((shipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 font-medium\",\n                                                            children: shipment.orderNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: shipment.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: new Date(shipment.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatUSD(shipment.fobAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: formatCurrency(shipment.landedCost)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right\",\n                                                            children: [\n                                                                shipment.coefficient.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(shipment.status === \"Completed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                children: shipment.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportReport(\"Shipment_\".concat(shipment.orderNumber)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_DollarSign_Download_FileText_Package_PieChart_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Download, {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Export\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, shipment.id, true, {\n                                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n                    lineNumber: 611,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n            lineNumber: 411,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\adam2\\\\pages\\\\reports.tsx\",\n        lineNumber: 410,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"V9rHqNTl7h2x7X2rKtHE3y76I5M=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.tsx\n"));

/***/ })

});